#!/bin/bash

kernel_name=$(uname -s)

check_command() {
  command -v "$1" &>/dev/null
}

if [ "$kernel_name" = "Darwin" ]; then
  echo "系统内核是 Darwin (macOS)"

  # volta 环境初始化
  if ! check_command volta; then
    curl https://get.volta.sh | bash

    # 根据终端类型，写入特殊环境变量（volta 的 pnpm 目前现处于实验性）
    VOLTA_ENV="VOLTA_FEATURE_PNPM=1"
    if [[ $0 == *bash* ]]; then
      echo "export $VOLTA_ENV" >> ~/.bashrc
    elif [[ $0 == *zsh* ]]; then
      echo "export $VOLTA_ENV" >> ~/.zshrc
    else
      echo "暂不支持的 Shell 类型: $0"
      exit 1
    fi
  fi

  # python 环境初始化
  if ! check_command python3; then
    brew install python
  fi

  # rust 环境初始化
  if ! check_command rustc; then
    curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
  fi

  # 环境清单输出
  echo "volta 版本: $(volta -v), python 版本: $(python -V | awk '{print $2}'), rust 版本: $(rustc -V | awk '{print $2}')"

elif [ "$kernel_name" = "Linux" ]; then
  echo "系统内核是 Linux, 暂无独立环境初始化场景, 均由容器化基础构建实现"
else
  echo "未知的系统内核: $kernel_name"
  exit 1
fi