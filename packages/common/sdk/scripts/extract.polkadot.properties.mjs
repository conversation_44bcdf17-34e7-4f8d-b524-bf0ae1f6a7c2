#!/usr/bin/env zx
/* global fs, path */
import _ from 'lodash'
import ss58Registry from '@substrate/ss58-registry'

const customRegistry = require('../src/properties/polkadot.custom.registry.json')
const customTypes = require('../src/properties/polkadot.custom.types.json')

// 从官方认定并已集成 registry 列表中，提取 tokenSymbol 不为空的所有项，进行格式转化
const official = ss58Registry.reduce((acc, { prefix: ss58Format, symbols: [tokenSymbol], decimals: [tokenDecimals] }) =>
  tokenSymbol ? { ...acc, [tokenSymbol]: { ss58Format, tokenDecimals, tokenSymbol } } : acc
, {})

const target = path.resolve(__dirname, '..', 'src', 'properties', 'polkadot.properties.js')
const result = _.merge(official, customRegistry, customTypes)

fs.writeFileSync(target, `export const CHAIN_PROPERTIES = ${JSON.stringify(result, null, 2)}`)
