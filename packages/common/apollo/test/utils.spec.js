import fs from 'fs'
import path from 'path'
import { testApolloOptions, configResponse } from './constants.js'
import { genAuthHeaders, format, readFile, decrypt } from '../src/utils.js'

describe('apollo客户端测试用例', () => {
  it('测试获取授权请求头', () => {
    const { host, appId, accessKey, releaseKey } = testApolloOptions
    const url = `${host}/configs/${appId}/cluster/application?releaseKey=${releaseKey}`
    const header = genAuthHeaders({ url, appId, accessKey })

    expect(header.Authorization).toMatch(/^Apollo test:/)
  })

  it('格式化apollo数据', () => {
    const { formatData } = format([configResponse])

    expect(formatData).toEqual({
      chains: {
        bnb1: {
          chain_id: 56,
          assets: { bnb1: { precision: 18, amount_limit: { enabled: false } } },
          is_case_sensitive: false,
          support_tx_type: ['transfer', 'contract'],
          support_refund: true
        }
      }
    })
  })

  it('读取文件', () => {
    const formatData = readFile(path.resolve(__dirname, 'test-config.json'))
    const filePath = path.resolve(__dirname, 'config.json')

    expect(JSON.parse(formatData)).toEqual({
      chains: {
        bnb1: {
          chain_id: 56,
          assets: { bnb1: { precision: 18, amount_limit: { enabled: false } } },
          is_case_sensitive: false,
          support_tx_type: ['transfer', 'contract'],
          support_refund: true
        }
      }
    })
    expect(() => { readFile(filePath) }).toThrow(`文件未找到, 文件路径为: ${filePath}. 请确认正确的文件路径.`)
  })

  it('解密数据', () => {
    const filePath = path.resolve(__dirname, 'encrypted-data.txt')

    expect(decrypt('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', fs.readFileSync(filePath))).toEqual({
      appId: 'test',
      cluster: 'default',
      namespaceName: 'chains',
      configurations: {
        cspr: '{"assets":{"cspr":{"precision":9}},"is_case_sensitive":false,"chain_id":"casper-test","support_tx_type":["transfer"]}'
      },
      releaseKey: '20250529102402-d02298492259ae69'
    })
  })
})
