import nock from 'nock'
import path from 'path'
import { configResponse, applicationDefaultUpdateResponse, testApolloOptions } from './constants.js'
import { wait } from '../src/utils.js'
import ApolloClient from '../src/index.js'

describe('apollo客户端测试用例', () => {
  beforeAll(() => {
    nock(testApolloOptions.host)
      .get(`/configs/${testApolloOptions.appId}/default/application`)
      .reply(200, configResponse)

    nock(testApolloOptions.host)
      .get(`/notifications/v2?appId=${testApolloOptions.appId}&cluster=default&notifications=%5B%7B%22namespaceName%22%3A%22application%22%2C%22notificationId%22%3A-1%7D%5D`)
      .reply(200, applicationDefaultUpdateResponse)

    nock(testApolloOptions.host)
      .get(`/notifications/v2?appId=${testApolloOptions.appId}&cluster=default&notifications=%5B%7B%22namespaceName%22%3A%22application%22%2C%22notificationId%22%3A101%7D%5D`)
      .reply(200, applicationDefaultUpdateResponse)
  })

  afterAll(() => {
    nock.restore()
  })

  it('加载远端配置信息', async () => {
    const client = new ApolloClient(testApolloOptions)
    const config = await client.load()

    expect(config).toEqual({
      chains: {
        bnb1: {
          chain_id: 56,
          assets: { bnb1: { precision: 18, amount_limit: { enabled: false } } },
          is_case_sensitive: false,
          support_tx_type: ['transfer', 'contract'],
          support_refund: true
        }
      }
    })
  })

  it('返回错误的配置信息', async () => {
    nock(testApolloOptions.host)
      .get('/configs/test/default/application')
      .reply(200, {
        appId: 'test',
        cluster: 'default',
        namespaceName: 'application',
        configurations: {
          '.bnb1': '{"chain_id":56,"assets":{"bnb1":{"precision":18,"amount_limit":{"enabled":false}}},"is_case_sensitive":false,"support_tx_type":["transfer","contract"],"support_refund":true}'
        },
        releaseKey: '20210706161521-b874e7ca43fa2664'
      })

    const client = new ApolloClient(testApolloOptions)

    client.on('error', error => {
      expect(error.message).toEqual('apollo存在无效键值. 键值格式应为: a或者a.b')
    })

    const config = await client.load()

    expect(config).toEqual({})

    await wait(50)
  })

  it('读取缓存配置信息配置信息', async () => {
    nock(testApolloOptions.host)
      .get('/configs/test/default/application')
      .reply(200, {
        appId: 'test',
        cluster: 'default',
        namespaceName: 'application',
        configurations: {
          '.bnb1': '{"chain_id":56,"assets":{"bnb1":{"precision":18,"amount_limit":{"enabled":false}}},"is_case_sensitive":false,"support_tx_type":["transfer","contract"],"support_refund":true}'
        },
        releaseKey: '20210706161521-b874e7ca43fa2664'
      })

    const client = new ApolloClient({
      ...testApolloOptions,
      cache: {
        enable: true,
        filePath: path.resolve(__dirname, 'test-config.json')
      }
    })

    client.on('error', error => {
      expect(error.message).toEqual('apollo存在无效键值. 键值格式应为: a或者a.b')
    })

    const config = await client.load()

    await wait(50)

    expect(config).toEqual({
      chains: {
        bnb1: {
          chain_id: 56,
          assets: { bnb1: { precision: 18, amount_limit: { enabled: false } } },
          is_case_sensitive: false,
          support_tx_type: ['transfer', 'contract'],
          support_refund: true
        }
      }
    })
  })

  it('开启热更新', async () => {
    const client = new ApolloClient(testApolloOptions)
    client.setHotUpdate(true)

    client.on('update', data => {
      expect(data).toEqual({
        name: 'update',
        detail: 'apollo have update.'
      })
    })

    client.polling()
    client.setHotUpdate(false)

    await wait(50)
  })
})
