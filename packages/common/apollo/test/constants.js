export const configResponse = {
  appId: 'test',
  namespaceName: 'application',
  configurations: {
    'chains.bnb1': '{"chain_id":56,"assets":{"bnb1":{"precision":18,"amount_limit":{"enabled":false}}},"is_case_sensitive":false,"support_tx_type":["transfer","contract"],"support_refund":true}'
  },
  releaseKey: '20210706161521-b874e7ca43fa2664'
}

export const applicationDefaultUpdateResponse = [
  {
    namespaceName: 'application',
    notificationId: 101
  }
]

export const testApolloOptions = {
  host: 'http://test-validator-1d-1.aws-jp1.huobiidc.com:8080',
  appId: 'test',
  accessKey: 'f56702ff357c40cfb7d2d0eba264120c',
  pollingIntervalsTime: 100,
  releaseKey: '20210706161521-b874e7ca43fa2664'
}
