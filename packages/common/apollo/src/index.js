import axios from 'axios'
import EventEmitter from 'events'
import DEFAULT_OPTIONS from './constants.js'
import { diffString } from 'json-diff'
import { wait, readFile, saveToFile, genAuthHeaders, format, axiosValidateStatus, decrypt } from './utils.js'

export default class ApolloClient extends EventEmitter {
  constructor (options = {}) {
    super()

    this.options = { ...DEFAULT_OPTIONS, ...options }

    /**
     * 处理 clusterNames 配置项
     * @param {Array|string} options.clusterNames - 集群名称配置
     * @example
     * options.clusterNames = ['abc'] -> ['abc']             // 数组：直接使用
     * options.clusterNames = 'abc'   -> ['default', 'abc']  // 字符串：与默认值合并
     * options.clusterNames = null    -> ['default']         // 其他：使用默认值
     */
    if (Array.isArray(options.clusterNames)) {
      this.options.clusterNames = options.clusterNames
    } else if (typeof options.clusterNames === 'string') {
      this.options.clusterNames = [...DEFAULT_OPTIONS.clusterNames, options.clusterNames]
    } else {
      this.options.clusterNames = DEFAULT_OPTIONS.clusterNames
    }

    // 初始化多集群的 notifications, 默认-1
    this.options.notifications = this.options.clusterNames.reduce((clusters, clusterName) => {
      clusters[clusterName] = this.options.namespaceList.reduce((namespaces, namespace) => {
        namespaces[namespace] = -1
        return namespaces
      }, {})
      return clusters
    }, {})
  }

  async #getNotificationsInfo () {
    const { host, appId, notifications, accessKey, pollingTime } = this.options

    const clusterPromises = Object.entries(notifications).map(([clusterName, namespaces]) => {
      const notificationsEncode = encodeURIComponent(
        JSON.stringify(
          Object.keys(namespaces).map(namespace => ({
            namespaceName: namespace,
            notificationId: namespaces[namespace]
          }))
        )
      )
      const url = `${host}/notifications/v2?appId=${appId}&cluster=${clusterName}&notifications=${notificationsEncode}`
      // 如果返回 304, 此时 apollo 会 hold 住请求 60 秒
      const requestConfig = {
        headers: genAuthHeaders({ url, appId, accessKey }),
        timeout: pollingTime,
        validateStatus: axiosValidateStatus
      }

      return axios.get(url, requestConfig).then(response => ({
        clusterName,
        response
      }))
    })

    return Promise.all(clusterPromises)
  }

  async #getRemoteConfig () {
    const { namespaceList, host, appId, clusterNames, accessKey, cache, topLevelNamespace } = this.options

    const configurationsPromises = namespaceList.flatMap(namespace =>
      clusterNames.map(clusterName => {
        // 正常情况返回 304, releaseKey: 用来给服务端比较版本，如果版本比下来没有变化，则服务端直接返回 304 以节省流量和运算
        const url = `${host}/configs/${appId}/${clusterName}/${namespace}`
        const requestConfig = {
          headers: genAuthHeaders({ url, appId, accessKey }),
          validateStatus: axiosValidateStatus
        }

        if (this.options.rsaPrivateKey) {
          requestConfig.responseType = 'arraybuffer'
        }

        return axios.get(url, requestConfig)
      })
    )

    let result = (await Promise.all(configurationsPromises)).map(res => res.data)
    const { rsaPrivateKey } = this.options
    if (rsaPrivateKey) {
      const decryptedResult = []
      for (const encryptedData of result) {
        const decryptedData = decrypt(this.options.rsaPrivateKey, encryptedData)
        decryptedResult.push(decryptedData)
      }
      result = decryptedResult
    }

    const { formatData, releaseKeys } = format(result, topLevelNamespace)
    this.#setUpdateDiff(this.#apolloDiffCompare(formatData))
    this.#setReleaseKeys(releaseKeys)

    if (cache.enable) {
      // 更新配置到本地
      saveToFile(cache.filePath, formatData)
    }

    return formatData
  }

  async #pollingListener () {
    let hasUpdates = false
    const results = await this.#getNotificationsInfo()
    for (const { clusterName, response } of results) {
      const { status, data } = response
      if (status === 200 && data) {
        hasUpdates = true
        for (const item of data) {
          this.options.notifications[clusterName][item.namespaceName] = item.notificationId
        }
      }
    }
    if (hasUpdates) this.emit('update', { name: 'update', detail: 'apollo have update.' })
  }

  setHotUpdate (hotUpdate) {
    this.options.hotUpdate = hotUpdate
  }

  #setUpdateDiff (apolloDiff) {
    this.options.updateDiff = apolloDiff
  }

  #setReleaseKeys (releaseKeys) {
    this.options.releaseKeys = releaseKeys
  }

  readConfigsFromFile () {
    const { filePath } = this.options.cache
    return JSON.parse(readFile(filePath))
  }

  async polling () {
    const { notifications, pollingIntervalsTime, pollingErrorIntervalsTime } = this.options
    let { hotUpdate } = this.options

    const results = await this.#getNotificationsInfo()
    for (const { clusterName, response } of results) {
      const { status, data } = response
      if (status === 200 && data) {
        for (const item of data) {
          notifications[clusterName][item.namespaceName] = item.notificationId
        }
      }
    }

    // eslint-disable-next-line
    while (hotUpdate) {
      try {
        await this.#pollingListener()
        // 为测试用例准备，防止测试用例进入循环，无法退出，导致测试用例无法结束
        hotUpdate = this.options.hotUpdate

        // 避免排队重启服务，进入无限循环卡死 redis
        await wait(pollingIntervalsTime)
      } catch (error) {
        this.emit('error', error)
        // 异步睡 5 秒后, 继续
        await wait(pollingErrorIntervalsTime)
      }
    }
  }

  async load (config = {}) {
    try {
      config = await this.#getRemoteConfig()
    } catch (remoteError) {
      this.emit('error', remoteError)

      if (this.options.cache.enable) {
        // 如果从本地缓存获取配置也失败，则中断退出
        try {
          config = this.readConfigsFromFile()
        } catch (localError) {
          this.emit('error', localError)
        }
      }
    }
    return config
  }

  getUpdateDiff () {
    return this.options.updateDiff
  }

  getReleaseKeys () {
    return this.options.releaseKeys
  }

  #apolloDiffCompare (newConfigurations) {
    try {
      const localConfigurations = this.readConfigsFromFile()
      // 如果读取文件失败（文件不存在），或者是第一次读取，直接返回空字符串
      return diffString(localConfigurations, newConfigurations, { color: false })
    } catch (localError) {
      return ''
    }
  }
}
