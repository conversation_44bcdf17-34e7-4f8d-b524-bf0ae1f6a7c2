import { existsSync, mkdirSync, readFileSync, writeFileSync } from 'fs'
import { dirname } from 'path'
import { createHmac, createPrivateKey, privateDecrypt, constants } from 'crypto'
import set from 'set-value'

const wait = ms => new Promise(resolve => setTimeout(resolve, ms))

const formatKey = data => {
  // 转换 a.b.c 为 { a: { b: { c:... }}} 并更新替换 data.configurations 中的指定位置
  Object.keys(data.configurations).forEach(key => {
    if (/\./.test(key)) {
      // 带 . 键名只允许出现在中间位置
      if (/^[^.](.*[^.])?$/.test(key)) {
        set(data.configurations, key, data.configurations[key])
        delete data.configurations[key]
      } else {
        throw new Error('apollo存在无效键值. 键值格式应为: a或者a.b')
      }
    }
  })
  return data
}

const jsonEscape = str => str.replace(/\n/g, '\\n').replace(/\r/g, '\\r').replace(/\t/g, '\\t')

const formatValue = data => JSON.parse(JSON.stringify(data), (k, v) => {
  if (typeof v === 'string') {
    try {
      return JSON.parse(jsonEscape(v))
    } catch (e) {
      // 进一步检查value是否存在未解析的json字符串，如果因value中配置格式错误，一样向上throw错误
      if (/[:{}]/.test(v)) {
        throw new Error(`apollo配置项存在错误的json格式: ${v}`)
      } else {
        return v
      }
    }
  }
  return v
})

const saveToFile = (filePath, config) => {
  const dirStr = dirname(filePath)

  if (!existsSync(dirStr)) {
    mkdirSync(dirStr, { recursive: true })
  }

  writeFileSync(filePath, JSON.stringify(config, null, 2))
}

const readFile = filePath => {
  if (!existsSync(filePath)) {
    throw new Error(`文件未找到, 文件路径为: ${filePath}. 请确认正确的文件路径.`)
  }

  return readFileSync(filePath, 'utf8')
}

const url2PathWithQuery = url => {
  let { pathname: pathWithQuery, search: query } = new URL(url)
  if (query && query.length > 0) {
    pathWithQuery += query
  }

  return pathWithQuery
}

const genSignature = ({ url, timestamp, accessKey, appId }) => {
  const hmac = createHmac('sha1', accessKey)
  const data = `${timestamp}\n${url2PathWithQuery(url)}`
  const signature = hmac.update(data).digest().toString('base64')

  return `Apollo ${appId}:${signature}`
}

const genAuthHeaders = ({ url, appId, accessKey }) => {
  const timestamp = Date.now()
  const Authorization = genSignature({ url, timestamp, accessKey, appId })
  return accessKey ? { Authorization, Timestamp: timestamp, 'RSA-PADDING-TYPE': 'OAEP' } : { 'RSA-PADDING-TYPE': 'OAEP' }
}

const format = (data, topLevelNamespace = []) => {
  const formatData = {}
  const releaseKeys = []

  for (const configInfo of data) {
    // 提取并存储 releaseKey
    if (configInfo.releaseKey) {
      releaseKeys.push({
        releaseKey: configInfo.releaseKey,
        namespaceName: configInfo.namespaceName
      })
    }

    const item = formatKey(formatValue(configInfo))

    if (topLevelNamespace.includes(item.namespaceName)) {
      formatData[item.namespaceName] = { ...formatData[item.namespaceName], ...item.configurations }
    } else {
      Object.assign(formatData, item.configurations)
    }
  }

  // 返回一个包含 formatData 和 releaseKeys 的对象
  return { formatData, releaseKeys }
}

// 304 不是错误, 而是代表配置无变化
const axiosValidateStatus = status => status === 200 || status === 304

const decrypt = (privateKeyForBase64, encryptedData) => {
  const privateKey = createPrivateKey({
    key: Buffer.from(privateKeyForBase64, 'base64'),
    format: 'der',
    type: 'pkcs8'
  })
  const encryptedBuffer = Buffer.from(encryptedData)
  const chunkSize = privateKey.asymmetricKeyDetails.modulusLength / 8
  const decryptedChunks = []

  for (let i = 0, len = encryptedBuffer.length; i < len; i = i + chunkSize) {
    const end = Math.min(i + chunkSize, encryptedBuffer.length)
    const chunk = encryptedBuffer.slice(i, end)
    const decrypted = privateDecrypt(
      {
        key: privateKey,
        padding: constants.RSA_PKCS1_OAEP_PADDING,
        oaepHash: 'sha256'
      },
      chunk
    )
    decryptedChunks.push(decrypted)
  }

  return JSON.parse(Buffer.concat(decryptedChunks).toString())
}

export {
  wait,
  decrypt,
  saveToFile,
  readFile,
  genAuthHeaders,
  format,
  axiosValidateStatus
}
