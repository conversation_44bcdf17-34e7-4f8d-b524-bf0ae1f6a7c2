export default {
  host: null,
  appId: null,
  accessKey: null,
  clusterNames: ['default'],
  namespaceList: ['application'],
  rsaPrivateKey: '',
  hotUpdate: false,
  cache: {
    enable: false,
    filePath: './config.json'
  },
  updateDiff: null,
  releaseKeys: null,
  // 作为一级目录名的namespace名称列表
  topLevelNamespace: [],
  // axios超时时间90秒，目前apollo官方会hold住60秒，所以该时间应该远大于官方
  pollingTime: 90 * 1000,
  // 检测到更新，循环请求间隔时间
  pollingIntervalsTime: 30 * 1000,
  // 轮询监听出现错误重试周期5秒
  pollingErrorIntervalsTime: 5 * 1000
}
