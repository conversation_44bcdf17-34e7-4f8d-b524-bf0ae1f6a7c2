import { createCipheriv, createDecipheriv } from 'crypto'

export class AesTps {
  /**
   * aes加密 aes-256-cbc
   * @param {string} data - 待加密内容
   * @param {string} key - shareKey字符长度64
   * @param clearEncoding
   * @param cipherEncoding
   * @returns {string} - encrypt result
   */
  static encrypt (data, key, clearEncoding = 'utf8', cipherEncoding = 'base64') {
    if (!data) return undefined

    key = Buffer.from(key, 'hex') // 字节数组长度32
    // 注：aes-256-cbc加解密必传iv 由于每次交易签名请求计算求得的sharekey都不一样 所以直接截取作为iv具备随机性 同时又可以避免签名机响应字段携带iv
    const iv = key.slice(0, 16) // 16字节IV

    const cipher = createCipheriv('aes-256-cbc', key, iv)
    cipher.setAutoPadding(true)

    let encrypted = cipher.update(data, clearEncoding, cipherEncoding)
    encrypted += cipher.final(cipherEncoding)

    return encrypted
  }

  /**
   * aes解密 aes-256-cbc
   * @param {string} data - 待解密内容
   * @param {string} key - shareKey字符长度64
   * @param clearEncoding
   * @param cipherEncoding
   * @returns {string} - decrypt result
   */
  static decrypt (data, key, clearEncoding = 'utf8', cipherEncoding = 'base64') {
    if (!data) return undefined

    key = Buffer.from(key, 'hex') // 字节数组长度32
    // 注：aes-256-cbc加解密必传iv 由于每次交易签名请求计算求得的sharekey都不一样 所以直接截取作为iv具备随机性 同时又可以避免签名机响应字段携带iv
    const iv = key.slice(0, 16) // 16字节IV

    const decipher = createDecipheriv('aes-256-cbc', key, iv)
    decipher.setAutoPadding(true)

    let decrypted = decipher.update(data, cipherEncoding, clearEncoding)
    decrypted += decipher.final(clearEncoding)

    return decrypted
  }
}
