import { readFileSync } from 'fs'
import jsonwebtoken from 'jsonwebtoken'

const { sign, verify } = jsonwebtoken

export class Jwt {
  #pubKey
  #privKey

  constructor ({ pubKey, privKey }) {
    this.#pubKey = readFileSync(pubKey, 'utf8')
    this.#privKey = readFileSync(privKey, 'utf8')
  }

  encrypt (data, options = {}) {
    return sign(data, this.#privKey, { ...options, allowInvalidAsymmetricKeyTypes: true })
  }

  decrypt (data) {
    return verify(data, this.#pubKey, { allowInvalidAsymmetricKeyTypes: true })
  }
}
