import elliptic from 'elliptic'

const { ec } = elliptic

export class Elliptic {
  static genKeyPair ({ curve = 'secp256k1', pers, persEnc, entropy } = {}) {
    const EC = ec(curve)
    const k = EC.genKeyPair({
      pers,
      persEnc,
      entropy,
      entropyEnc: entropy
    }) // Generate keys
    const privKey = k.getPrivate().toString(16, 64)
    const pubKey = k.getPublic(true, 'hex')

    return { privKey, pubKey }
  }

  static sign (msg, privateKey, { curve = 'secp256k1', enc = 'hex' } = {}) {
    const EC = ec(curve)
    const key = EC.keyFromPrivate(privateKey, enc)
    return key.sign(msg).toDER(enc)
  }

  static verify (privKey, pubKey, { curve = 'secp256k1', enc = 'hex' } = {}) {
    try {
      const EC = ec(curve)
      const msg = Buffer.from('1234')
      // const signature = sign(msg, priv<PERSON><PERSON>, opt);
      const key = EC.keyFromPrivate(privKey, enc)
      const signature = key.sign(msg).toDER(enc)

      return EC.verify(msg, signature, pubKey, enc)
    } catch (e) {
      console.log('Validate sign error: ', e.message, e.stack)
      return false
    }
  }
}
