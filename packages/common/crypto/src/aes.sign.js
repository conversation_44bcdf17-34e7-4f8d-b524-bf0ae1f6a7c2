import { randomBytes, createCipheriv, createDecipheriv } from 'crypto'

export class AesSign {
  /**
   * aes加密 AES-256-CBC
   * @param {string} data - 待加密内容
   * @returns {object} - encrypt result
   */
  static encrypt (data) {
    if (!data) return undefined

    const key = randomBytes(32)
    const iv = randomBytes(16)
    const c = Buffer.alloc(48)
    key.copy(c, 0, 0)
    iv.copy(c, 32, 0)

    const cipher = createCipheriv('AES-256-CBC', key, iv)
    let encrypted = cipher.update(data, 'utf8', 'base64')
    encrypted += cipher.final('base64')

    return { cipher: c, encrypted }
  }

  /**
   * aes解密
   * @param {String} data - 待解密内容
   * @param {<PERSON><PERSON><PERSON>} password - 必须为48字节
   * @returns {string} - decrypt result
   */
  static decrypt (data, password) {
    if (password.length !== 48) {
      throw Error('AES decrypt error, key length is not right')
    }

    const key = password.subarray(0, 32)
    const iv = password.subarray(32, 48)

    const decipher = createDecipheriv('AES-256-CBC', key, iv)
    let decrypted = decipher.update(data, 'base64', 'utf8')
    decrypted += decipher.final('utf8')

    return decrypted
  }
}
