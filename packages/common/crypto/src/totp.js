import * as OTPAuth from 'otpauth'

export class Totp {
  #totp

  constructor ({ label, period = 60, secret } = {}) {
    if (!secret) throw new Error('secret is required')
    this.#totp = new OTPAuth.TOTP({
      issuer: 'HTX',
      algorithm: 'SHA256',
      label,
      period,
      secret
    })
  }

  static generateSecret (size) {
    return new OTPAuth.Secret({ size }).base32
  }

  /**
   * 验证 TOTP 令牌的有效性
   * @param {string} token - 要验证的 TOTP 令牌
   * @throws {Error} 如果令牌无效，则抛出异常
   * @description
   * 此方法使用`OTPAuth.TOTP.validate()`进行验证。`window`参数默认为`1`，意味着：
   * - 验证时，会接受当前时间步及前后各1个时间步（共3个时间步）的 TOTP 令牌
   * - 如果希望仅验证当前时间步的令牌，可以将`window`设置为`0`
   * - `window`的值越大，接受的时间范围越广，例如`window = 2`将允许前后2个时间步的令牌
   * - 如果令牌超出了`window`允许的时间范围，验证将失败
   */
  validateTotp (token) {
    const delta = this.#totp.validate({ token })
    if (delta === null) throw new Error(`TOTP token validate fail. token: ${token}`)
  }

  getTotpUri () {
    return this.#totp.toString()
  }

  isValidTotp () {
    this.validateTotp(this.#totp.generate())
  }
}
