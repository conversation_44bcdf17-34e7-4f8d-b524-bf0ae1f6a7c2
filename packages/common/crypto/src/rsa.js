import { constants, privateDecrypt, publicEncrypt } from 'crypto'

export class Rsa {
  #password
  #pubKey
  #privKey

  constructor ({ pubKey, privKey, password }) {
    this.#pubKey = pubKey
    this.#privKey = privKey
    this.#password = password
  }

  encrypt (data, fromEnc = 'base64') {
    const buffer = Buffer.from(data, fromEnc)
    // https://nodejs.org/dist/latest-v18.x/docs/api/crypto.html#cryptopublicencryptkey-buffer
    // 非 object 时，默认使用 RSA_PKCS1_OAEP_PADDING 加密 padding 方式
    return publicEncrypt(this.#pubKey, buffer)
  }

  decrypt (data, fromEnc = 'base64') {
    const buffer = Buffer.from(data, fromEnc)
    const key = {
      key: this.#privKey,
      padding: constants.RSA_PKCS1_OAEP_PADDING,
      passphrase: this.#password
    }
    return privateDecrypt(key, buffer)
  }

  verify () {
    // self verify for pub<PERSON><PERSON> and priv<PERSON><PERSON>
    const origin = 'aGVsbG8gd29ybGQ='
    const encrypted = this.encrypt(origin).toString('base64')
    const decrypted = this.decrypt(encrypted).toString('base64')
    if (decrypted !== origin) {
      throw Error('private key password is not correct')
    }
  }

  encryptSecret (secret) {
    return this.encrypt(secret, 'utf8').toString('base64')
  }

  decryptSecret (enSecret) {
    return this.decrypt(enSecret).toString('utf8')
  }

  encryptCipher (cipher) {
    return this.encrypt(cipher, 'utf8').toString('base64')
  }

  decryptCipher (enCipher) {
    return this.decrypt(enCipher)
  }
}
