import { webcrypto } from 'crypto'

const CURVE = 'X25519'
const PRIVATE_KEY_FORMAT = 'pkcs8'
const PUBLIC_KEY_FORMAT = 'spki'
const ENCODING = 'base64'

export class ECDH {
  #privateKey
  #publicKey

  constructor (privateKey, publicKey) {
    this.#privateKey = privateKey
    this.#publicKey = publicKey
  }

  getPublicKey () {
    return this.#publicKey
  }

  static async generate () {
    const keyPair = await webcrypto.subtle.generateKey({ name: CURVE }, true, ['deriveBits'])
    const privateKeyRaw = await webcrypto.subtle.exportKey(PRIVATE_KEY_FORMAT, keyPair.privateKey)
    const publicKeyRaw = await webcrypto.subtle.exportKey(PUBLIC_KEY_FORMAT, keyPair.publicKey)

    return new ECDH(Buffer.from(privateKeyRaw).toString(ENCODING), Buffer.from(publicKeyRaw).toString(ENCODING))
  }

  async getShareKey (publicKey) {
    const privateKey = await webcrypto.subtle.importKey(
      PRIVATE_KEY_FORMAT,
      Buffer.from(this.#privateKey, ENCODING),
      { name: CURVE },
      false,
      ['deriveBits']
    )

    const sharePublicKey = await webcrypto.subtle.importKey(
      PUBLIC_KEY_FORMAT,
      Buffer.from(publicKey, ENCODING),
      { name: CURVE },
      false,
      []
    )

    const shareKey = await webcrypto.subtle.deriveBits(
      {
        name: CURVE,
        public: sharePublicKey
      },
      privateKey,
      256
    )

    return Buffer.from(shareKey).toString('hex')
  }
}
