import { pbkdf2Sync, createHash } from 'crypto'
import rand from 'csprng'

export class Hash {
  static #digestWithSalt (data, salt) {
    const key = pbkdf2Sync(data, salt, 100000, 128, 'sha512')
    return key.toString('hex')
  }

  static #randomSalt () {
    return Buffer.from(rand(160, 36), 'utf8').toString('hex')
  }

  static #mergeHashSalt (hash, salt) {
    const saltPart1 = salt.substring(0, salt.length / 2)
    const saltPart2 = salt.substring(salt.length / 2)
    const hashPart1 = hash.substring(0, hash.length / 2)
    const hashPart2 = hash.substring(hash.length / 2)
    const saltPad1 = Number(salt.length << 1).toString().padStart(6, '0')
    const saltPad2 = Number(hash.length << 2).toString().padStart(6, '0')

    return saltPad1 + saltPad2 + saltPart1 + hashPart1 + saltPart2 + hashPart2
  }

  static #splitHashSalt (hashSalt) {
    const saltLength = parseInt(hashSalt.substring(0, 6), 10) >> 1
    const hashLength = parseInt(hashSalt.substring(6, 12), 10) >> 2

    const saltPart1 = hashSalt.substring(12, 12 + saltLength / 2)
    const hashPart1 = hashSalt.substring(12 + saltLength / 2, 12 + saltLength / 2 + hashLength / 2)

    // salt.length + 1 to cover the odd length case
    const saltPart2 = hashSalt.substring(12 + saltLength / 2 + hashLength / 2, 12 + saltLength / 2 + hashLength / 2 + (saltLength + 1) / 2)
    const hashPart2 = hashSalt.substring(12 + saltLength / 2 + hashLength / 2 + (saltLength + 1) / 2)

    return [hashPart1 + hashPart2, saltPart1 + saltPart2]
  }

  static digest (data) {
    const salt = this.#randomSalt()
    const key = this.#digestWithSalt(data, salt)
    return this.#mergeHashSalt(key, salt)
  }

  static isValid (data, digestHash) {
    const savedKeySalt = this.#splitHashSalt(digestHash)
    const savedKey = savedKeySalt[0]
    const salt = savedKeySalt[1]

    const newKey = this.#digestWithSalt(data, salt)
    return savedKey === newKey
  }

  static toHex (data, algorithm = 'sha256') {
    const hash = createHash(algorithm)
    hash.update(data, 'utf8')

    return hash.digest('hex')
  }
}
