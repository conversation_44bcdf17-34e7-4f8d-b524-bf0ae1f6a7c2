import { createDecipheriv } from 'crypto'
import CryptoJS from 'crypto-js'

/**
 * @description he3.app Aes Node.js 翻译版本，支持动态 mode + padding 加解密
 * @website https://he3app.com/zh/
 * @param mode/padding 支持列表：https://cryptojs.gitbook.io/docs/#block-modes-and-padding
 * @notice 1. 当待加密的 message 为 Json 对象时，推荐对其进行类似 base64 处理
 * @notice 2. 防止 he3.app vue rawValue 与 Node.js 在该种类型转码时的行为不一致
 * @notice 3. passphrase 或 iv，可用密码管理器随机生成，同时支持数字 + 英文大小写 + 特殊字符，位数不限
 */
export class AesAsm {
  static #getCipherOps (iv, mode, padding) {
    return {
      iv: CryptoJS.enc.Utf8.parse(iv),
      mode: CryptoJS.mode[mode],
      padding: CryptoJS.pad[padding]
    }
  }

  static encrypt (message, passphrase, iv, { mode = 'CBC', padding = 'Pkcs7' } = {}) {
    const ripeValue = CryptoJS.AES.encrypt(
      CryptoJS.enc.Utf8.parse(message),
      CryptoJS.enc.Utf8.parse(passphrase),
      this.#getCipherOps(iv, mode, padding)
    )
    return ripeValue.toString()
  }

  static decrypt (encrypted, passphrase, iv, { mode = 'CBC', padding = 'Pkcs7' } = {}) {
    const ripeValue = CryptoJS.AES.decrypt(
      encrypted,
      CryptoJS.enc.Utf8.parse(passphrase),
      this.#getCipherOps(iv, mode, padding)
    )
    return ripeValue.toString(CryptoJS.enc.Utf8)
  }

  static decrypted (encrypted, passphrase, iv) {
    const decipher = createDecipheriv('aes-128-cbc', Buffer.from(passphrase), Buffer.from(iv))
    let decryptedData = decipher.update(Buffer.from(encrypted, 'hex'))
    decryptedData = Buffer.concat([decryptedData, decipher.final()])

    return decryptedData.toString('utf8')
  }
}
