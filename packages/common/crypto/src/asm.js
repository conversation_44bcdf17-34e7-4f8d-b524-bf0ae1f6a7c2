import * as aws from '@aws-sdk/client-secrets-manager'
const { SecretsManagerClient, GetSecretValueCommand } = aws

export class Asm {
  constructor (region, accessKeyId, secretAccessKey, endpoint) {
    this.region = region
    this.accessKeyId = accessKeyId
    this.secretAccessKey = secretAccessKey
    this.endpoint = endpoint
  }

  async getSecretValue (SecretId) {
    const { client } = this

    const command = new GetSecretValueCommand({ SecretId })

    try {
      const secretData = await client.send(command)

      return secretData.SecretString
    } catch ({ code, message }) {
      throw new Error(`ASM Error, code: ${code}, message: ${message}`)
    }
  }

  generateClient () {
    const { region, accessKeyId, secretAccessKey, endpoint } = this

    let credentials
    if (accessKeyId && secretAccessKey) {
      credentials = { accessKeyId, secretAccessKey }
    }

    this.client = new SecretsManagerClient({ credentials, region, endpoint })
  }
}
