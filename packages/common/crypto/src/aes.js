import { createCipheriv, createDecipheriv } from 'crypto'

export class Aes {
  /**
   * 保证输入的密码长度是符合要求的
   * @param {array} key - key
   * @return {array|string} - format key
   */
  static #formatKey (key) {
    if (key.length === 0) return ''
    if (key.length > 32) return key.slice(0, 32)

    while (key.length < 32) {
      key.push(0x0)
    }

    return key
  }

  /**
   * 保证输入的密码长度是符合要求的（老版本）
   * @param {string} key - key
   * @return {string} - format key
   */
  static #formatKeyOld (key) {
    if (key.length === 0) return ''
    if (key.length > 32) return key.slice(0, 32)

    while (key.length < 32) {
      key += '{'
    }

    return key
  }

  /**
   * string to bytes
   * @param {string} str - str
   * @return {Array} - bytes array
   */
  static #stringToBytes (str) {
    let ch
    let st
    let re = []
    for (let i = 0; i < str.length; i += 1) {
      ch = str.charCodeAt(i) // get char
      st = [] // set up "stack"
      do {
        st.push(ch & 0xff) // push byte to stack
        ch >>= 8 // shift value down by 1 byte
      } while (ch)
      // add stack contents to result
      // done because chars have "wrong" endianness
      re = re.concat(st.reverse())
    }
    // return an array of bytes
    return re
  }

  /**
   * bytes to string
   * @param {array} array - arr
   * @return {string} - string result
   */
  static #bytesToString (array) {
    let result = ''
    for (let i = 0; i < array.length; i += 1) {
      result += String.fromCharCode(array[i])
    }
    return result
  }

  /**
   * 处理字符串 key
   * @param {string} key - key
   * @return {string} - handle result
   */
  static #handleStrKey (key) {
    return this.#bytesToString(this.#formatKey(this.#stringToBytes(key)))
  }

  /**
   * aes加密 aes-256-ecb
   * @param {string} data - 待加密内容
   * @param {string} key - 必须为32位私钥
   * @param {string} iv - 默认为空
   * @param clearEncoding
   * @param cipherEncoding
   * @returns {string} - encrypt result
   */
  static encrypt (data, key, iv = '', clearEncoding = 'utf8', cipherEncoding = 'base64') {
    if (!data) return undefined

    key = this.#handleStrKey(key)
    const cipherChunks = []
    const cipher = createCipheriv('aes-256-ecb', key, iv)
    cipher.setAutoPadding(true)
    cipherChunks.push(cipher.update(data, clearEncoding, cipherEncoding))
    cipherChunks.push(cipher.final(cipherEncoding))

    return cipherChunks.join('')
  }

  /**
   * aes解密
   * @param {string} data - 待解密内容
   * @param {string} key - 必须为32位私钥
   * @param {string} iv - 默认为空
   * @param clearEncoding
   * @param cipherEncoding
   * @returns {string} - decrypt result
   */
  static decrypt (data, key, iv = '', clearEncoding = 'utf8', cipherEncoding = 'base64') {
    if (!data) return undefined

    key = this.#handleStrKey(key)
    const cipherChunks = []
    const decipher = createDecipheriv('aes-256-ecb', key, iv)
    decipher.setAutoPadding(true)
    cipherChunks.push(decipher.update(data, cipherEncoding, clearEncoding))
    cipherChunks.push(decipher.final(clearEncoding))

    return cipherChunks.join('')
  }

  /**
   * 双重加密
   * @param {string} data
   * @param {string} key
   * @param {string} iv
   * @return {string} - double encrypt result
   */
  static doubleEncrypt (data, key, iv = '') {
    return this.encrypt(this.encrypt(data, key, iv), key, iv)
  }

  /**
   * 双重解密
   * @param {string} doubleAesDecData
   * @param {string} key
   * @param {string} iv
   * @return {string} - double decrypt result
   */
  static doubleDecrypt (doubleAesDecData, key, iv = '') {
    return this.decrypt(this.decrypt(doubleAesDecData, key, iv), key, iv)
  }

  /**
   * aes加密 - 旧版，用来部分地址迁移使用
   * @param {string} data - 待加密内容
   * @param {string} key - 必须为32位私钥
   * @param {string} iv
   * @returns {string} - encrypt result
   */
  static oldEncrypt (data, key, iv) {
    if (!data) return ''

    key = this.#formatKeyOld(key)
    iv = iv || ''
    const clearEncoding = 'utf8'
    const cipherEncoding = 'base64'
    const cipherChunks = []
    const cipher = createCipheriv('aes-256-ecb', key, iv)
    cipher.setAutoPadding(true)
    cipherChunks.push(cipher.update(data, clearEncoding, cipherEncoding))
    cipherChunks.push(cipher.final(cipherEncoding))

    return cipherChunks.join('')
  }

  /**
   * aes 解密 - 旧版，用来部分地址迁移使用
   * @param {string} data - 待加密内容
   * @param {string} key - 必须为32位私钥
   * @param {string} iv
   * @returns {string} - decrypt result
   */
  static oldDecrypt (data, key, iv) {
    if (!data) {
      return ''
    }

    key = this.#formatKeyOld(key)
    iv = iv || ''
    const clearEncoding = 'utf8'
    const cipherEncoding = 'base64'
    const cipherChunks = []
    const decipher = createDecipheriv('aes-256-ecb', key, iv)
    decipher.setAutoPadding(true)
    cipherChunks.push(decipher.update(data, cipherEncoding, clearEncoding))
    cipherChunks.push(decipher.final(clearEncoding))
    return cipherChunks.join('')
  }

  /**
   * 旧版双重解密 - 用来部分地址迁移使用
   * @param {string} data
   * @param {string} key
   * @param {string} iv
   * @return {string} - double encrypt result
   */
  static oldDoubleEncrypt (data, key, iv) {
    return this.oldEncrypt(this.oldEncrypt(data, key, iv), key, iv)
  }

  /**
   * 旧版双重解密 - 用来部分地址迁移使用
   * @param {string} doubleAesDecData
   * @param {string} key
   * @param {string} iv
   * @return {string} - double decrypt result
   */
  static oldDoubleDecrypt (doubleAesDecData, key, iv) {
    return this.oldDecrypt(this.oldDecrypt(doubleAesDecData, key, iv), key, iv)
  }
}
