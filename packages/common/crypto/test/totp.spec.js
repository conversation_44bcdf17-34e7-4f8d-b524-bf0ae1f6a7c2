import { Totp } from '../src/index.js'

describe('Totp test', () => {
  const secret = Totp.generateSecret(24)
  const totp = new Totp({ secret, period: 30 })

  it('throws an error if secret is missing', () => {
    expect(() => new Totp({})).toThrow('secret is required')
  })

  it('generates a valid secret', () => {
    const generatedSecret = Totp.generateSecret(24)
    expect(generatedSecret).toMatch(/^[A-Z2-7]+=*$/)
  })

  it('throws an error for an invalid TOTP', () => {
    expect(() => totp.validateTotp('123456')).toThrow('TOTP token validate fail. token: 123456')
  })

  it('returns a valid TOTP URI', () => {
    const uri = totp.getTotpUri()
    expect(uri).toMatch(/^otpauth:\/\/totp/)
  })

  it('isValidTotp should return true for a valid token', () => {
    expect(() => totp.isValidTotp()).not.toThrow()
  })
})
