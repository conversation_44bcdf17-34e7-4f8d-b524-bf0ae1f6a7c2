import { ECDH } from '../src/index.js'

describe('ECDH test', () => {
  const privateKey = 'MC4CAQAwBQYDK2VuBCIEICBlWkSTHPyv6sHyIWQzk7bJNQ7uQL6PCHgYkUrZJUZi'
  const publicKey = 'MCowBQYDK2VuAyEAUccjOkjFn16s7OFJ9Q/QB1kiX1kpjtusL0t0A8ljEHc='
  const sharePublicKey = 'MCowBQYDK2VuAyEAlAWdXpW0XrIMiYheFjNtYNkphyMfb/Y06aRwui7fm0I='
  const shareKey = '5905cf4bab55e6218943f2ab7f0ec971b127e0c2f439c5cea5df308d6e9c2e7e'

  it('generate', async () => {
    const ecdh = await ECDH.generate()
    expect(() => ecdh.getShareKey(publicKey)).not.toThrow()
  })

  it('get public key', async () => {
    const ecdh = await ECDH.generate()

    expect(ecdh.getPublicKey()).toEqual(expect.any(String))
  })

  it('get share key', async () => {
    const ecdh = new ECDH(privateKey, publicKey)

    expect(await ecdh.getShareKey(sharePublicKey)).toBe(shareKey)
  })
})
