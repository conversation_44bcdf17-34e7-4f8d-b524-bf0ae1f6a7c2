import fs from 'fs'
import path from 'path'
import { RsaTps } from '../src/index.js'

describe('RsaTps test', () => {
  const rsaTps = new RsaTps({
    pubKey: fs.readFileSync(path.join(process.env.PWD, 'config', 'crypto', 'rsa_tps_public_sample.pem')),
    privKey: fs.readFileSync(path.join(process.env.PWD, 'config', 'crypto', 'rsa_tps_private_sample.pem')),
    password: '4TRuZV8XQ@!-M9wM'
  })

  it('verify', async () => {
    expect(() => {
      rsaTps.verify()
    }).not.toThrow()
  })

  it('encrypt & decrypt secret', () => {
    const secret = '111111'
    const enSecret = rsaTps.encryptSecret(secret)
    expect(rsaTps.decryptSecret(enSecret)).toBe(secret)
  })

  it('encrypt & decrypt cipher', () => {
    const cipher = Buffer.alloc(48, 's')
    const enCipher = rsaTps.encryptCipher(cipher)
    expect(rsaTps.decryptCipher(enCipher)).toStrictEqual(cipher)
  })

  it('decrypt a given secret', () => {
    const secret = '111111'
    const enSecret = 'T7JcQwgqrzGFzF9xVijAJ9snWSoSOlz0AusqHpj87OBTZ6CM8ba1uQfrEeqWeSSB13BlJtfgrndA+J8amfsqwXmYlQrsIbaMTYEyVW4cZ16bw3joW3uihLDoeBspadTclJ9TpmWgoIRS7BYbPuVHzr/GlHfZUUc/+ISuYkZMf2dZ25Rkk6Xq2WOyUpdOLP8Yqtnw+zv1pRk6GZ3wwidV9xmuNJU2yvuWCOp9L07ry6DyLScGhollsrTyZQWWKN/TMaZMHIge+VzYhC+cF2gRtzFq8tKXTGklsOCgasEAfDq4bao+lKirU0WAxP3O1KR4+0186iDnUBOh9PGEl6mDvzqsacQhd2aCgFppd775kR7ZH09zjnzMPwToGTbLYIfuFrrQMuW1cJ8Z1/SrYD+NdOk0M5i892NCGFzdmedI7SIxJsT3ngXwhbDu3ul2k7y5C3eC6Ng+9RkorQHzojyGIBLvwAYw9t2h1wv81EQxP0mLS0s0j3BfeGkBrRCKkF5MouxuMI2zflH+pnB7sTg8l2ip/h53xDjP7nQPEBcNPeVSNvd5lfoLxIaR2UpeFMyCPuEXVbdpdY7Ync35+EGA5aYXvXa95OpPqnOd5ppq4Q2U/FTT+wivW3ZkkDcQ8dLE8qZyQ3Tgk53hA6Hi9VL+QvZKZjdIHfrDIr3GsqIlzA0='
    expect(rsaTps.decryptSecret(enSecret)).toBe(secret)
  })

  it('decrypt a given cipher', () => {
    const cipher = Buffer.alloc(48, 's')
    const enCipher = 'ehrvGcd37k94S+3eLzPJWyqySpTSYrJfEdiGmXA+J+Hf+PS/hsuhiDX8NrWlOEFEo92v0uTIr074okZAQF7THFsOcW2+59lDvykFVJxqxSRXxnm0G1JyoZsiB7cWGFIoQ9MgNSd90mFq7XMTlTYGC6ZwkIAXPgsz8LkuP4MYGvwtFYyuyJEnQ9PClshHUbRET0IMz+obR4PS4cXEhtiRp+KF2zCTw61AgkRzAAk2QnlG6MUXGuuCTdB7CJnuDAhaHYAizmttod5i7/3wbMKijAx5cI26hekmXU5hRWOfDX9RuVK3+xs9fM/WTd6mRN6KMwvoZRxnbjqKs7/n9IDa/B807EPTr0tS4IsI1XJvnXYP5o0K1DftZ357czO70xow7SEEFAugcFw9Y91umjIbnXKo0sb6Wav4uTW90sFwZiO+TcpUSSE+EgnMZQNbrXnB3KsQcKUIAG1beg8s7jOgfYGhrV+EGK1pDHdHBqKQj+KQ5+uHTgmVu0JDSSDQhAAFJ7owNodzGwuXLMWQa7G3B6+cSj81jVMqbpduzO14zQe0ZWBB1UBCvbwviG560jCZ5ti46R9jhUE11TDMpOkqDM/bVb6lUEPvwkuq2xVNXeFS2dTyxuGA/5hOjSmwvtHVKsCxa9/mpjLlZUySkOxgIBK5BBrML4GcDgZ3/ke25jY='
    expect(rsaTps.decryptCipher(enCipher)).toStrictEqual(cipher)
  })
})
