import { AesSign } from '../src/index.js'

describe('Aes Sign test', () => {
  const str = 'Hello World'
  let c, enStr

  it('encrypt', () => {
    const { cipher, encrypted } = AesSign.encrypt(str)
    c = cipher
    enStr = encrypted

    expect(Buffer.isBuffer(cipher)).toBe(true)
    expect(typeof encrypted).toBe('string')
  })

  it('decrypt', () => {
    const deStr = AesSign.decrypt(enStr, c)

    expect(deStr).toBe(str)
  })
})
