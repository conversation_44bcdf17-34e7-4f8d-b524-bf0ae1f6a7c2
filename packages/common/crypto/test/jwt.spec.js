import path from 'path'
import { Jwt } from '../src/index.js'

const { join } = path

describe('Jwt test', () => {
  const jwt = new Jwt({
    pubKey: join(process.env.PWD, 'config', 'crypto', 'jwt_public_sample.pem'),
    privKey: join(process.env.PWD, 'config', 'crypto', 'jwt_private_sample.pem')
  })
  const origin = {
    addr: '******************************************',
    type: '4'
  }
  let enStr

  it('encrypt', () => {
    const options = {
      algorithm: 'ES256',
      noTimestamp: true
    }
    enStr = jwt.encrypt(origin, options)
    expect(typeof enStr).toBe('string')
  })

  it('decrypt', () => {
    expect(jwt.decrypt(enStr)).toEqual(origin)
  })
})
