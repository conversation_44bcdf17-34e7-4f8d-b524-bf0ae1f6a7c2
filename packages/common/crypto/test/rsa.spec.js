import fs from 'fs'
import path from 'path'
import { Rsa } from '../src/index.js'

describe('Rsa test', () => {
  const rsa = new Rsa({
    pubKey: fs.readFileSync(path.join(process.env.PWD, 'config', 'crypto', 'rsa_public_sample.pem')),
    privKey: fs.readFileSync(path.join(process.env.PWD, 'config', 'crypto', 'rsa_private_sample.pem')),
    password: 'huobitest'
  })

  it('verify', async () => {
    expect(() => {
      rsa.verify()
    }).not.toThrow()
  })

  it('encrypt & decrypt secret', () => {
    const secret = '111111'
    const enSecret = rsa.encryptSecret(secret)
    expect(rsa.decryptSecret(enSecret)).toBe(secret)
  })

  it('encrypt & decrypt cipher', () => {
    const cipher = Buffer.alloc(48, 's')
    const enCipher = rsa.encryptCipher(cipher)
    expect(rsa.decryptCipher(enCipher)).toStrictEqual(cipher)
  })

  it('decrypt a given secret', () => {
    const secret = '111111'
    const enSecret = 'b+IvsFt/5btRvQ+mMnnYJSltSUb6D0Xa/Ihqk8jkrIO2fSMajC74of1zVW1ehszYEa96Z7K/Yg8hERusiXhSxn3Ebn6pgv9PeN24UGUHhi+6Z1TW78y3tVjkY701ZDtxVh0YNRiQNDLR3cqtBLtvIRzo1uRsmzseFZHlOFtoSSstMGlX8FzV0Yp6koxV2dejUshfxXGQQWIqKgDZM4/diNJqLXaLin+mlPYRGT+hXVEICE8udIe8YfHBiiz/e8q0GkyO4hBJYw3JV75eTSy1JfJvdk0m7eVsIKvus+6s82oYt8cZ1gPgT+jgy7yMWKVSGHz3VGHXXybtrZFCJP8pPJHECzoLqsAx/NWxjsH6fWongWVrAluA70pPb0Iu1bvIhaqzAhoH8ccykm9gZX5cKybdnOdlNWTPynf4PdzTcghxrqC7GpAW7SUWlNp5xKPGdwHty7xAqD9oxmaEYFsJAz8AVb+DOJkcJWKps3chwyi+lbOmBq9L6PsD4bquSpzMZeJSxUMNcE++q7BbGotisuZsOISrKjCxWEUnnGn2UxT5YOO+vymtlACTQNwY1X9gbfu8Qhi5l0FxBFYhxJcJTLQqV2DSJN+D3wutBH6/+XRU/lgUF0uMmzqOv2V1zsut4LA2W3kgBAtrG0oVrSBcyi/sgVGoQk7BtZX1urvNrOw='
    expect(rsa.decryptSecret(enSecret)).toBe(secret)
  })

  it('decrypt a given cipher', () => {
    const cipher = Buffer.alloc(48, 's')
    const enCipher = 'pHLWE5baxfx4bl4Vy1XrTnlwxf5WGZRV9lVrfdPATStG85KZ9w8EX4fUFGgfWJdNzuAK+5T1e4QfoIhjf/NLAlZUHontrHmPGRRVhBCQjU53Rt9EVssf7djcFf15se/7uwDdtNW40Jl3Hm3m01t79dmFW2QlOUmcMDRn2PhiwxUv0HwWAmkQ3iB/aF5FnuhhFsQqFKma/nqX1Bx44bbyWZ0Y2LNYbqklDP+qurCifWYSqPdjr0/zrC2dhp2fGsUtabRMNx4Zn5ZVA1IX2xjH+iZ1ddKCR19EYSMNqoLdWW/tMTzu9t1ByE/X6bm0rogZYRmsrgH8RUxHJpipOrEqUsUQJ5VGoN5rU8PCSUtgG8ilQZ9GdyT0qeM6NcIrPg6BO/Y93wE6ghlo+zdyQAIS1su8oVdV3zweJpyKe4lUeV9bNaSYUtCFmCefAFGbeJF+gFpMP7OIqFc7VWRjP0ssamg2CYq3Nfh79hlApT3pa4DfiZX6pbq3tUWew1hTHDR85yAHna45OMFbNWLCp2ibh4EJs82turMQv8vyDrcLtr2q0PmKspdYPEAo1X4WNSmEoNTcVp+js2yF+lynKFPLMDCq96BaVHXcv/F6y1tfknM+iPTnlADG7j7NB8gVBMTMWga/myCuMQ5r8Ph47iA2Hur3YTUvHNfITObVgNvGDfE='
    expect(rsa.decryptCipher(enCipher)).toStrictEqual(cipher)
  })
})
