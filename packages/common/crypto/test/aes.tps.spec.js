import { AesTps } from '../src/index.js'

describe('AesTps test', () => {
  const shareKey = '2c7390a4979bb198be012e6a0f182f9a5e641688627d70ee0d7ef9cf7c3d940b'
  const originStr = 'adf123qwe'
  const enExpected = 'HbmElKMur33FIBvoHOaMZQ=='

  it('encrypt', () => {
    const enStr = AesTps.encrypt(originStr, shareKey)

    expect(enStr).toBe(enExpected)
  })

  it('decrypt', () => {
    const deStr = AesTps.decrypt(enExpected, shareKey)

    expect(deStr).toBe(originStr)
  })
})
