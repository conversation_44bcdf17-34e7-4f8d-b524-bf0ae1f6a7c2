import { Hash } from '../src/index.js'

describe('Hash', () => {
  const pwd = '1234'
  let hashStr

  it('digest', () => {
    hashStr = Hash.digest(pwd)

    expect(typeof hashStr).toEqual('string')
  })

  it('isValid', () => {
    expect(Hash.isValid(pwd, hashStr)).toBe(true)
  })

  it('toHex', () => {
    expect(Hash.toHex(pwd)).toBe('03ac674216f3e15c761ee1a5e255f067953623c8b388b4459e13f978d7c846f4')
  })
})
