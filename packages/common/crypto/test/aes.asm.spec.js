import { AesAsm } from '../src/index.js'

describe('Aes Asm test for he3.app', () => {
  const passphrase = 't[&tQ>}c2FRA#x%3'
  const iv = 'VpPxRP:c1#2%5$'

  const normalMessage = 'HelloWorld!'
  const objectMessage = { a: 1, b: 'c', d: '--Begin--' }
  const encodedData = Buffer.from(JSON.stringify(objectMessage)).toString('base64')

  it('encrypt + decrypt normal string with default mode + padding', () => {
    const encrypted = AesAsm.encrypt(normalMessage, passphrase, iv)
    const decrypted = AesAsm.decrypt(encrypted, passphrase, iv)

    expect(encrypted).toBe('JtGAESSEwU35wNxs8ER58Q==')
    expect(decrypted).toBe(normalMessage)
  })

  it('encrypt + decrypt object with default mode + padding', () => {
    const encrypted = AesAsm.encrypt(encodedData, passphrase, iv)
    const decodedData = AesAsm.decrypt(encrypted, passphrase, iv)
    const decrypted = Buffer.from(decodedData, 'base64').toString()

    expect(encodedData).toBe('eyJhIjoxLCJiIjoiYyIsImQiOiItLUJlZ2luLS0ifQ==')
    expect(encrypted).toBe('MQuIKLWxUAl/c5Hcxfc1eyWYaxsAnstfcSn9i7GB+PGW3dQLtUtbGES6BFQ7ASTj')
    expect(decodedData).toBe(encodedData)
    expect(JSON.parse(decrypted)).toEqual(objectMessage)
  })

  it('encrypt + decrypt normal string with mode[CTR] + padding[AnsiX923]', () => {
    const cipherOps = { mode: 'CTR', padding: 'AnsiX923' }
    const encrypted = AesAsm.encrypt(normalMessage, passphrase, iv, cipherOps)
    const decrypted = AesAsm.decrypt(encrypted, passphrase, iv, cipherOps)

    expect(encrypted).toBe('Nxet8WHp04UbHEZ1xp9xaw==')
    expect(decrypted).toBe(normalMessage)
  })

  it('encrypt + decrypt object with mode[OFB] + padding[Iso10126]', () => {
    const cipherOps = { mode: 'OFB', padding: 'Iso10126' }
    const encrypted = AesAsm.encrypt(encodedData, passphrase, iv, cipherOps)
    const decodedData = AesAsm.decrypt(encrypted, passphrase, iv, cipherOps)
    const decrypted = Buffer.from(decodedData, 'base64').toString()

    expect(JSON.parse(decrypted)).toEqual(objectMessage)
  })
})

describe('Aes Asm test for Python', () => {
  const passphrase = 'Q<=)y*.HmuiINC-:'
  const iv = 'x@xe-Ih_]nF1{q^M'
  const encryptedData = 'f9e8c34d74b564ed90b1f81eafe92be0b35e940c2e4de76db52fb95ce8cbe015009c5941183fc304a88b522eec4b86bf289b9ecbff2697eb41816d0d82f8ec6b0ce082a8878013006d726201c7fcdc62e66eb9d72ca4c128ecfebe2b2f0698325a1a2f17d46a0b25b9ff45364f9bb5a3795b25f0012a3c22aacd26102fd8495b926983907f4d5acf2fcaf2bd2d1f8f57546bc7b82e575ff50cde7ef55b5cba4eead9373c39441ca1a14d7b564c9c370a603048361ba86296edcf86adee2ee8cdc3226292ee4b01ab9c574d07c39f89e1fc4e16ac9dc0b0a43b1ff6236868ae128afea3be903f6699fc035ead7b73412fd349f5f29a57d91ff977500a532c715c1adc56f76c83fcec62830a91b564d17417152ff1d3259bb1f964e49668e38176ff8099ce6da398f6ac78f37a829ee7875b329239d32ce7025d02d31b8b00a8985214f217b9cd83aaa796fae78666d0d72d4c3c42564aa8f09d7b013c9811e00a46b62f526ce69cacb438e8577e63f49d295ed38820d7fc01573793d444fedddd762b1934f93fa8006039e483dda60b1360611fed7221ece97a6cee5f0b2204f9d3f3eda32b3226a46a016a1c520c75fdddde533e97c7aa7893ebdfaaaff3461b2a136914b6ce4c65c848ef68657037c725d30c8334019ef5e076641f2e23051a770a902332a37c6924de57320f3cc82e7fe221edd0bd51807ebe9845a0c91628784f0979ea3ed40ee9dcf81baba2767fd0fa586f94455249a856f5f0c37371305e25706bbb250d12d29d3dbed1e8b37b3f3d1843d205b5f61a3550902ed725b5ba56bddf0fa0310803b71aac90515ed9d386a014e0e48e88b0cf0b3ae50532349390bc3473875cc338317357496cdecddf6ca953ae78b7b006220e34b23ad9951aa4a4873a610d04ef0401208df32d846df3503e631fab6712e7c3285c55a87769db2ac6e01dd51926cce1217d094c43c6b40f5aab65fe1ea2c3e967bd6ab2ad4575266229ffd2769ff52d1d27e36109f90c1f31996d82f59f4139d28b7d4d26d217c466c4a78286ad8a70ddf166b3ddd7f39da982da4bb1da151f46a1e6953dc0fa795147ab6ad5262b4da6cef5334e17aea04cfa047b34a2b6a3b46dc5902e75197f6fa55fccaaf8e76b983e008c13c9ed6259dbe91cea77c75f50333c47226bc47fc9080dd29c2816886589ac6df05f11865d183d4f0d01245a3e9f1d207083ab32c231a4b565d38f79e3a2887f5b3d05d8c940971f054642d884ee66a612b823a70bb01ab610163156d567b0ab3b28d80de4075fb48a74b9ac039b03d3b5a81c83eb13ff126dd04a0240c2399c1767e5b3f112844f6c276082e88253c15166fac971855146fca5fd2c5a97219c9f8ef1ef681ce5dc4381f32c65715ef0e6d1ec5d3a7ec3611f8549fc53bfb2d2a7c00277dd3f324cd51fe390697cb2efe89288863bce9e96a90ee626e7e13c20c212ac55fecc5f33abdbf7354be9f86fc0e20e04531f07827085d12b4a1c8c55fc4b4d41b86567bd832c764036374fd2397d972f12dd1229948ad7ca876437f891abe60bdca523458316a6ba1ac4697338cccac959973f15c7b95da08dec55a8b2ca6b89e5f3ffee8181d4724c494238faf01f70abbbcee67cc09b0d1ba57caae6f26821e18c8b08dcc542d9d2ecec9132d3aa825dd918e9e55b09ccbc9412d9ebf3acc84ff317caae987bf1b7f56524382e1f4a7825a337b67351739051b36d5f8eb845535334725390efa82c47a8c89eb2e30a9734f9a673e064e6a34c75b356b4fbcdd9dfcc8fbda947b4d68bc7c8a34a3fb8cc3b99f50cb2bb7f32fdcb6a32b0522d03595c188de2e6488f1af9fafa127580f50ddefdc5384eed5233fd8750f54a14532c6934e661c2c58f9c77d27cc31c628562223e562443c3adcf2a429a086a9baf2d68c650d9989cd00bfeceaa76a690c2f236ba47fa34657fc254211f8f6e6dcb14c66ce4fd09625c4acdff74452f8d3150a36eb64141194979225508fda9a38c4933c824416bfc3ac141611e137d3ba4afd5434c4a8e6fd0b42e4df00116784ccbc39c751bea0eec5061835d3e74b11da568d1513c1ff874762598b73d9ead684d13475645523ab7b2362a1544eca2b8b8a1cb16ccd04b2359d55bf9b87282f478562c4a07a7f9ec5d33498d74c7274a31025e12ea8cfc2dca12a6b73e23e68def29e2e7cd93f6687998cf89225bc2103ae7579c53b50730c0b4ff4299090e84354ea6f00b760829ac756fdf6fda27f8cdb24cd98da07b52c5caa6ef0e3ae40320080b2ffe299a6f83a1e3378f2ede37eea42382daf09e0b415fb1a93d7744979ffc6de99a881edaa7e7e9ab4813a4edd928357e3c2e1a74509a44a921db3e214a1ad685e0ebde0c47bdc59e8a30fefae162d52e03eea502d21d0c30b8866964a2e6e0d00b2eefe5368e2b0ec6911d3627aa90fe6f1fa4026cf220d76b5982fb97b83a522a9896b4e5529416784da7650f68b8bb4710f265dc1672bcc6c3139054831bac69027bf598ab615ab2ebd0c01410a85f3127fc18c18f8651b8e6ef534f05e641061dac4224c441ebb73b109eaeb35d1c8bd055a8f6de5625ffca04839008cb6cf2a05ecb7e20c6f54d6e767d5707bdc13b7d73b5a5636c0ba459aac7b42664c2a9b35dbbe4782fcbc6309b181f93ae850661cf471184cea89cc2ee652353e00bc54d32d0f79c05a3d3759832d1a77671235d29f8b07233130ab5d9ebd75778e93f8ee320a8e3195aa53ced90b7e7640e3c0dc74879f127bc7b68baa8105666f7708ad5fa4db887e7d36e752547ce76dfce8c7a652f29a56b1cf5455faccb3747cdf90c7a664a668ec1cb16f0f0136bf3bc1a86964498096b5a209974e17649166ac6a7531209b27bf5006a5c78a417f2c03aad8d2bd54815cbd7f84a145da5bf2c3270ecf444bb9bb98a0b1ff3170b41e863de710ec899dc3cb95fc48990fb9f6c6461e4faf29801ffdbb99b70d2730fb0dfa042d9137672bea82bfa688c1fc51b4010b36b0364d10c334a84233d063155b4866cd5f175202e3b761cebaacb1046ffd10c2d2d256ea450f46d4ab451040fe74ad038f925735ba4eafaa6b5b2b012d4c9dae2aa459ef6f4f83bbc03799bb92c4c9dae090c992692c0d265817972a6e70af2dd90f53f6ba4020f31e2dc21e1415103181fde49981ecb84d55f249aeb485a77b65f48437b4ac671da308b6ffec8438bf0e631059d2e31bc36ad94ce6b7b7f2b05596996b19085732ad7724d72095fe92993df56ce18796e67b5a737d2a9f70fea7d8f2684a81e85f993ebb6a963fc5fc61efd931d0902fd4af8cd1c9f6b41e78ff0a41503bde00a42236c9b629fc079799e880830e596e7790c517b5431ee3fa4b30246dab8f5947c22b69d26480e9e83de465a7a5c36aee28f1ca29fbd71959ca4b393ff8b7e5cf542e1ed06f70e0ff59eca64318e51569a15b584ce9311c69694144d147a41e1ad7af5782524eb16e203a546de9d52a27149712ce40d467b70b1d1c51cd3845694641fc0631f2b7c9a3839d807f9a4f9168ce72993bb8e0d6055989e98c322e59a903fb209b60bb850ffab13400f7b0bc434d64d917d6121946e55c255df3d6ae8217757ff6b3a7ac4091fb1cdf92b78dbb739ea022d2ec65eb0232fae8a0632036ee2b5b6498000c3b0ef2dae9c6d5d51e683ef71a00276c695bb6112f8585439a9cf4ee997c48dd01061a1de42138f803db555699b174b9cb81b39dc4908c2e687cfc9b5c43770780533a2bdfd2e431ca2dd9729b6fbaa32fc5bc4df2ada04a5e57bdded16baea9cab101b3306fe7f73234e29ff08330773245475b0c0c04cddde6b29b9add094d2bb6c06abbc557f4a2a07fa9c6d40bcbdff5ea1cb9b6d83acc794830e2da932d19d3c10e0bf0563bfb8f2b4923f458150611ea6f14431622a882e4c4e28cb9619c0dd68162753a708c928a6b12262836245bdabb973b7ec9a2c7fea6d5061da897a2da998062a573a4e816ce795a7215a035249a4699534db727178f9f926e4403a421f4afed52b490ddb767e34aba974531c80664ae053a8cb421fc57cfddb094c69a61ec878a46b7e31c391a0b0a6aa4eef39b8af5a7ac923ec968b2c1906dc6f30780d4153decb55584db3db5f665dcf82c4a1a2bb8bdad6e1bce7bb6a7c43398072f19788353512e7cc41b4e4b64910f8508449a877ffc86f69563e91b59bed0d1f18680b6a86a0067ae8bf4b3b0a44b429f20d057c5e16f587fe49f109f51584c3d1ee4c83270306b1387db08ed912f8781ae8786490663240b88b2994c8852f7440d76e9064e55a56e85d2cd4d9ab414ac855fe0a8c0235885dff7feabc004fd8cd00bbcaf62b5c50039b6e918c44caa932beb9ed51add76d9a0042e3555a650bf6c1c4432b6b8d3391bc19d0cfdb2819a6d3354789c9be1838fcfe4d2d737a0e1e2c3481da7e35305f91f50f04fdfb483955e4f7d828b5a2b32326b8b2a6035b0c2f27de363c6c8856a93a1b145d23b1302f7dab7b4db07dca129b4ff159eed04f02fd44ed2142b21ad4354c03bf0313a4ed6a8b9d574b466e8dcdb4ed4abed21915a21d2af6d8864b2d79a1c15f020c3cb97124ddbb69c12acc38dc102c54b29316f6c06e01e70132536ae49d9ecf95bbe5052d5107bd0295c2afc80117c3a4743246356adb0546f1a223cf01d3eab131c5f4bf98fea53e7e3266425645e809e6b8e198fb8d16643bac8d78d86448d5edfa5a34158bb2ce6b973cc29d0dfc642d1be65a0840a9e57116d0a9555f02bff2d74c833b9ddacde6aad5c51e4f92f41375a934347ac3c711362b6059325878622c707d5dee48f3629d2848f4a656767f5c26589ea2e4140d3b9c3cb4abfca79d37228a435cd3a36b2964134c480dabd1076aa738c6c04a259f9cd49b350fc96dd975acc4f5ebd1209446e1cbb374b94447d061df69386ceeabe3cfe086d314bf9405c05c236a2423fc76bcb1f417be95e0deecca2ee3fe744599f371c91b5f6b6984dfdfe0de6033f7b8b5d24870c43447006e286814af206b1cc166257ae133ce569dc0606e74d23188be043428d97842bfecb78579f104102c7f9d4893e57e3f1cedf3cc2be16b701e6b044625c15ae5b4cb97900240d8bd641ca62c3068b999c4447a56b61b8297d81a3e8a778e8b9991c99dc2bb905497b120784286411f766f5641cc7002b6f4657e3f4a1ddc9ecd56eeaeea0a03850796845e870f722b72d6ecc6165f9c6377707c85b64826bac7b077fc43d0db5ba3c7775358e8979807b94e9ff4ad554ed45d37d615a89c37326b9511fe4565d33fc7f586f8be4e617dc3e75440c065864fd4aea2a39531c12e22828490e8a892192e9b069efa47040a5bd28c7941e5c264a059cb540c2a5cdccb419e42424f08e74a8d84291d65ecc78237f0627dbffa45dc765f402eeccb8283d250c0ab6ba8feafb746d9a5b7f79e6fd44e70957002d95591364b9ee4b26a8e2c72956dd2e2fd0f9724d76c892a9e6a6286091e84d9ce3acb1f79a9dfecf21de5b1a26a7fc26bf674a3243cab44bde57c0d4100b3063e4a34dd607132d77304afaa2cc17a727d480482b5f8e0f54145f083471c8743d37966c2077ca48003fe55d17e4aa108131cacdedb4ed527975bd2302c641625d2ba217398db6f8edebccb7dcf57934de619ba74bf20b1a1e89de45a549a1b1020076c3c406826f2db4853d8f8b15ca54036729cefe751e3c2757815efb1d1709f03a35675f45bce5d51071999d02ab5cf2f16c30fcce91601da2396022088d128a6c50dfe25ae6818d84d2979662a6901a5499768e29785a01e44a0fd7f63e586184d8bb6fcc7001c7a909cadff22c32854cfe3ffbb3f1c4159e643b7c30285d990f9f97780480422767d8ca23c5101bc6722a574ec000473c190d6e57ada8e0b10a10e05fb788c5a316043b2cf0aac13e39cbbb86084175a3a61e17c2bae647e7b627ada46dfc5e41c21c19fbaab36a3bbb36a3c69892f27a72778ec3bc675b6466dc7bc343ceff924da67d93c8a99f20ff75f21e72230ff93ffd00adb2ca52384a1cccf18409e5206a5d3159f6897783da7bae362dcd802d3e0ef8874efdb10bde5a1806843294d4d4b3ad278ce925143c7876536d61ab7a508060dc081e9082bc672a243f859ca85b40313104377d06254b96caf39e55b5f972d6cfac21f848979557d3386d10824fd92d8338a78704539052bffac6722b552303e8ddbec8ba0b564cc05f5af5c56bc393b190d19c42a4391ad1ecab4c45712c93bc68b3f87c95425bb74423ed8365b482d92d4b52066049d895a3582d3db9aa56d5306c2ef5d45277d06771286679c3950c2fbec107661c032df52dc31fd3838f9742d695b3ae009085fe093308ecc4926fee86da4f33f586a0c0d1f9fd6d7ebddb7367d6084136635cc35a538e62f5206088e66d8b982f47d5d8321b61f533c0649ad34d36b865b70ae947b774c746cbb784c0a4db788bd8a3972c78047faa384acfc5c12b6390674c7281b1f2b7f1a52001a78f9c3d20406423a3f0e07bd661ef13edda6a47763ad6f99959e234ebaf79089fa90c9843443910c86902483f5a98b6ae030627c6ce05828a24a5e1d01b75db8abbb273ca5cd60cac1bea452ed67f1c41a400cd60f3442b50df34173f893ab9c554989e50d25a4eae4a4a5429ba4fe37667d400440428320dbbfcd5daa0aa09655a2101c33091dd3926bc525f4ba0ac3702c6221750fb7ca799d34354d5db2e21e9f51a96bb2e2a630c0a2926ac067df0b00d3f2dbea74263e0f0693ee95d0cbddfc81f237921fd079cca7176268e91283b6d057a9e031231ee514a20f2c033788300f7f3d38e9405f02edec8f222d9355784805a93ebdd8673e7a6aa816e7266c531d032744ac461b864163fef35cd2e3c3c3c21a2e3461d14456e1d33e71fbcc5018511a8f533795abf13297eae91a77862ea9a7577329b9db220845212e640ba016743de0b56c40dcaaf3ef33f643fe1cddf71bb5e457b62cb9a4d8c4c7be15a44b8176350338953d960ad3aafd351ac7d77e7a03b1be6cc892bf32461beca2841d45cefd538e20d595e68d22a3a6deb50baf5637d95cfa1931eefd2ab1ab4f7ad804e8ebca3b498fbf1a4f4fbe87b9ecfcae075ff016d03653b3ff4e2242990f88bb67c566f89ea295d3fead5c87c7c9927371694e82ecc212adcf0b13760feabb5ecd00cf0026a1124f342f6b75610514d293c2d44e7c617c2edeec9ca072065a777f60911743f589b2be31e4c9791e2d2f7834c3f3d5e90ec0911330be650d1706128af759800a30416b8ba9cf101c6d92ac9cb7be91e560cc4cffbdb8a2e860463609d6e3c053821f9326136d8e0cc43a07b9415cb9a079f5bc888feffe43cb910efa3b8c428c4e955c23b5d256dc06d2dad23e8df2c79dcc45c0cca94f963c959002b104901e731c9825d5ef1e50902de6ac919de64220cf4cc5fc30842a346821d2c540648929adc74161edf2f71b7cfd80be9634c95f345c16ad3c66fa86e5eee063b6f342dbf863f9684792def14a447de5c586b8c6c9077951a22553728b46c142e0c3df446aa86fb6d1efd7bb463694abb696b614895044632f85cfde649728580be5a6dc7a1362febc9f43b30d1d63429da7c4f0d4f47f3a23f4a93178ef258425bd8aeece383877b620a1ed2de4c1e6351569bf37415dc62dad5529c0c9b3a440d042fc78d477eb18e0e2f5879291b87ade620647fd68597927794a68d91adbbef5c338309eb204cecab7d1ff3c027d46b735040239ef984e641c4974084c4ace134452d21d4cc3a1827b3c9ea44c263cf89b687358a069fa63bf47a19f3727e7fccc048639a5ec8c06df4da6b9180f317a8a3be5318dd45f18e63d4396dcfabd46bb17f8cdee1c4cfe8282540b783c1d5e815496251699f3f166486a69bf237d25ae3c52576698b4e8b44bb1c613ed99f5118730ebb1fe84abd3896411d21ecd861d62dcf328fe5cb8e653190d6024f471c0b231ad32ed87db897d05b52f0de4d9ee02487da6e64056b4276f39925ae4'
  const encryptedExpected = '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'
  const ERROR_MESSAGE = 'error:1C800064:Provider routines::bad decrypt'

  it('decrypt success', () => {
    const encrypted = AesAsm.decrypted(encryptedData, passphrase, iv)

    expect(encrypted).toBe(encryptedExpected)
  })

  it('decrypt fail with wrong passphrase', () => {
    expect(() => { AesAsm.decrypted(encryptedData, passphrase.slice(0, -1) + ']', iv) }).toThrow(ERROR_MESSAGE)
  })

  it('decrypt fail with wrong iv', () => {
    // 注意：iv错误，程序不会报错，只会返回错误的解密结果
    const encrypted = AesAsm.decrypted(encryptedData, passphrase, iv.slice(0, -1) + '+')

    expect(encrypted).not.toBe(encryptedExpected)
  })
})
