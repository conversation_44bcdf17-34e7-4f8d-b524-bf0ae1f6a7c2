import crypto from 'crypto'
import { Elliptic } from '../src/index.js'

const { randomBytes } = crypto

describe('elliptic test', () => {
  it('genKeyPair', () => {
    const keyPair = Elliptic.genKeyPair()
    expect(keyPair).toEqual(expect.objectContaining({
      privKey: expect.any(String),
      pubKey: expect.any(String)
    }))
  })

  describe('Use default curve', () => {
    it('sign', () => {
      const { privKey } = Elliptic.genKeyPair()
      const msg = randomBytes(32)
      const signature = Elliptic.sign(msg, privKey)
      expect(typeof signature).toBe('string')
    })

    it('verify', () => {
      const { privKey, pubKey } = Elliptic.genKeyPair()

      const res = Elliptic.verify(privKey, pubKey)
      expect(res).toBe(true)
    })
  })

  describe('Use ed25519 curve', () => {
    const option = { curve: 'ed25519' }

    it('sign', () => {
      const { privKey } = Elliptic.genKeyPair(option)
      const msg = randomBytes(32)
      const signature = Elliptic.sign(msg, privKey, option)
      expect(typeof signature).toBe('string')
    })

    it('verify', () => {
      const { privKey, pubKey } = Elliptic.genKeyPair(option)
      const res = Elliptic.verify(privKey, pubKey, option)
      expect(res).toBe(true)
    })
  })
})
