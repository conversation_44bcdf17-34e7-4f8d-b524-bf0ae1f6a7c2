import { Aes } from '../src/index.js'

describe('Aes test', () => {
  const pwd = '1234'
  const str = 'Hello World'
  let enStr

  it('encrypt', () => {
    const enExpected = 'mdGkTElNnDcSsKJuNe7CqQ=='
    enStr = Aes.encrypt(str, pwd)

    expect(enStr).toBe(enExpected)
  })

  it('decrypt', () => {
    expect(Aes.decrypt(enStr, pwd)).toBe(str)
  })

  it('double encrypt', () => {
    const enExpected = 'HMHbNlTEv9W60vDjmeaTL3eSv4BZd7r8IfRPEyu3uaA='
    enStr = Aes.doubleEncrypt(str, pwd)

    expect(enStr).toBe(enExpected)
  })

  it('double decrypt', () => {
    expect(Aes.doubleDecrypt(enStr, pwd)).toBe(str)
  })

  it('[Deprecated]encrypt', () => {
    const enExpected = '+j+0PF5qCPAjqmEiYlEidA=='
    enStr = Aes.oldEncrypt(str, pwd, '')

    expect(enStr).toBe(enExpected)
  })

  it('[Deprecated]decrypt', () => {
    expect(Aes.oldDecrypt(enStr, pwd, '')).toBe(str)
  })

  it('[Deprecated]double encrypt', () => {
    const enExpected = 'wDG8kC2Gm4PJYmpAE5a6ie6pDSKrN9nxzbepVq0eErM='
    enStr = Aes.oldDoubleEncrypt(str, pwd, '')

    expect(enStr).toBe(enExpected)
  })

  it('[Deprecated]double decrypt', () => {
    expect(Aes.oldDoubleDecrypt(enStr, pwd, '')).toBe(str)
  })
})
