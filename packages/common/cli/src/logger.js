import fs from 'fs'
import path from 'path'
import chalk from 'chalk'
import bunyan from 'bunyan'

const logConsole = () => {
  return {
    info (value = '') {
      console.log(value)
    },
    done (value = '') {
      console.log(chalk.bgGreen.black(value))
    },
    error (value = '') {
      console.log(chalk.redBright(value))
    },
    warn (value = '') {
      console.log(chalk.yellow(value))
    }
  }
}

export class Logger {
  constructor (logPath) {
    this.logPath = logPath
  }

  activeConsole () {
    const logger = logConsole()
    Object.assign(this, logger)
  }

  activeFile () {
    if (!this.logPath) {
      this.logPath = path.resolve(process.cwd(), 'logs')
    }
    if (!fs.existsSync(this.logPath)) {
      fs.mkdirSync(path.resolve(this.logPath), { recursive: true })
    }

    const logFilePath = `${this.logPath}/signer.log`
    const logger = bunyan.createLogger({
      name: 'paper-wallet',
      streams: [
        {
          level: 'trace',
          type: 'rotating-file',
          path: logFilePath,
          period: '7m',
          count: 100
        }
      ],
      src: true
    })

    this.trace = (...args) => {
      logger.trace(...args)
    }
  }
}
