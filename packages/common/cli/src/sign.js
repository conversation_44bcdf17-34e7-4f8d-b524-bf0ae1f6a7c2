import fs from 'fs'
import path from 'path'
import chalk from 'chalk'
import inquirer from 'inquirer'
import moment from 'moment'
import shelljs from 'shelljs'
import sequelize from 'sequelize'
import { Aes } from '@common/crypto'
import { multi, divide } from '@common/utils'
import { Model } from './model.js'
import { Logger } from './logger.js'
import {
  pwdValidate,
  isFile
} from './utils.js'

const { exec } = shelljs
const { Op } = sequelize

export class Sign {
  constructor ({ Transaction, chain, dbPath, logPath, supportBuild = true }) {
    this.chain = chain
    this.dbPath = dbPath
    this.logPath = logPath
    this.supportBuild = supportBuild
    this.logger = new Logger(logPath)
    this.logger.activeConsole()
    this.logger.activeFile()
    this.model = new Model({ storage: this.dbPath })
    this.transaction = new Transaction()
  }

  async signTransaction (txData, privateKeys) {
    const { transaction } = this
    let tx
    if (this.supportBuild) {
      tx = await transaction.hbBuild(txData).hbSign(privateKeys)
    } else {
      tx = await transaction.hbSign(txData, privateKeys)
    }

    return {
      txhash: tx.hbGetTxHash(),
      output: tx.hbSerialize()
    }
  }

  // 手续费计算各币种之间可能存在差异
  calculateFee (txData) {
    return multi(txData.fee_step, txData.fee_price)
  }

  // 如果遇到非常规币种这里也需要重写
  getProcessededInfo (txData) {
    let addrs = []
    let infoStr = '\n------------- tx information -------------\n'
    if (txData.vin && txData.vout) {
      const vinInfos = []
      const voutInfos = []
      addrs = addrs.concat = txData.vin.map((item) => {
        const amount = divide(item.amount, 10 ** txData.decimal).toString()
        vinInfos.push(`${chalk.red(item.address)}, ${chalk.red(amount)} ${chalk.red(item.asset)}`)
        return item.address
      })
      infoStr += `vins    : ${vinInfos.join('\n          ')}\n`
      /* eslint-disable array-callback-return */
      txData.vout.map((item) => {
        const amount = divide(item.amount, 10 ** txData.decimal).toString()
        voutInfos.push(`${chalk.red(item.address)}, ${chalk.red(amount)} ${chalk.red(item.asset)}`)
      })
      infoStr += `vouts   : ${voutInfos.join('\n          ')}\n`
      infoStr += `fee     : ${chalk.red(divide(txData.fee, 10 ** txData.decimal).toString())} ${chalk.red(txData.asset)}\n`
    } else {
      addrs.push(txData.from)
      infoStr += `from    : ${chalk.red(txData.from)}\n`
      infoStr += `to      : ${chalk.red(txData.to)}\n`
      infoStr += `amount  : ${chalk.red(divide(txData.amount, 10 ** txData.decimal).toString())} ${chalk.red(txData.asset)}\n`
      infoStr += `fee     : ${chalk.red(divide(this.calculateFee(txData), 10 ** txData.fee_decimal).toString())} ${chalk.red(txData.fee_asset)}\n`
    }
    infoStr += '------------------------------------------\n'
    infoStr += 'confirm(Y/n)'

    return {
      addrs,
      infoStr
    }
  }

  async getPrivKeyByAddress (addrs, password) {
    const { model } = this
    const res = await model.findAll({
      attributes: ['address', 'private_key'],
      where: {
        address: { [Op.in]: addrs }
      }
    })
    if (!res.length) {
      throw new Error('KeyPairs can not be founded in wallet db')
    }
    const keyMap = new Map(res.map(item => [item.address, Aes.decrypt(item.private_key, password)]))
    return addrs.map(addr => keyMap.get(addr))
  }

  async markAddrs (addrs) {
    await this.model.update({ status: 1 }, { where: { address: { [Op.in]: addrs } } })
  }

  getFileMd5 (filepath) {
    const { stdout } = exec(`md5 ${filepath}`, { silent: true })
    return stdout.split(' = ')[1].split('\n')[0]
  }

  async sign () {
    try {
      if (!this.dbPath || !isFile(this.dbPath)) {
        throw new Error('The path to storage account is not right')
      }
      await this.model.sync()

      const { buildFilePath } = await inquirer.prompt([
        {
          type: 'input',
          name: 'buildFilePath',
          message: 'input build file path:',
          filter: input => {
            let inputFilePath = input.trim()
            if (!path.isAbsolute(inputFilePath)) {
              inputFilePath = path.join(process.cwd(), inputFilePath)
            }
            return inputFilePath
          },
          validate (input) {
            if (!isFile(input)) {
              return 'The path to builded data file is not right'
            }
            return true
          }
        }
      ])
      const buildFileDir = path.dirname(buildFilePath)
      const buildFileMd5 = this.getFileMd5(buildFilePath)
      const txData = JSON.parse(fs.readFileSync(buildFilePath).toString())
      const txDataStr = JSON.stringify(txData)
      this.logger.trace(txDataStr)
      const { addrs, infoStr } = this.getProcessededInfo(txData)

      const { md5check, infocheck } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'md5check',
          message: `md5 "${buildFilePath}" is ${buildFileMd5}, confirm(Y/n)`
        },
        {
          type: 'confirm',
          name: 'infocheck',
          message: `${infoStr}`,
          when (res) {
            return res.md5check
          }
        }
      ])
      if (!md5check || !infocheck) {
        return
      }
      const { pwd } = await inquirer.prompt([
        {
          type: 'password',
          name: 'pwd',
          mask: '*',
          message: 'input password',
          validate: pwdValidate
        }
      ])

      const privateKeys = await this.getPrivKeyByAddress(addrs, pwd)
      const tx = await this.signTransaction(txData, privateKeys)
      const jsonTx = JSON.stringify(tx)
      // write radata into log
      this.logger.trace(jsonTx)
      // write radata into file
      const signedFile = `${buildFileDir}/${this.chain}-Build-signed-${moment().format('YYYYMMDD-HHmmss')}.json`
      fs.writeFileSync(signedFile, jsonTx)
      const signedFileMd5 = this.getFileMd5(signedFile)
      this.logger.info('Sign Done :)')
      this.logger.info('------------- signed result --------------')
      this.logger.info(`storage : ${chalk.red(signedFile)}`)
      this.logger.info(`md5     : ${chalk.red(signedFileMd5)}`)
      this.logger.info('------------------------------------------')
      await this.markAddrs(addrs)
    } catch (err) {
      this.logger.error(`address validate error ${err.message}`)
    }
  }
}
