import sequelize from 'sequelize'
const { DataTypes } = sequelize

export const ATTRIBUTES = {
  address: {
    type: DataTypes.STRING,
    unique: true
  },
  private_key: {
    type: DataTypes.TEXT,
    allowNull: false,
    defaultValue: ''
  },
  public_key: {
    type: DataTypes.TEXT,
    allowNull: false,
    defaultValue: ''
  },
  status: {
    type: DataTypes.TINYINT,
    allowNull: false,
    defaultValue: 0
  }
}
