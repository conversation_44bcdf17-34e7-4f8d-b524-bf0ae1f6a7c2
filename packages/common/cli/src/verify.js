import inquirer from 'inquirer'
import { Aes } from '@common/crypto'
import { Model } from './model.js'
import { Logger } from './logger.js'
import {
  pwdValidate,
  isFile,
  newProgressBar
} from './utils.js'

export class Verify {
  constructor ({ Account, dbPath }) {
    this.dbPath = dbPath
    this.Account = Account
    this.logger = new Logger()
    this.logger.activeConsole()
    this.model = new Model({ storage: this.dbPath })
  }

  async validateAccount (address, encryptPrivKey, password, publicKey) {
    const { Account } = this
    const privateKey = Aes.decrypt(encryptPrivKey, password, '')
    const account = new Account(privateKey, publicKey, address)
    return account.hbValidate()
  }

  async getValidateInfo () {
    const { pwd } = await inquirer.prompt([
      {
        type: 'password',
        name: 'pwd',
        mask: '*',
        message: 'input password',
        validate: pwdValidate
      }
    ])

    return {
      password: pwd
    }
  }

  async verify () {
    try {
      if (!this.dbPath || !isFile(this.dbPath)) {
        throw new Error('The path to storage account is not right')
      }
      await this.model.sync()
      const { password } = await this.getValidateInfo()
      const total = await this.model.count()
      if (total === 0) {
        throw new Error('there is no account.')
      }
      const bar = newProgressBar('Validating [:bar] :current/:total ', total)
      const res = await this.model.findAll()
      for (let i = 0; i < res.length; i++) {
        const temp = res[i]
        if (!(await this.validateAccount(temp.address, temp.private_key, password, temp.public_key))) {
          this.logger.error(`verify failed! address: ${temp.address}`)
          return
        }
        bar.tick()
      }
      this.logger.done(`Total ${total} addresses has been verified.`)
    } catch (err) {
      this.logger.error(`address verify error ${err.message}`)
    }
  }
}
