import fs from 'fs'
import ProgressBar from 'progress'

const PWD_MIN_LEN = 8
const PWD_MAX_LEN = 24
const NUM_REGEX = /^[0-9]+$/
const PWD_REGEX = new RegExp(`^[0-9A-Za-z!#$%&'()+,-./:;<=>?@[\\]^_\`{|}~*]{${PWD_MIN_LEN},${PWD_MAX_LEN}}$`)

export const intValidate = input => {
  if (!NUM_REGEX.test(input)) {
    return 'Input must be a integer'
  }
  return true
}

export const pwdValidate = input => {
  if (!PWD_REGEX.test(input)) {
    return `Password must contain alphanumeric characters, and supports special characters. The length of password varies from ${PWD_MIN_LEN} to ${PWD_MAX_LEN}`
  }
  return true
}

export const newProgressBar = (template, count) => {
  return new ProgressBar(template, { total: Number(count) })
}

export const isDirectory = filePath => {
  try {
    const stats = fs.statSync(filePath)
    return stats.isDirectory()
  } catch (err) {
    return false
  }
}

export const isFile = filePath => {
  try {
    const stats = fs.statSync(filePath)
    return stats.isFile()
  } catch (err) {
    return false
  }
}
