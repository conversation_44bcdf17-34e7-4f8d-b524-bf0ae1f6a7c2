import fs from 'fs'
import path from 'path'
import { Model } from './model.js'
import { Logger } from './logger.js'
import {
  isFile,
  isDirectory,
  newProgressBar
} from './utils.js'

export class Export {
  constructor ({ chain, dbPath, logPath, exportPath }) {
    this.chain = chain
    this.dbPath = dbPath
    this.exportPath = exportPath
    this.logger = new Logger(logPath)
    this.logger.activeConsole()
    this.logger.activeFile()
    this.model = new Model({ storage: this.dbPath })
  }

  async export () {
    try {
      if (!this.dbPath || !isFile(this.dbPath)) {
        throw new Error('The path to storage account is not right')
      }
      if (!this.exportPath || !isDirectory(this.exportPath)) {
        fs.mkdirSync(path.resolve(this.exportPath), { recursive: true })
      }
      await this.model.sync()
      const res = await this.model.findAll()
      if (res.length === 0) {
        throw new Error('there is no account.')
      }
      const exportFilepath = `${this.exportPath}/${this.chain}_export_address.txt`
      const exportStream = fs.createWriteStream(exportFilepath)
      const bar = newProgressBar('Exporting [:bar] :current/:total ', res.length)
      for (let i = 0; i < res.length; i++) {
        const temp = res[i]
        exportStream.write(`${temp.address}\n`, 'UTF8')
        bar.tick()
      }
      exportStream.end()
      this.logger.done(`Total ${res.length} addresses has been exported.`)
    } catch (err) {
      this.logger.error(`address export error ${err.message}`)
    }
  }
}
