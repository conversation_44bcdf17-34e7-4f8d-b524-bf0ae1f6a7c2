import { Model } from './model.js'
import { Logger } from './logger.js'
import {
  isFile
} from './utils.js'

export class Cursor {
  constructor ({ dbPath }) {
    this.dbPath = dbPath
    this.logger = new Logger()
    this.logger.activeConsole()
    this.model = new Model({ storage: this.dbPath })
  }

  async cursor () {
    try {
      if (!this.dbPath || !isFile(this.dbPath)) {
        throw new Error('The path to storage account is not right')
      }
      await this.model.sync()
      const previousList = await this.model.findAll({ where: { status: 1 }, order: [['id', 'DESC']], limit: 1 })
      const currentList = await this.model.findAll({ where: { status: 0 }, order: [['id', 'ASC']], limit: 1 })
      this.logger.done(`Previous Used: ${previousList[0] ? previousList[0]?.address : 'No used addresses yet'}`)
      this.logger.done(`Current Using: ${currentList[0] ? currentList[0]?.address : 'The address is empty or has been used up'}`)
    } catch (err) {
      this.logger.error(`address cursor error ${err.message}`)
    }
  }
}
