import inquirer from 'inquirer'
import { A<PERSON> } from '@common/crypto'
import { Model } from './model.js'
import { Logger } from './logger.js'
import uuid from 'uuid'
import {
  pwdValidate,
  intValidate,
  newProgressBar
} from './utils.js'

export class Generate {
  constructor ({ Account, dbPath, logPath }) {
    this.dbPath = dbPath
    this.Account = Account
    this.logger = new Logger(logPath)
    this.logger.activeConsole()
    this.logger.activeFile()
    this.model = new Model({ storage: this.dbPath })
  }

  async generateAccount (password) {
    const { Account } = this
    const account = await new Account()
    return {
      address: account.address,
      public_key: account.publicKey,
      private_key: Aes.encrypt(account.privateKey, password)
    }
  }

  async getGenerateInfo () {
    const { count, pwd } = await inquirer.prompt([
      {
        type: 'input',
        name: 'count',
        message: 'input generate address number',
        default: 2000,
        validate: intValidate
      },
      {
        type: 'password',
        name: 'pwd',
        mask: '*',
        message: 'input password',
        validate: pwdValidate
      },
      {
        type: 'password',
        name: 'pwdConfirm',
        mask: '*',
        message: 'confirm password',
        validate (input, answers) {
          const r = pwdValidate(input)
          if (r !== true) {
            return r
          }
          if (input !== answers.pwd) {
            return 'password not match the previous input'
          }
          return true
        }
      }
    ])

    return {
      count,
      password: pwd
    }
  }

  async generate () {
    try {
      await this.model.sync()
      const { password, count } = await this.getGenerateInfo()
      const bar = newProgressBar('Generating [:bar] :current/:total ', count)
      const accounts = []
      for (let i = 0; i < count; i++) {
        const account = await this.generateAccount(password)
        account.address = account.address ? account.address : `tmp-addr-${uuid.v1()}`
        accounts.push(account)
        bar.tick()
      }
      await this.model.bulkCreate(accounts)
      this.logger.done(`Total ${count} addresses has been generated.`)
    } catch (err) {
      this.logger.error(`addresses create error ${err.message}`)
    }
  }
}
