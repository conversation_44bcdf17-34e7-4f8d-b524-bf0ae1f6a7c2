import { mixin, multi, divide, checkAws4QueryAuth } from '../src/index.js'

describe('snippet test', () => {
  it('mixin', () => {
    class A {
      listen () {
        return true
      }
    }
    class B {
      speak () {
        return false
      }
    }
    class C extends mixin(A, B) {}
    const instance = new C()

    expect(instance.listen()).toBe(true)
    expect(instance.speak()).toBe(false)
  })

  it('multi', () => {
    const res = multi(2, 4)
    expect(res.toNumber()).toBe(8)
  })

  it('divide', () => {
    const res = divide(4, 2)
    expect(res.toNumber()).toBe(2)
  })

  describe('checkAws4QueryAuth test', () => {
    const data = {
      id: 1,
      platform_id: 1,
      asset_name: 'algo',
      business_type: 2,
      encrypt_data: 'KqcJxJQlVjOeO3EMeAYYjZzvAQ0lFkElGSMa0mIw/c+nRDwQMsiGjCh3B8Gdywv83tXJky+zWQcvq/GQXlbE328wGLQappmLpL7zl4lfiAWvTVZPp5Kf1VVRKKqNg7iWUeuVcQi4D7pFzlXVW6gtMjTt0IuaZvvD1YOA6HM5yF6mGYIStn8rJbASy47b/SwDYZmFQSGlJDMbbZckJJ2UTvrAQrja3uemVAFX63W0r8z2y3Dc8X0XjoKMtQC0cE7oCW0YR/pfnyUTpVIhFp4A1VqgzWdXGT8MARp//yakiGcnk/2x5CV0vNKsbcrBycGMKKUV7cutx1HoNmfCn4o2XGf+NkS++UVxMN39IeiiBaCGlSQFZiACM37rCvFMQ8GolT0MeN/FbbR2CRaEhfy9LX5fsc+5ogcR8kKIPceMcWt1z3DxGoOXwx0qWeVKaKa5cBaVHy4RaapD316Pwtj/tpx5sM+2Qpe916x+ocpF8ApiLHjQcSW+paMjr57DK0zd136iHAztflfrdhJOQ4mfcmt8S0ENtroucXeBAEJnAwGzYWzp0xdvNG4xI3eCnVGM',
      cipher_key: 'JZNg8D0FV0hfBrqEosMYBJO5fE86UieCndrTSxsXrfcc7KFJJlEf0Dnv6ZX91mkIN0YB9WzDxYSi6Bg8GipqCEfIedvFFUl2ovWV4kK2l0ygQDp3tjoVDXGHm/CovoYaH6PvPz1tExXnlHaX7kj2ufLKA1/+6k5kajmTwVDdAELCJgcGWWS9KrJVP5mQdP3TT7O/rf0kWvVG6vx2qEZ/Tyt1hb7P+dUr9dpxRYhBqjVxN/1DYg0PqxEQ0altbTbOoapcAEMfxWco1JYK4epDwwqR8GVIBOp0Qz77xP9UskaJGap0IeVrIuGDVYFK3L+CxCj6HotruSvTKnybU81UfJ9oRG3ay3MmYyOUMpNAc+jNVEEe/5iejojAvpUR2742eeCFOnc1+LxphEJHTtSgoxQW6B5acE9Bhh9+Sv3kaBv4LqFE/iE+coN4bUrk5h4ESnDkBuOtjxAz26afq+h6nwTXdfEpAaO3SIh7mXRX3x9G7/Hs01TMOxhyCqvo+tlniq5OrjHrkWWR5iDbLTD+nQKCfffHRbXZgaI1wxtCkIJ3MPDskf5q+z7Aei8oVjsf1CIP/W99MxqOwjIhfcz5dsYYggRrLAuc7/kgm7IGWwrEtjfBZnp6s3McIFCFI4vCcoc3Zvz6ZThkvRfiuoT/kHFRMPTo77hwa34l35/bSCs='
    }
    const date = '20190703'
    const datetime = `${date}T034244Z`
    const awsKeys = [{ appid: 'validator', appkey: '12345678' }]

    it('with expires', () => {
      const query = {
        'X-Amz-Expires': 10000000000000,
        'X-Amz-Date': datetime,
        'X-Amz-Algorithm': 'AWS4-HMAC-SHA256',
        'X-Amz-Credential': `validator/${date}/us-east-1/sign/aws4_request`,
        'X-Amz-SignedHeaders': 'content-type;host',
        'X-Amz-Signature': 'bb8fb5f1e10fc619de587dbde914a432b5885473e3ad84651ea673a91fdeef63'
      }

      const res = checkAws4QueryAuth({
        path: `/sign/ae?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=validator%$${date}%2Fus-east-1%2Fsign%2Faws4_request&X-Amz-Date=${datetime}&X-Amz-Expires=10000000000000&X-Amz-SignedHeaders=content-type%3Bhost`,
        query,
        headers: {
          host: 'sign.us-east-1.amazonaws.com',
          'content-type': 'application/json'
        },
        body: JSON.stringify(data)
      },
      awsKeys
      )

      expect(res).toBe(true)
    })

    it('without expires', () => {
      const query = {
        'X-Amz-Date': datetime,
        'X-Amz-Algorithm': 'AWS4-HMAC-SHA256',
        'X-Amz-Credential': `validator/${date}/us-east-1/sign/aws4_request`,
        'X-Amz-SignedHeaders': 'content-type;host',
        'X-Amz-Signature': '628f3bc29c828eeba6a3f4386ce4234a94c50fda5492bade50c5862fb4565d29'
      }

      const res = checkAws4QueryAuth({
        path: `/sign/ae?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=validator%$${date}%2Fus-east-1%2Fsign%2Faws4_request&X-Amz-Date=${datetime}&X-Amz-SignedHeaders=content-type%3Bhost`,
        query,
        headers: {
          host: 'sign.us-east-1.amazonaws.com',
          'content-type': 'application/json'
        },
        body: JSON.stringify(data)
      },
      awsKeys
      )

      expect(res).toBe(true)
    })

    it('by headers', () => {
      const res = checkAws4QueryAuth({
        path: '/validate',
        query: {},
        headers: {
          host: 'validator.blockchain.amazonaws.com',
          'content-type': 'application/json',
          'content-length': '1457',
          'x-amz-date': '20200110T082616Z',
          authorization: 'AWS4-HMAC-SHA256 Credential=validator/20200110/blockchain/validator/aws4_request, SignedHeaders=content-length;content-type;host;x-amz-date, Signature=ae6dd199defbe2daec61d22ad22d8f292dac7a68ebc4a1ed670008251383e429'
        },
        body: '{"id":1,"platform_id":1,"asset_id":1,"asset_name":"zil1","business_type":2,"encrypt_data":"Jrv8ojvdDXervxQVEClKZPQ5B4TvmhfwEFoefMNu+77Ov0uoJJq6CsQjogq716LPy7lcDMZIvAwXqivw0glomdR+ENu0/5/UcHFM1ywn6XYRQ0JvnVclSqppd49twE7QhawqaYfYnuqUWuG/Nf7Q7wfcsNjJySjivNXU97xydK5z/lLfyz9ncplCuhEJQ0ddiZE9f1hWFyfvgv32Cpa+KNmqZlzt214H3VwbHIoChs9dca6wXgEf1A5K9lYJFPk6fqbHz0bWpCqp92OsBHSd+9JleeuEkAGJRyC6K8RjY3AZvwncwagEBwyhRXoiaL+0RoEeGzfM2G7PNcFxECcWW575HoegyUgueCsv1OIXFbJsiBL7uVuo79+cpfHiI2J1ma1f+FGmaMc1dGqQUTLHTndVvhDK7HArx2XYSK56nlMy+qNyUt4g5/9PcoR6DbHNcTg71yDgdOtEGbrrKNlQKk6/u28nL6Bz5jlgTitG9JPMedSPvpe787wrQatPAtp/p6g0cGgMabCodI+ok3XaepIOgOPl0LVfIe7UnW9jBMNZCFyasYR2Vj0CPT8gxxIZvJigPDsZ+kWTmQPkiNSYs5mQvX7+frXbaQZoL4F5YrSfeONb2wiXI2ajkfMV+S7XL/oE/Q5V+vLq4yjSSh4X5w==","cipher_key":"RoecRhBHFvVL0g9ZeVn04wxv7i+0OgqwzO2JhYlfPLHIdi5tK3KnGYr7+SD61Tj345bFY1HTyBNRS9KLb348z2HagP3X+HnWlhmpi5DpoFdVrtU7Bi1cMZQ4sa+SxnTovxGGs7t8loA8apZ5uVeScE6FhESQzwBJaxDplR7J9t72IgfmyPWClThgzn7b03jO7Mr7LoYNyn/8iOFFG8zOZGJZpHcZ6oDB8Fi1Bw9mR2tsL9sfhXIYidRGLiAORZ4sy7e5mmi9bdl4fjO2+t1Nt/Qad8xpBD+yI3p9A3R0S+mLHH7LJ26Yl2eNcVJKOJxKxDYE7ibtmlgCpwBGsDhF+vOjI7EeBluRZe2zIuts24oTQ/84oy4SLPlxG7OjeCMboLTu+oI+wQzuEZiolAwddc+MKAgoo5xbFDCEXOwtdSekW5ow79ds4K6BpzLc+s+67KUAW6UXNFDHSfMJQHjdcMj5G7MZn7q9dhvhE/7u9pL7o5kJq8ZXUbsDnUbtG6yp9BpazfCdOJpeGnAHc2ZUQP61HwNRIq7Ggy0XXPjZp0y+bA2/QTK5p7VowmOjZmVaCdUoC0jzuozrUdj5Uj+FcjDBQaqJQeiTOSoCDx7ND6Aq+QYN8EC9EQLCNFaloKAwP8U2NxumGfCjSbR3LuY9n7NEZzBWsiDw2cAyieswZJ4="}'
      },
      awsKeys
      )

      expect(res).toBe(true)
    })
  })
})
