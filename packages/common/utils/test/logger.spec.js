import { Logger } from '../src/index.js'

describe('logger test', () => {
  it('default logger', () => {
    const logger = new Logger()

    expect(logger).toHaveProperty('info')
    expect(logger).toHaveProperty('error')
    expect(logger).toHaveProperty('warn')
    expect(logger).toHaveProperty('trace')
    expect(logger.info).not.toBe(undefined)
    expect(logger.error).not.toBe(undefined)
    expect(logger.warn).not.toBe(undefined)
    expect(logger.trace).not.toBe(undefined)
  })

  it('signer logger', () => {
    const logger = new Logger({
      name: 'signer',
      file: 'signer.log'
    })

    expect(logger).toHaveProperty('info')
    expect(logger).toHaveProperty('error')
    expect(logger).toHaveProperty('warn')
    expect(logger).toHaveProperty('trace')
    expect(logger.info).not.toBe(undefined)
    expect(logger.error).not.toBe(undefined)
    expect(logger.warn).not.toBe(undefined)
    expect(logger.trace).not.toBe(undefined)
  })

  it('validator logger', () => {
    const logger = new Logger({
      name: 'validator',
      file: './validator.log'
    }, {
      enabled: false
    }, {
      env: 'test',
      group: 'validator'
    })

    expect(logger).toHaveProperty('info')
    expect(logger).toHaveProperty('error')
    expect(logger).toHaveProperty('warn')
    expect(logger).toHaveProperty('trace')
    expect(logger.info).not.toBe(undefined)
    expect(logger.error).not.toBe(undefined)
    expect(logger.warn).not.toBe(undefined)
    expect(logger.trace).not.toBe(undefined)
  })
})
