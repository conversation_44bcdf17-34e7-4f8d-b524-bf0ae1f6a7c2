import fs from 'fs'
import path from 'path'
import { SignerModel, ValidatorModel } from '../src/index.js'

const { existsSync } = fs
const { join } = path
const executeValidator = process.env.NODE_ENV === 'gitlab'

describe('signer cipher model', () => {
  const storage = './config/wallet.db'
  const cipher = 'HelloWorld'
  const signerModel = new SignerModel({ storage, cipher })
  const address = '0x88888888'
  const addressUpdated = '0x66666666'

  beforeAll(async () => {
    await signerModel.sync()
  })

  afterAll(async () => {
    signerModel.cipherDb.close()
    await signerModel.sequelize.close()
  })

  it('db exist', () => {
    const dbPath = join(process.cwd(), storage)

    expect(existsSync(dbPath)).toBe(true)
  })

  it('lock and checkLock when don\'t encrypt before', async () => {
    expect(await signerModel.checkLock()).toBe(false)

    signerModel.lock()

    expect(await signerModel.checkLock()).toBe(true)
  })

  it('unLock', () => {
    // 理论上，如果资源未释放，db 未关闭，前面上锁后，这里无需解锁
    expect(() => { signerModel.unLock() }).not.toThrow()
  })

  it('insert', () => {
    const stmt = signerModel.cipherDb.prepare('INSERT INTO keystore(address) VALUES (?)')
    const { changes } = stmt.run(address)

    expect(changes).toBe(1)
  })

  it('update', () => {
    const stmtInsert = signerModel.cipherDb.prepare('UPDATE keystore SET address = ? WHERE address = ?')
    const { changes } = stmtInsert.run(addressUpdated, address)

    expect(changes).toBe(1)
  })

  it('select', () => {
    const stmt = signerModel.cipherDb.prepare('SELECT * FROM keystore WHERE address = ?')
    const info = stmt.all(addressUpdated)

    expect(info.length).toBe(1)
  })

  it('delete', () => {
    const stmtInsert = signerModel.cipherDb.prepare('DELETE FROM keystore WHERE address = ?')
    const { changes } = stmtInsert.run(addressUpdated)

    expect(changes).toBe(1)
  })

  it('rmLock and checkLock', async () => {
    signerModel.rmLock()
    expect(await signerModel.checkLock()).toBe(false)
  })
});

(executeValidator ? describe : describe.skip)('validator model', () => {
  const validatorModel = new ValidatorModel({
    host: 'mysql',
    user: 'root',
    password: 'test_password',
    database: 'validator'
  })
  const chainName = 'test'
  const rawTx = '0x88888888'
  const rawTxUpdated = '0x66666666'

  afterAll(async () => {
    await validatorModel.sequelize.close()
  })

  it('init tables and check', async () => {
    await validatorModel.init([chainName])
    const chainTable = await validatorModel.sequelize.query(`SHOW TABLES LIKE '${chainName}_%'`, { type: 'SHOWTABLES' })
    const otherTable = await validatorModel.sequelize.query('SHOW TABLES LIKE \'whitelist\'', { type: 'SHOWTABLES' })

    expect(chainTable).toEqual(['test_address', 'test_raw_tx', 'test_task'])
    expect(otherTable).toEqual(['whitelist'])
  })

  it('insert', async () => {
    const [, changes] = await validatorModel.sequelize.query(`INSERT INTO ${chainName}_raw_tx(raw_tx) VALUES (?)`, { replacements: [rawTx], type: 'INSERT' })
    expect(changes).toBe(1)
  })

  it('update', async () => {
    const [, changes] = await validatorModel.sequelize.query(`UPDATE ${chainName}_raw_tx SET raw_tx = ? WHERE raw_tx = ?`, { replacements: [rawTxUpdated, rawTx], type: 'UPDATE' })
    expect(changes).toBe(1)
  })

  it('select', async () => {
    const res = await validatorModel.sequelize.query(`SELECT * FROM ${chainName}_raw_tx WHERE raw_tx = ?`, { replacements: [rawTxUpdated], type: 'SELECT' })
    expect(res.length).toBeGreaterThanOrEqual(1)
  })

  it('delete', async () => {
    const res = await validatorModel.sequelize.query(`DELETE FROM ${chainName}_raw_tx WHERE raw_tx = ?`, { replacements: [rawTxUpdated], type: 'DELETE' })
    expect(res).toEqual(undefined)
  })
})
