import sequelize from 'sequelize'
const { DataTypes } = sequelize

export const VALIDATOR_LOGGER_STRUCT = {
  env: 'dev',
  group: 'unstable',
  devlang: 'javascript',
  platform: 'undefined',
  chain: 'undefined',
  asset: 'undefined',
  message: 'undefined',
  stack: 'undefined',
  ctx: {}
}

export const ENCRYPTED_ERR = 'SQLITE_NOTADB'

export const SEQUELIZE_OPTIONS = {
  dialect: 'mysql',
  logging: false, // 无论何时，禁用日志模式。调试期间，临时改为 true，将在终端输出每条 SQL
  define: {
    timestamps: false, // 无论何时，禁止额外字段查询返回
    freezeTableName: true
  },
  query: {
    raw: true
  }
}

export const METADATA_ATTRIBUTES = {
  chain: {
    primaryKey: true,
    type: DataTypes.STRING,
    unique: true,
    allowNull: false
  },
  spec_version: {
    type: DataTypes.INTEGER,
    unique: false,
    allowNull: false
  },
  metadata: DataTypes.TEXT
}

export const SIGNER_ATTRIBUTES = {
  address: {
    type: DataTypes.STRING,
    unique: true
  },
  coin: {
    type: DataTypes.STRING,
    allowNull: false,
    defaultValue: ''
  },
  private_key: {
    type: DataTypes.TEXT,
    allowNull: false,
    defaultValue: ''
  },
  public_key: {
    type: DataTypes.TEXT,
    allowNull: false,
    defaultValue: ''
  },
  platform: {
    type: DataTypes.TEXT,
    allowNull: false,
    defaultValue: ''
  }
}

export const TASK_TABLE = chain => `CREATE TABLE IF NOT EXISTS ${chain}_task (
  id            INT UNSIGNED     NOT NULL AUTO_INCREMENT PRIMARY KEY,
  task_id       VARCHAR(255)     NOT NULL,
  tx_id         INT UNSIGNED     NOT NULL,
  platform_id   CHAR(10)         NOT NULL,
  tx_type       VARCHAR(64)      NOT NULL,
  task_type     VARCHAR(64)      NOT NULL,
  raw_tx_id     INT UNSIGNED     NOT NULL,
  result        VARCHAR(128),
  created       TIMESTAMP        NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated       TIMESTAMP        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX (task_id),
  INDEX (tx_id),
  INDEX (task_id, tx_type, task_type, platform_id),
  INDEX (raw_tx_id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8`

export const RAW_TX_TABLE = chain => `CREATE TABLE IF NOT EXISTS ${chain}_raw_tx (
  id      INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
  raw_tx  TEXT         NOT NULL,
  created TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE = InnoDB DEFAULT CHARSET = utf8`

export const ADDRESS_TABLE = chain => `CREATE TABLE IF NOT EXISTS ${chain}_address (
  id           INT UNSIGNED     NOT NULL AUTO_INCREMENT PRIMARY KEY,
  address      VARCHAR(255)     CHARACTER SET utf8 COLLATE 'utf8_bin' NOT NULL,
  address_type INT(10) UNSIGNED NOT NULL COMMENT '1: external, 4: user, 16: sys, 64: ow, 256: cold, 1024: vote',
  platform_id  CHAR(10)         NOT NULL,
  address_id   VARCHAR(100)     NOT NULL COMMENT '地址服务的地址id',
  created      TIMESTAMP        NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated      TIMESTAMP        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE (address),
  INDEX (address, platform_id)
) ENGINE = InnoDB DEFAULT CHARSET = utf8`

export const WHITELIST_TABLE = `CREATE TABLE IF NOT EXISTS whitelist (
  id         INT UNSIGNED     NOT NULL AUTO_INCREMENT PRIMARY KEY,
  raw_tx_id  INT              NOT NULL COMMENT "",
  chain      VARCHAR(100)     NOT NULL COMMENT "链名称",
  created    TIMESTAMP        NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated    TIMESTAMP        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX (raw_tx_id),
  UNIQUE (raw_tx_id, chain)
) ENGINE = InnoDB DEFAULT CHARSET = utf8`
