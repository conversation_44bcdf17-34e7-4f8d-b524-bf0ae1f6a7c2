import { isAbsolute, resolve } from 'path'
import aws4 from 'aws4'
import moment from 'moment'
import BigNumber from 'bignumber.js'

BigNumber.config({ EXPONENTIAL_AT: [-20, 20] })

const copyProperties = (target, source) => {
  for (const key of Reflect.ownKeys(source)) {
    if (key !== 'constructor' &&
      key !== 'prototype' &&
      key !== 'name'
    ) {
      const desc = Object.getOwnPropertyDescriptor(source, key)
      Object.defineProperty(target, key, desc)
    }
  }
}

export const mixin = (...mixins) => {
  class Mix {
    constructor () {
      for (const Mixin of mixins) {
        copyProperties(this, new Mixin()) // 拷贝实例属性
      }
    }
  }

  for (const mixin of mixins) {
    copyProperties(Mix, mixin) // 拷贝静态属性
    copyProperties(Mix.prototype, mixin.prototype) // 拷贝原型属性
  }

  return Mix
}

export const multi = (num1, num2) => {
  const bigNum1 = new BigNumber(num1)
  return bigNum1.times(num2)
}

export const divide = (num1, num2) => {
  const bigNum1 = new BigNumber(num1)
  return bigNum1.dividedBy(num2)
}

export const getAbsolutePath = (file) => isAbsolute(file) ? file : resolve(process.cwd(), file)

export const checkAws4QueryAuth = ({ headers, query, path, body, doNotModifyHeaders = true }, awsKeys = []) => {
  if (!awsKeys) {
    return false
  }

  const datetime = headers['x-amz-date'] || query['X-Amz-Date']
  const expires = headers['x-amz-expires'] || query['X-Amz-Expires']

  let signature; let credential; let signedHeaders; let signQuery = false
  if (headers.authorization) {
    const [x, y, z] = headers.authorization.split(', ')
    signature = z.split('=')[1]
    credential = x.split(' ')[1].split('=')[1]
    signedHeaders = y.split('=')[1]
  } else {
    signQuery = true
    signature = query['X-Amz-Signature']
    credential = query['X-Amz-Credential']
    signedHeaders = query['X-Amz-SignedHeaders']
    path = path.replace(/&X-Amz-Signature=[^&]+/g, '')
  }

  if (!credential || !signature || !datetime) { return false }

  headers = signedHeaders
    ? signedHeaders.split(';').reduce((result, key) => {
      result[key] = headers[key.toLowerCase()]
      return result
    }, {})
    : {}

  const [appid] = credential.split('/')
  if (!appid) { return false }

  const { appid: accessKeyId, appkey: secretAccessKey } = awsKeys.find(item => item.appid === appid)
  if (!accessKeyId || !secretAccessKey) { return false }

  const opts = { signQuery, headers, path, body, doNotModifyHeaders }
  const credentials = { accessKeyId, secretAccessKey }
  const signer = new aws4.RequestSigner(opts, credentials)
  signer.datetime = datetime // 时间闪回，必须设置，不然会默认为当前时间

  // 1. verify expires
  if (expires) {
    const currentTime = moment.utc()
    const givenTime = moment.utc(datetime, 'YYYYMMDDTHHmmss[Z]')
    if (currentTime.diff(givenTime, 'seconds') >= expires) {
      return false
    }
  }

  // 2. verify signature
  return signer.signature() === signature
}
