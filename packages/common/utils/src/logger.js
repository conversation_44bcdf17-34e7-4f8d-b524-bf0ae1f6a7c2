import { nameFromLevel, INFO, createLogger } from 'bunyan'
import KafkaStream from 'bunyan-nokafka'
import { getAbsolutePath } from './snippet.js'
import { VALIDATOR_LOGGER_STRUCT } from './constants.js'

class MagicKafkaStream extends KafkaStream {
  write (record) {
    const { hostname: host, msg: message, level, ...rest } = JSON.parse(record)
    super.write(JSON.stringify({ ...rest, host, message, level: nameFromLevel[level].toUpperCase() }))
  }
}

export class Logger {
  // 参数顺序(bunyan, kafka, { env, group })
  constructor ({ name = 'bunyan', file } = {}, { enabled = false, topic, broker_urls: brokerUrls } = {}, args = {}) {
    // rotating-file 默认配置为：level=info, period=1d, count=10
    const streams = file ? { streams: [{ type: 'rotating-file', path: getAbsolutePath(file) }] } : {}
    // 如果 args 存在，则将其与 validator 默认独有参数合并
    const custom = Object.keys(args).length ? { ...VALIDATOR_LOGGER_STRUCT, ...args } : args

    this.logger = createLogger({ name, ...streams, ...custom })

    if (enabled) this.loadKafka({ topic, brokerUrls })

    return this.logger
  }

  loadKafka ({ topic, brokerUrls }) {
    const kafkaStream = new MagicKafkaStream({
      topic,
      kafkaOpts: {
        connectionString: brokerUrls.toString(),
        partitioner: KafkaStream.roundRobinPartitioner()
      }
    })

    kafkaStream.on('ready', () => {
      this.logger.addStream({ level: INFO, stream: kafkaStream }, INFO)
    })
  }
}
