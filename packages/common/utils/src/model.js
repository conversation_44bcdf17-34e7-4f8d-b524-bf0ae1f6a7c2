import { Sequelize } from 'sequelize'
import Database from 'better-sqlite3-multiple-ciphers'
import { SEQUELIZE_OPTIONS, METADATA_ATTRIBUTES, SIGNER_ATTRIBUTES, TASK_TABLE, RAW_TX_TABLE, ADDRESS_TABLE, WHITELIST_TABLE, ENCRYPTED_ERR } from './constants.js'
import { mixin } from './snippet.js'

export class BaseModel {
  constructor (options) {
    this.mixinBaseModel(options)
  }

  mixinBaseModel (options) {
    this.sequelize = new Sequelize({ ...SEQUELIZE_OPTIONS, ...options })
  }
}

class Model extends BaseModel {
  constructor (storage) {
    super({ dialect: 'sqlite', storage })
  }

  mixinModel (storage) {
    super.mixinBaseModel({ dialect: 'sqlite', storage })
  }

  async sync () {
    await this.sequelize.sync({ alter: true })
  }
}

class ModelCipher {
  mixinModelCipher ({ storage = 'sequelize.db', cipher }) {
    const db = new Database(storage)
    db.pragma('cipher=\'aes256cbc\'')
    this.cipherDb = db
    this.cipher = cipher
  }

  lock () {
    try {
      this.cipherDb.rekey(Buffer.from(this.cipher))
    } catch (err) {
      this.errorHandler(err)
      console.error('please don\'t reLock, it doesn\'t work')
    }
  }

  unLock () {
    this.cipherDb.key(Buffer.from(this.cipher))
  }

  rmLock () {
    try {
      this.cipherDb.key(Buffer.from(this.cipher))
      this.cipherDb.rekey(Buffer.from(''))
    } catch (err) {
      this.errorHandler(err)
      console.error('unfortunately, the cipher is wrong')
    }
  }

  errorHandler ({ code }) {
    if (code !== 'SQLITE_NOTADB') { console.error(`unexpected error: ${code}`) }
  }
}

export class MetaModel extends Model {
  constructor ({ name = 'metadata', storage = `${name}.sqlite3`, attributes = METADATA_ATTRIBUTES } = {}) {
    super(storage)
    return this.sequelize.define(name, attributes) // 理论上 constructor 中不应该 return，此处为便捷，将 Meta Sequelize 直接返回修改原型实例
  }
}

export class SignerModel extends mixin(Model, ModelCipher) {
  constructor ({ name = 'keystore', storage = 'wallet.db', attributes = SIGNER_ATTRIBUTES, cipher }) {
    super() // 多态继承模式下，会忽略 constructor，所以传参无效，需要下面手动挡 mixin
    this.mixinModel(storage)
    this.mixinModelCipher({ storage, cipher })
    this.sequelize.define(name, attributes)
  }

  async checkLock () {
    try {
      await this.sync()
    } catch (err) {
      if (err.original.code === ENCRYPTED_ERR) return true
      else throw new Error(err)
    }
    return false
  }
}

export class ValidatorModel extends BaseModel {
  constructor (dialectOptions) {
    super({ dialectOptions })
  }

  async init (chains) {
    for (const chain of chains) {
      await this.sequelize.query(TASK_TABLE(chain))
      await this.sequelize.query(RAW_TX_TABLE(chain))
      await this.sequelize.query(ADDRESS_TABLE(chain))
    }
    await this.sequelize.query(WHITELIST_TABLE)
  }
}
export class AddressServiceModel extends BaseModel {
  constructor (dialectOptions) {
    super({ dialectOptions })
  }
}
