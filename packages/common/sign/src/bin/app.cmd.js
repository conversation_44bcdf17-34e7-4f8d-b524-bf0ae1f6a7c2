import program from 'commander'
import inquirer from 'inquirer'
import { SignerModel as BaseModel, <PERSON><PERSON> as BaseLogger } from '@common/utils'
import { BaseAccount as BaseCommand } from '../module/account.js'
import { BaseConfig } from '../module/read.conf.js'
import { cipherQuestions } from '../module/constants.js'
import { dirExist } from '../module/utils.js'

export class BaseAppCmd {
  constructor () {
    this.chain = ''
    this.CommandCls = BaseCommand
    this.ModelCls = BaseModel
    this.ConfigCls = BaseConfig
    this.LoggerCls = BaseLogger
    this.enableCmds = {
      generate: {
        cmd: 'generate',
        desc: 'Generate KeyPair'
      },
      validate: {
        cmd: 'validate',
        desc: 'Validate KeyPair'
      },
      export: {
        cmd: 'export',
        desc: 'Export Address'
      },
      smsg: {
        cmd: 'sign_msg',
        desc: 'sign message'
      },
      sqlite: {
        cmd: 'sqlite',
        desc: 'Sqlite Tools'
      },
      addEncryption: {
        cmd: 'addEncryption',
        desc: 'Add encryption'
      },
      delEncryption: {
        cmd: 'delEncryption',
        desc: 'Delete encryption'
      },
      checkEncryption: {
        cmd: 'checkEncryption',
        desc: 'Check encryption'
      },
      changeSignerPassword: {
        cmd: 'changeSignerPassword',
        desc: 'Change password of encrypted private_key'
      }
    }

    this.init()
  }

  /**
   * implInit interface for child class (Factory Mode)
   */
  init () {
    throw new Error(`it is init interface!,${this.chain}`)
  }

  async startCommand (configPath, cmd) {
    // config module
    const config = new this.ConfigCls(configPath)
    const chain = config.chain || this.chain
    const dbChain = config.dbChain || chain

    // logger module
    const logger = console

    // model module
    const { cipher } = await inquirer.prompt(cipherQuestions)
    const walletdbDir = `${config.signer.db.dbPath}/${dbChain}`
    dirExist(walletdbDir, true)
    const storage = `${config.signer.db.dbPath}/${dbChain}/${config.signer.db.dbName}.db`
    const Model = this.Model ? this.Model : this.ModelCls // 对之前命名兼容
    const model = new Model({ name: config.signer.db.tableName, storage, cipher })
    await model.checkLock()

    // command module
    const Command = this.Account ? this.Account : this.CommandCls // 对之前命名兼容
    const command = new Command({ model, chain, logger, config })
    command[cmd]()
  }

  start () {
    this.logger = console
    const self = this

    process.on('uncaughtException', e => {
      self.logger.error('uncaughtException', `Error: ${e.message}, Stack: ${e.stack}`)
      process.exit(1)
    })

    process.on('unhandledRejection', e => {
      self.logger.error('unhandledRejection', `Error: ${e.message}, Stack: ${e.stack}`)
      process.exit(1)
    })

    try {
      let validCmd = false
      Object.keys(this.enableCmds).forEach(k => {
        const command = this.enableCmds[k]
        program
          .command(command.cmd)
          .description(command.desc)
          .option('-c, --config <path>', 'Set config path. Defaults to ./config')
          .action(options => {
            self.startCommand(options.config, command.cmd)
            validCmd = true
          })
      })
      program.parse(process.argv)
      if (!validCmd) {
        program.help()
      }
    } catch (e) {
      self.logger.error('cmd start error:', `${e.message}, ${e.stack}`)
      process.exit(1)
    }
  }
}
