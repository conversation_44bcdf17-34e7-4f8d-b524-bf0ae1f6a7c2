import program from 'commander'
import { Logger as BaseLogger } from '@common/utils'
import { BaseTransaction } from '../module/transaction.js'
import { BaseServerOffer } from '../module/server.offer.js'
import { BaseConfig } from '../module/read.conf.js'

export class BaseAppServerOffer {
  constructor () {
    this.chain = ''
    this.TransactionCls = BaseTransaction
    this.ServerOfferCls = BaseServerOffer
    this.ConfigCls = BaseConfig
    this.LoggerCls = BaseLogger
    this.init()
  }

  /**
   * implInit interface for child class (Factory Mode)
   */
  init () {
    throw new Error(`it is init interface!,${this.chain}`)
  }

  async startServer (configPath) {
    // config module
    const config = new this.ConfigCls(configPath)
    const chain = config.chain || this.chain

    // logger module
    const logger = new this.LoggerCls(config.server.bunyan)
    this.logger = logger

    // transaction module
    const Transaction = this.Transaction ? this.Transaction : this.TransactionCls // 对之前命名兼容
    const transaction = new Transaction({ config, chain, logger, model: null })

    // server module
    const Server = this.Server ? this.Server : this.ServerOfferCls // 对之前命名兼容
    const server = new Server({ config, logger, transaction, chain })
    await server.start()
  }

  async start () {
    this.logger = console
    const self = this

    process.on('uncaughtException', e => {
      self.logger.error('uncaughtException', `Error: ${e.message}, Stack: ${e.stack}`)
      process.exit(1)
    })

    process.on('unhandledRejection', e => {
      self.logger.error('unhandledRejection', `Error: ${e.message}, Stack: ${e.stack}`)
      process.exit(1)
    })
    try {
      program.option('-c, --config <path>', 'Set config path. Defaults to ./config')
      program.parse(process.argv)
      await this.startServer(program.config)
    } catch (e) {
      self.logger.error('cmd start error:', `${e.message}, ${e.stack}`)
      process.exit(1)
    }
  }
}
