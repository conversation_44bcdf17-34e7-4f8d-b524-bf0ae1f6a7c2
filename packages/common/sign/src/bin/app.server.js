import program from 'commander'
import inquirer from 'inquirer'
import cluster from 'node:cluster'
import { SignerModel as BaseModel, <PERSON><PERSON> as BaseLogger } from '@common/utils'
import { BaseTransaction } from '../module/transaction.js'
import { BaseServer } from '../module/server.js'
import { BaseConfig } from '../module/read.conf.js'
import { cipherQuestions } from '../module/constants.js'
import { dirExist, unlock } from '../module/utils.js'
import { rotateRsaTps, scheduleRotateRsaTps } from '../module/rsa.tps.js'

export class BaseAppServer {
  constructor () {
    this.chain = ''
    this.TransactionCls = BaseTransaction
    this.ModelCls = BaseModel
    this.ServerCls = BaseServer
    this.ConfigCls = BaseConfig
    this.LoggerCls = BaseLogger
    this.init()
  }

  /**
   * implInit interface for child class (Factory Mode)
   */
  init () {
    throw new Error(`it is init interface!,${this.chain}`)
  }

  async startServer (configPath) {
    // config module
    const config = new this.ConfigCls(configPath)
    const chain = config.chain || this.chain
    const dbChain = config.dbChain || chain

    // logger module
    const logger = new this.LoggerCls(config.server.bunyan)
    this.logger = logger

    // model module
    const { cipher } = process.env.cipher ? { cipher: process.env.cipher } : await inquirer.prompt(cipherQuestions)
    if (!process.env.cipher) {
      process.send({ cipher })
    }

    const walletdbDir = `${config.signer.db.dbPath}/${dbChain}`
    dirExist(walletdbDir, true)
    const storage = `${config.signer.db.dbPath}/${dbChain}/${config.signer.db.dbName}.db`
    const Model = this.Model ? this.Model : this.ModelCls // 对之前命名兼容
    const model = new Model({ name: config.signer.db.tableName, storage, cipher })

    await unlock(model, true)
    // 签名机升级数据库新增字段platform，启动时检查该字段，防止老版签名机数据库未升级
    const res = await model.cipherDb.prepare(`select * from ${config.signer.db.tableName} limit 1;`).all([])
    const [data] = res
    if (data && !Object.keys(data).includes('platform')) {
      throw new Error(
        'The signature machine update adds a new platform field. ' +
        'Please execute sql to update the database table structure. ' +
        'The sql is as follows: (Note: please confirm the platform name again and again!!!)' +
        'ALTER TABLE keystore ADD platform TXT(50) DEFAULT \'your platform name\' NOT NULL;'
      )
    }

    // transaction module
    const Transaction = this.Transaction ? this.Transaction : this.TransactionCls // 对之前命名兼容
    const transaction = new Transaction({ config, chain, logger, model })

    // server module
    const Server = this.Server ? this.Server : this.ServerCls // 对之前命名兼容
    const server = new Server({ config, logger, transaction, chain })
    scheduleRotateRsaTps(config.server.cron || '0 0 1 * *', rotateRsaTps)
    await server.start()
  }

  async start () {
    this.logger = console
    const self = this

    process.on('uncaughtException', e => {
      self.logger.error('uncaughtException', `Error: ${e.message}, Stack: ${e.stack}`)
      process.exit(1)
    })

    process.on('unhandledRejection', e => {
      self.logger.error('unhandledRejection', `Error: ${e.message}, Stack: ${e.stack}`)
      process.exit(1)
    })

    try {
      if (cluster.isPrimary) {
        cluster.fork()
        // 只能用cluster监听exit 因为worker会出现进程退出的情况
        cluster.on('exit', (worker, code, signal) => {
          console.error(`Signer worker ${worker.process.pid} exited, exit code: ${code}, signal: ${signal}`)
          if (signal !== 'SIGINT') {
            cluster.fork()
          }
        })

        cluster.on('message', (worker, msg) => {
          if (msg.cipher && !process.env.cipher) {
            process.env.cipher = msg.cipher
          }
          if (msg.pwd1 && !process.env.pwd1) {
            process.env.pwd1 = msg.pwd1
          }
          if (msg.secret && !process.env.secret) {
            process.env.secret = msg.secret
          }
          if (msg.enTotp && !process.env.enTotp) {
            process.env.enTotp = msg.enTotp
          }
          if (msg.disTotp && !process.env.disTotp) {
            process.env.disTotp = msg.disTotp
          }
        })

        cluster.on('fork', (worker) => {
          console.log(`Signer worker ${worker.process.pid} forked`)
        })
      } else {
        program.option('-c, --config <path>', 'Set config path. Defaults to ./config')
        program.parse(process.argv)
        await this.startServer(program.config)
      }
    } catch (e) {
      self.logger.error('cmd start error:', `${e.message}, ${e.stack}`)
      process.exit(1)
    }
  }
}
