import { AesTps, RsaTps } from '@common/crypto'
import cron from 'node-cron'
import { gzipSync } from 'zlib'

const RsaTpsMap = { previous: null, current: null, isRotating: false }

export function rotateRsaTps () {
  if (RsaTpsMap.current) {
    RsaTpsMap.previous = { ...RsaTpsMap.current }
  }
  RsaTpsMap.isRotating = true
  const { publicKey, privateKey, password } = RsaTps.generate() // 这个比较耗时
  RsaTpsMap.current = { publicKey, privateKey, password }
  RsaTpsMap.isRotating = false
}

export function getRsaTps () {
  if (RsaTpsMap.isRotating) {
    return RsaTpsMap.previous
  } else {
    return RsaTpsMap.current
  }
}

export function scheduleRotateRsaTps (cronExpression, taskExecutor) {
  cron.schedule(cronExpression, () => {
    taskExecutor()
  }, {
    scheduled: true,
    timezone: 'Asia/Shanghai',
    runOnInit: true
  })
}

/**
 * 获取TxKey
 * @param {string} privateKey
 * @param {string} password
 * @param {string} shareKey 通过ECDH生成的共享密钥
 * @returns
 */
export function getTxKey (privateKey, password, shareKey) {
  const deflatePrivateKey = gzipSync(privateKey)
  const data = deflatePrivateKey.toString('base64') + password
  return AesTps.encrypt(data, shareKey)
}
