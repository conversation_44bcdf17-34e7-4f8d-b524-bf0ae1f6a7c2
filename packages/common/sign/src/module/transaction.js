import { Aes, AesSign, Rsa, RsaTps, ECDH } from '@common/crypto'
import { REQ_ERRORS } from './constants.js'
import { getRsaTps, getTxKey } from '../module/rsa.tps.js'

export class BaseTransaction {
  constructor ({ config, chain, logger, model } = {}) {
    this.config = config
    this.chain = chain
    this.logger = logger
    this.model = model
  }

  /**
   * implSign interface for child class
   * @param {object} _txData - transaction data
   * @param {[string]} _privateKeys - private keys array
   * @param {object} _encryptParams - encrypt param
   */
  async iSign (_txData, _privateKeys, _encryptParams) {
    throw new Error(`it is iSign interface!${this.chain}`)
  }

  async signByPrivateKey (body) {
    const { logger, chain } = this
    let error = false
    try {
      const results = await this.iSign(body)
      return {
        error,
        ...results
      }
    } catch (e) {
      logger.error({ chain }, `Error: ${e.message}, Stack: ${e.stack}`)
      error = {
        code: REQ_ERRORS.PROCESS_ERROR,
        message: `Error: ${e.message}, Stack: ${e.stack}`
      }
      return { error }
    }
  }

  /**
   * check sign transaction source data
   * @param {[string]} addrs - to addresses of transaction
   * @param {object} txData - request tx data
   * @param {object} encryptParams - task info of transaction
   */
  checkTxData ({ addrs, txData, encryptParams }) {
    let error = false
    if (!(txData.asset && typeof txData.asset === 'string')) {
      error = {
        code: REQ_ERRORS.REQUEST_PARAM_ERROR,
        message: `${this.chain} Request parameters asset error`
      }
    }
    if (typeof txData.decimal !== 'number') {
      error = {
        code: REQ_ERRORS.REQUEST_PARAM_ERROR,
        message: `${this.chain} Request parameters decimal error`
      }
    }
    if (!(txData.platform && typeof txData.platform === 'string')) {
      error = {
        code: REQ_ERRORS.REQUEST_PARAM_ERROR,
        message: `${this.chain} Request parameters platform error`
      }
    }
    if (!addrs) {
      error = {
        code: REQ_ERRORS.REQUEST_PARAM_ERROR,
        message: `${this.chain} Request parameters addrs error`
      }
    }
    if (!encryptParams) {
      error = {
        code: REQ_ERRORS.REQUEST_PARAM_ERROR,
        message: `${this.chain} Request parameters encrypt_params error`
      }
    }
    return error
  }

  /**
   * sign transaction
   * @param {string} pwd1 - the first password
   * @param {object} body - request tx data
   * @param {string} platform - platform
   */
  async sign (pwd1, body, platform) {
    const { config, chain, logger } = this
    let error

    try {
      const { addrs, data, secret, encrypt_params: encryptParam, ecdh_public_key: ecdhPublicKey, use_rsa_tps: useRsaTps = false } = body

      if (!addrs || !data || !secret || !encryptParam) {
        error = {
          code: REQ_ERRORS.REQUEST_PARAM_ERROR,
          message: 'Request parameters error'
        }
        logger.error({ chain }, 'Request parameters error')
        return { error }
      }
      const txData = typeof data === 'string' ? JSON.parse(data) : data
      const encryptParams = typeof encryptParam === 'string' ? JSON.parse(encryptParam) : encryptParam

      // 1. decrypt secret
      const secretRsa = new Rsa({
        password: config.server.rsaSecret.password,
        privKey: config.server.rsaSecret.privKey
      })
      const pwd2 = secretRsa.decryptSecret(secret)
      // 2. get private key for sign
      const privateKeys = await this.getPrivKeyByAddress(addrs, platform, pwd1 + pwd2)

      // 数据库中新增platform信息，防止出现部分地址跨平台，如果查询不到私钥，该数组元素均为undefined
      if (!privateKeys || privateKeys.includes(undefined)) {
        error = {
          code: REQ_ERRORS.KEY_NOT_FOUND,
          message: 'KeyPairs can not be founded in wallet db'
        }
        logger.error({ chain }, 'KeyPairs can not be founded in wallet db')
        return { error }
      }
      // 2.1 check tx data
      error = this.checkTxData({ addrs, txData, encryptParams })
      if (error) {
        return { error }
      }
      // 3. sign tx data
      const { txhash, output, extra } = await this.iSign(txData, privateKeys, encryptParams)
      // 4. aes encrypt raw tx
      encryptParams.raw_tx = output
      const { cipher, encrypted } = AesSign.encrypt(JSON.stringify(encryptParams))
      // 5. encrypt aes cipher
      const encryptionResult = useRsaTps && ecdhPublicKey
        ? await this.encryptWithRsaTps(cipher, ecdhPublicKey)
        : this.encryptWithRSA(cipher)

      return {
        error,
        txhash,
        encrypted,
        extra,
        ...encryptionResult
      }
    } catch (e) {
      logger.error({ chain }, `Error: ${e.message}, Stack: ${e.stack}`)
      error = {
        code: REQ_ERRORS.PROCESS_ERROR,
        message: `Error: ${e.message}, Stack: ${e.stack}`
      }
      return { error }
    }
  }

  async encryptWithRsaTps (cipher, ecdhPublicKey) {
    const ecdh = await ECDH.generate()
    const shareKey = await ecdh.getShareKey(ecdhPublicKey)
    const { publicKey, privateKey, password } = getRsaTps()
    // 无论翻转前、后 需保证getTxKey & RsaTps实例化使用同一套rsa
    const txKey = getTxKey(privateKey, password, shareKey)
    const signRsa = new RsaTps({
      pubKey: publicKey
    })

    return {
      enCipher: signRsa.encryptCipher(cipher),
      ecdhPublicKey: ecdh.getPublicKey(),
      txKey
    }
  }

  encryptWithRSA (cipher) {
    const signRsa = new Rsa({
      pubKey: this.config.server.rsaSign.pubKey
    })

    return {
      enCipher: signRsa.encryptCipher(cipher)
    }
  }

  async getPrivKeyByAddress (addrs, platform, password) {
    const { config, model } = this

    const sql = `select address,private_key from ${config.signer.db.tableName} where platform=? and address in (${new Array(addrs.length).fill('?').join(',')})`
    const res = await model.cipherDb.prepare(sql).all([platform, ...addrs])

    const keyMap = new Map(res.map(item => [item.address, Aes.decrypt(item.private_key, password)]))
    return addrs.map(addr => keyMap.get(addr))
  }
}
