import fs from 'fs'
import path from 'path'
import shelljs from 'shelljs'
import { Hash } from '@common/crypto'

// password length between 4 ~ 24
// 因为之前限制是64, 如果第1个人输入超过32位，那第2个人的密码就没有意义了。
// 两人密码一人一半，为什么选24而不是16.
// 如果之前第一个人输入密码已经大于16位，那之后增加地址或验证密码都会有问题。
export const PWD_MIN_LEN = 4
export const PWD_MAX_LEN = 24
export const NUM_REGEX = /^[0-9]+$/
export const MAX_CREATE = 1000 // max sqlite create account number

// Password must contain alphanumeric characters, and supports special characters.
// const PWD_REGEX = new RegExp(`^(?=.*[0-9])(?=.*[A-Za-z])[0-9A-Za-z!#$%&'()+,-./:;<=>?@[\\]^_\`{|}~*]{${PWD_MIN_LEN},${PWD_MAX_LEN}}$`);
// 鉴于签名机分段密码仍然存在全为字母或全为数字的可能性 故去掉部分校验规则
export const PWD_REGEX = new RegExp(`^[0-9A-Za-z!#$%&'()+,-./:;<=>?@[\\]^_\`{|}~*]{${PWD_MIN_LEN},${PWD_MAX_LEN}}$`)

export const isFunction = val => typeof val === 'function'

export const isAsyncFunction = val => Object.prototype.toString.call(val) === '[object AsyncFunction]'

export const getTimes = (count, maxInTime = MAX_CREATE) => {
  const times = Math.ceil(Number(count) / Number(maxInTime))
  // internal use, don't care out of range case
  const timeCount = currentTimeLoop => (currentTimeLoop === times ? count - maxInTime * (currentTimeLoop - 1) : maxInTime)
  return { times, timeCount }
}

export const relToAbsPath = (dir, str) => !path.isAbsolute(str) ? path.resolve(dir, str) : str

export const dirExist = (dirPath, autoCreate = false) => {
  const exist = fs.existsSync(dirPath)
  if (!exist && autoCreate) {
    // fs.mkdirSync recursive option can only support by v10+
    // fs.mkdirSync(dirPath, { recursive: true })
    shelljs.mkdir('-p', dirPath)
  }
  return exist
}

/**
 * store password hash
 * @param pwd - password
 * @param pwdFilePath - password file path
 * @return {boolean} - verify result
 */
export const verifyPwdWithFile = (pwd, pwdFilePath) => {
  try {
    if (fs.existsSync(pwdFilePath)) {
      const pwdConfirm = fs.readFileSync(pwdFilePath, 'utf8')
      if (!Hash.isValid(pwd, pwdConfirm)) {
        console.error('error', 'password valid error')
        return false
      }
      return true
    }
    fs.writeFileSync(`${pwdFilePath}`, Hash.digest(pwd))
    return true
  } catch (e) {
    console.error('read pwd valid file error:', `${e.message}, ${e.stack}`)
    return false
  }
}

export const verifyECDHPubkeyWithFile = (ecdhPublicKey, ecdhFilePath) => {
  try {
    if (fs.existsSync(ecdhFilePath)) {
      const ecdhPublicKeySaved = fs.readFileSync(ecdhFilePath, 'utf8')
      if (!Hash.isValid(ecdhPublicKey, ecdhPublicKeySaved)) {
        console.error('error', 'ecdh publickey verified error')
        return false
      }
      return true
    }
    return false
  } catch (e) {
    console.error('ecdh publickey verified error:', `${e.message}, ${e.stack}`)
    return false
  }
}

export const storeECDHPubkeyHashWithFile = (ecdhPublicKey, ecdhFilePath) => {
  try {
    if (fs.existsSync(ecdhFilePath)) {
      const ecdhPublicKeySaved = fs.readFileSync(ecdhFilePath, 'utf8')
      if (!Hash.isValid(ecdhPublicKey, ecdhPublicKeySaved)) {
        fs.writeFileSync(`${ecdhFilePath}`, Hash.digest(ecdhPublicKey))
      }
    } else {
      fs.writeFileSync(`${ecdhFilePath}`, Hash.digest(ecdhPublicKey))
    }
  } catch (e) {
    console.error('ecdh publickey stored error:', `${e.message}, ${e.stack}`)
  }
}

export const createSuccessResponse = (data) => ({
  result: true,
  error: null,
  data
})

export const createErrorResponse = (code, message) => ({
  result: false,
  error: { code, message },
  data: null
})

export const pwdValidate = input => {
  if (!PWD_REGEX.test(input)) {
    return `Password must contain alphanumeric characters, and supports special characters. The length of password varies from ${PWD_MIN_LEN} to ${PWD_MAX_LEN}`
  }
  return true
}

export const intValidate = input => {
  if (!NUM_REGEX.test(input)) {
    return 'Input must be a integer'
  }
  return true
}

export const totpTokenValidate = input => {
  if (!NUM_REGEX.test(input) || input.length !== 6) {
    return 'Input must be a 6 digits totp token'
  }
  return true
}

export const assembleSqlParams = (options = {}) => {
  const { attributes = [], where = {}, limit, offset } = options
  let sql = `select ${attributes.join(',') || '*'} from keystore`
  let params = []

  if (Object.keys(where).length > 0) {
    let whereStr = ''
    for (const [key, value] of Object.entries(where)) {
      if (whereStr) whereStr += ' and '
      if (Array.isArray(value)) {
        whereStr += `${key} in (${new Array(value.length).fill('?').join(',')})`
        params = params.concat(value)
      } else {
        whereStr += `${key}=?`
        params.push(value)
      }
    }
    if (whereStr) sql += ` where ${whereStr}`
  }

  if (limit) {
    sql += ' limit ?'
    params.push(limit)
    if (offset) {
      sql += ' offset ?'
      params.push(offset)
    }
  }

  return { sql, params }
}

export const unlock = async (model, force = false) => {
  try {
    const encrypted = await model.checkLock()
    if (encrypted) {
      model.unLock()
    } else {
      if (force) {
        throw new Error('Wallet.db should be encrypted before starting the server!')
      }
    }
  } catch (err) {
    console.error(err.message)
    throw err
  }
}
