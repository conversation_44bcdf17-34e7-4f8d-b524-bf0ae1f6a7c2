import fs from 'fs'
import Koa from 'koa'
import Mali from 'mali'
import path from 'path'
import https from 'https'
import inquirer from 'inquirer'
import sslify from 'koa-sslify'
import KoaRouter from 'koa-router'
import { fileURLToPath } from 'url'
import BodyParser from 'koa-bodyparser'
import * as reflection from '@grpc/reflection'
import * as protoLoader from '@grpc/proto-loader'
import { Totp } from '@common/crypto'
import { verifyPwdWithFile } from './utils.js'
import { BaseController } from './controller.js'
import { pwdQuestions, secretQuestions } from './constants.js'

const filename = fileURLToPath(import.meta.url)
const dirname = path.dirname(filename)

// 兼容测试用例default问题
const enforceHttps = sslify.default || sslify

export class BaseServer {
  constructor ({ config, logger, transaction, chain } = {}) {
    this.config = config
    this.logger = logger
    this.transaction = transaction
    this.chain = chain
    // this is for cache 1st password in memory
    this.password = ''
    // totp instance
    this.totp = null
    // https server instance
    this.server = null
    this.router = new KoaRouter()
  }

  async getCheckedPwd1 (questions) {
    const { config, logger } = this
    const pwdPath = `${config.signer.db.dbPath}/${config.dbChain || this.chain}`
    const pwd1File = `${pwdPath}/pwd1`

    const { pwd1 } = await inquirer.prompt(questions)
    if (!verifyPwdWithFile(pwd1, pwd1File)) {
      logger.error('error', '1st password error')
      return this.getCheckedPwd1(questions)
    }

    return { pwd1 }
  }

  async getCheckedTotpSecret (questions) {
    if (process.env.disTotp) {
      return { enTotp: false }
    }
    const { logger } = this
    const { enTotp, secret, token } = await inquirer.prompt(questions)
    if (!enTotp) {
      return { enTotp: false }
    }
    this.totp = new Totp({ secret })
    try {
      this.totp.validateTotp(token)
      return { secret, enTotp: true }
    } catch (error) {
      logger.error('error', 'totp token error')
      return this.getCheckedTotpSecret(questions)
    }
  }

  getRouter () {
    const { router, config, logger, transaction, chain, password } = this
    router.post(BaseController.sign.path, ...BaseController.sign.middleware({ config, logger, transaction, chain, password }))
  }

  async startHttp () {
    const { config, logger, router } = this
    const app = new Koa()
    this.getRouter()
    const { server } = config
    const port = server.port || 3000
    const hostname = server.host || 'localhost'

    const sslOpt = {
      ca: [fs.readFileSync(server.ssl_ca)],
      key: fs.readFileSync(server.ssl_key),
      cert: fs.readFileSync(server.ssl_cert),
      requestCert: server.requestCert,
      rejectUnauthorized: server.rejectUnauthorized
    }

    app.use(enforceHttps({ hostname, port }))

    app.use(BodyParser())

    app.use(router.routes()).use(router.allowedMethods())

    this.server = https.createServer(sslOpt, app.callback()).listen(port, hostname, () => {
      console.info('success', `HTTP Server start on https://${hostname}:${port}`)
      logger.info('success', `HTTP Server start on https://${hostname}:${port}`)
    })
  }

  async startGrpc () {
    const { config, logger, transaction, chain, password, totp } = this
    const getProtoFile = path.resolve(dirname, '..', 'protos', 'signer.proto')
    const app = new Mali(getProtoFile, 'SignerService', {
      keepCase: true
    })
    const { server } = config
    const port = server.grpcPort || 3001
    const hostname = server.host || 'localhost'

    const sslOpt = {
      ca: fs.readFileSync(server.ssl_ca),
      key: fs.readFileSync(server.ssl_key),
      cert: fs.readFileSync(server.ssl_cert),
      requestCert: server.requestCert
    }

    const credentials = app.grpc.ServerCredentials.createSsl(
      sslOpt.ca,
      [{ cert_chain: sslOpt.cert, private_key: sslOpt.key }],
      sslOpt.requestCert
    )

    // 路由
    app.use({ sign: BaseController.signv1.middleware({ config, logger, transaction, chain, password, totp }) })

    const grpcServer = await app.start(`${hostname}:${port}`, credentials)
    console.info('success', `gRPC Server started on ${hostname}:${port}`)
    logger.info('success', `gRPC Server started on ${hostname}:${port}`)

    // 反射
    const packageDefinition = protoLoader.loadSync(getProtoFile, {
      keepCase: true
    })
    const reflectionService = new reflection.ReflectionService(packageDefinition)
    reflectionService.addToServer(grpcServer)
  }

  async start () {
    const { pwd1 } = process.env.pwd1
      ? { pwd1: process.env.pwd1 }
      : await this.getCheckedPwd1([pwdQuestions[0]])
    this.password = pwd1
    if (!process.env.pwd1) {
      process.send({ pwd1 })
    }
    const { secret, enTotp } = process.env.secret
      ? { secret: process.env.secret, enTotp: process.env.enTotp }
      : await this.getCheckedTotpSecret(secretQuestions)
    if (secret) {
      if (!process.env.secret) {
        process.send({ secret })
      }
      this.totp = new Totp({ secret })
    }
    if (enTotp) {
      if (!process.env.enTotp) {
        process.send({ enTotp })
      }
    } else {
      if (!process.env.disTotp) {
        process.send({ disTotp: true })
      }
    }

    const { serverType } = await inquirer.prompt([
      {
        type: 'list',
        name: 'serverType',
        message: '请选择服务启动类型:',
        choices: ['http', 'grpc'],
        default: 'http'
      }
    ])
    if (serverType === 'grpc') {
      await this.startGrpc()
    } else {
      await this.startHttp()
    }
  }
}
