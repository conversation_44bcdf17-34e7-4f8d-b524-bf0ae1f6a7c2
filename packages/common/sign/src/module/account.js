/* eslint-disable no-plusplus */
import fs from 'fs'
import inquirer from 'inquirer'
import ProgressBar from 'progress'
import { Jwt, Aes } from '@common/crypto'
import uuid from 'uuid'
import assert from 'assert'
import {
  generateQuestions,
  pwdValidateQuestions,
  sqliteQuestions,
  generateQuestionsWithSignMsg
} from './constants.js'
import {
  isFunction, isAsyncFunction, getTimes, unlock,
  dirExist, verifyPwdWithFile, assembleSqlParams,
  intValidate, pwdValidate, MAX_CREATE
} from './utils.js'

export class BaseAccount {
  constructor ({ config, chain, logger, model } = {}) {
    this.config = config
    this.chain = chain
    this.logger = logger
    this.model = model
  }

  /**
   * password check, if not exist then store hash
   * @param {string} pwd1 - 1st password
   * @param {string} pwd2 - 2nd password
   * @return {boolean} - check result
   */
  pwdCheck (pwd1, pwd2) {
    const { config, logger } = this
    const pwdPath = `${config.signer.db.dbPath}/${config.dbChain || this.chain}`
    const pwd1File = `${pwdPath}/pwd1`
    const pwd2File = `${pwdPath}/pwd2`

    if (!verifyPwdWithFile(pwd1, pwd1File)) {
      logger.error('error', '1st password error')
      return false
    }
    if (!verifyPwdWithFile(pwd2, pwd2File)) {
      logger.error('error', '2nd password error')
      return false
    }

    return true
  }

  async getCheckedPwd1 (questions) {
    const { config, logger } = this
    const pwdPath = `${config.signer.db.dbPath}/${config.dbChain || this.chain}`
    const pwd1File = `${pwdPath}/pwd1`

    const { pwd1 } = await inquirer.prompt(questions)
    if (!verifyPwdWithFile(pwd1, pwd1File)) {
      logger.error('error', '1st password error')
      return this.getCheckedPwd1(questions)
    }

    return { pwd1 }
  }

  async getCheckedPwd2 (questions) {
    const { config, logger } = this
    const pwdPath = `${config.signer.db.dbPath}/${config.dbChain || this.chain}`
    const pwd2File = `${pwdPath}/pwd2`

    const { pwd2 } = await inquirer.prompt(questions)
    if (!verifyPwdWithFile(pwd2, pwd2File)) {
      logger.error('error', '2nd password error')
      return this.getCheckedPwd2(questions)
    }

    return { pwd2 }
  }

  /**
   * implGenerate interface for child class (Factory Mode)
   * @param {string} _password - 1st + 2nd password.
   * @param {number} _count - generate address Nth.
   */
  async iGenerate (_password, _count) {
    throw new Error(`it is iGenerate interface!,${this.chain}`)
  }

  async getGenerateInfo () {
    const { count } = await inquirer.prompt(generateQuestions.slice(0, 1))
    const { pwd1 } = await this.getCheckedPwd1(generateQuestions.slice(1, 3))
    const { pwd2 } = await this.getCheckedPwd2(generateQuestions.slice(3, 5))

    return {
      count,
      pwd1,
      pwd2,
      password: pwd1 + pwd2
    }
  }

  async animateExecute ({ bar, count, items, executor }) {
    assert.ok(executor, 'What do you want, en ?')
    const results = []
    if (Array.isArray(items)) {
      for (let c = 0; c < items.length; c++) {
        const item = items[c]
        const result = isAsyncFunction(executor) ? await executor(item) : executor(item)
        results.push(result)
        if (bar) bar.tick()
      }
    } else {
      for (let c = 0; c < count; c++) {
        const result = isAsyncFunction(executor) ? await executor() : executor()
        results.push(result)
        if (bar) bar.tick()
      }
    }

    return results
  }

  async internalGenerate ({ password, transform }) {
    const account = await this.iGenerate(password)
    if (isFunction(transform)) {
      if (isAsyncFunction(transform)) {
        await transform(account)
      } else {
        transform(account)
      }
    }
    return account
  }

  getProgressBar (template, count) {
    return new ProgressBar(template, { total: Number(count) })
  }

  async generate () {
    try {
      const { model } = this
      await unlock(model)
      const { password, count } = await this.getGenerateInfo()
      const bar = this.getProgressBar('Generating [:bar] :current/:total ', count)
      const { times, timeCount } = getTimes(count, MAX_CREATE)
      for (let time = 1; time <= times; time++) {
        const accounts = await this.animateExecute({
          count: timeCount(time),
          bar,
          executor: async () =>
            this.internalGenerate({
              password,
              transform: account => {
                account.address = account.address ? account.address : `tmp-addr-${uuid.v1()}`
                account.coin = this.chain
                account.platform = this.config.signer.addrExport.platform.trim()
              }
            })
        })
        // Transaction functions do not work with async functions. Technically speaking, async functions always return after the first await, which
        // means the transaction will already be committed before any async code executes. Also, because SQLite3 serializes all transactions, it's
        // generally a very bad idea to keep a transaction open across event loop ticks anyways.
        const fields = Object.keys(accounts[0]).join()
        const values = Object.keys(accounts[0]).map(item => `@${item}`).join()
        model.cipherDb.transaction((list) => {
          try {
            for (const item of list) model.cipherDb.prepare(`insert into keystore (${fields}) values (${values})`).run(item)
          } catch (err) {
            if (!model.cipherDb.inTransaction) throw err // (transaction was forcefully rolled back)
          }
        })(accounts)
      }
      await this.addEncryption()
      await this.closeModel()
      this.logger.info('success', `${count} account has been generated`)
    } catch (e) {
      this.logger.error('account create error', `${e.message}, ${e.stack}`)
    }
  }

  /**
   * iSignMsg
   * implSignMessage interface for child class (Factory Mode)
   * @param {string} _msg - be signed message
   * @param {string} _address - addr
   * @param {string} _encryptPrivKey - private key
   * @param {string} _password - password
   * @param {string} _publicKey - public key
   */
  async iSignMsg (_msg, _address, _encryptPrivKey, _password, _publicKey) {
    throw new Error(`it is iSignMsg interface!,${this.chain}`)
  }

  /**
   * sign_msg
   * use privateKey sign a message, use to jp
   */
  async sign_msg () {
    const { logger, config, model } = this
    await unlock(model)
    const { msg } = await inquirer.prompt(generateQuestionsWithSignMsg.slice(0, 1))
    const { pwd1 } = await this.getCheckedPwd1(generateQuestionsWithSignMsg.slice(1, 2))
    const { pwd2 } = await this.getCheckedPwd2(generateQuestionsWithSignMsg.slice(2, 3))
    const { filename } = await inquirer.prompt(generateQuestionsWithSignMsg.slice(3))

    const signPath = `${config.signer.db.dbPath}/${config.dbChain || this.chain}`
    const filterPathFile = `${signPath}/${filename}`
    // validate address in wallet.db
    const options = { where: {} }
    if (filename && fs.existsSync(filterPathFile)) {
      const { fieldName, items } = await this.getTargetField(filterPathFile)
      options.where[fieldName] = items
    }
    const { sql, params } = assembleSqlParams(options)
    const res = await model.cipherDb.prepare(sql).all(params)
    const count = res.length
    if (!res || count === 0) {
      logger.error('sign_msg error', `No ${this.chain} account.`)
      process.exit(1)
    }
    const signPathFile = `${signPath}/signature.txt`
    const signatureOutput = fs.createWriteStream(signPathFile)

    try {
      const pwd = pwd1 + pwd2
      const bar = new ProgressBar('Signing [:bar] :current/:total ', { total: +count })
      for (let i = 0; i < count; i += 1) {
        const temp = res[i]
        const signature = await this.iSignMsg(msg, temp.address, temp.private_key, pwd, temp.public_key)
        if (!signature) {
          logger.error('invalid', `row count: ${i}, address: ${temp.address}`)
          return
        }
        signatureOutput.write(`${temp.address},${msg},${signature},${temp.public_key}\n`, 'UTF8')
        bar.tick()
      }
      signatureOutput.end()
      await this.closeModel()
      logger.info('success', `${count} account has signed the message.`)
      // emit validate success event
      process.emit('sign_msg:success', count)
    } catch (e) {
      logger.error('sign_msg error', `${e.message}, ${e.stack}`)
    }
  }

  /**
   * implValidate interface for child class (Factory Mode)
   * @param {string} _address - addr
   * @param {string} _encryptPrivKey - private key
   * @param {string} _password - password
   * @param {string} _publicKey - public key
   */
  async iValidate (_address, _encryptPrivKey, _password, _publicKey) {
    throw new Error(`it is iValidate interface!,${this.chain}`)
  }

  async getValidateInfo () {
    const { single } = await inquirer.prompt(pwdValidateQuestions.slice(0, 1))
    const { pwd1 } = await this.getCheckedPwd1(pwdValidateQuestions.slice(1, 2))
    const { pwd2 } = await this.getCheckedPwd2(pwdValidateQuestions.slice(2, 3))

    return {
      single: Number(single),
      pwd1,
      pwd2,
      password: pwd1 + pwd2
    }
  }

  /**
   * 线上，新币已经很少使用validate了。因为新生成地址必须走gateway测试，可忽略这一步。
   * 但签名机巡检等其他业务还需要
   * @returns void
   */
  async validate () {
    const { logger, chain, model } = this
    await unlock(model)
    const { single, password } = await this.getValidateInfo()
    const { total } = (await model.cipherDb.prepare('select count(*) as total from keystore').all([]))[0]
    const { times } = getTimes(total, single)
    if (total === 0) {
      logger.error('check error', `No ${chain} account.`)
      process.exit(1)
    }
    try {
      const bar = this.getProgressBar('Validating [:bar] :current/:total ', times)
      for (let t = 1; t <= times; t += 1) {
        const { sql, params } = assembleSqlParams({ offset: (t - 1) * single, limit: single })
        const res = await model.cipherDb.prepare(sql).all(params)
        for (let i = 0; i < res.length; i += 1) {
          const temp = res[i]
          if (!(await this.iValidate(temp.address, temp.private_key, password, temp.public_key))) {
            logger.error('invalid', `row count: ${i}, address: ${temp.address}`)
            return
          }
        }
        bar.tick()
      }
      await this.closeModel()
      logger.info('success', `${total} account has been validated.`)
      // emit validate success event
      process.emit('validate:success', total)
    } catch (e) {
      logger.error('address validate error', `${e.message}, ${e.stack}`)
    }
  }

  logExport (exportPath, record) {
    const filename = `${exportPath}/${this.chain}_export.log`
    fs.appendFileSync(filename, record)
  }

  /**
   * @returns Promise<void>
   */
  async export () {
    try {
      const { model } = this
      const conf = {
        exportFields: this.getExportFields('platform'),
        jwtTool: this.getJwtTool(),
        exportPath: this.getExportPath(),
        coin: this.chain,
        addrExport: this.config.signer.addrExport,
        platform: this.config.signer.addrExport.platform.trim()
      }
      const configuredExportList = this.getConfiguredExportList()
      await unlock(model)
      let total = 0
      for (const exportItem of configuredExportList) {
        const rows = await this.getRowsByConf(conf, exportItem)
        const progressBar = this.getProgressBar(`Exporting address.type=${exportItem.type} [:bar] :current/:total `, rows.length)
        const items = await this.animateExecute({
          items: rows,
          bar: progressBar,
          executor: async row => this.parseRow(row, conf, exportItem)
        })
        total += items.length
        this.writeRows(`${items.join('\n')}\n`, conf)
        this.writeLog(conf, exportItem)
      }
      await this.closeModel()
      this.logger.info('success', `${total} account has been exported.`)
      process.emit('export:success', total)
    } catch (e) {
      this.logger.error('address export error', `${e.message}, ${e.stack}`)
    }
  }

  writeRows (content, conf) {
    const filename = `${conf.exportPath}/${conf.coin}.${conf.addrExport.platform.trim()}.default.address.txt`
    fs.appendFileSync(filename, content)
  }

  writeLog (conf, meta) {
    this.logExport(conf.exportPath, `${meta.start} - ${meta.end} ${conf.addrExport.platform} ${meta.type}\n`)
  }

  async parseRow (row, conf, meta) {
    const token = conf.jwtTool.encrypt(
      {
        chain: conf.coin,
        addr: row.address,
        platform_name: row.platform,
        addr_type: parseInt(meta.type, 10)
      },
      conf.addrExport.jwtOptions
    )
    const fieldsData = []
    for (const field of conf.exportFields) {
      fieldsData.push(row[field])
    }
    return `${fieldsData.join(',')},${meta.type},${token}`
  }

  async getRowsByConf (conf, meta) {
    const { model } = this
    const { start, end } = meta
    const { sql, params } = assembleSqlParams({
      attributes: conf.exportFields,
      where: {
        platform: conf.platform
      },
      limit: end - start + 1,
      offset: start - 1
    })

    return model.cipherDb.prepare(sql).all(params)
  }

  getExportFields (...extra) {
    const defaultFields = ['address']
    defaultFields.push(...extra)
    return defaultFields
  }

  getJwtTool () {
    const { addrExport } = this.config.signer
    return new Jwt({ pubKey: addrExport.jwt_pub, privKey: addrExport.jwt_priv })
  }

  getExportPath (sync = true) {
    const { addrExport } = this.config.signer
    const exportPath = addrExport.exportPath || `${this.config.signer.db.dbPath}/${this.chain}`
    dirExist(exportPath, sync)
    return exportPath
  }

  getConfiguredExportList () {
    const { addrExport } = this.config.signer
    const exportList = []
    for (let i = 0; i < addrExport.addrTypes.length; i += 1) {
      const { type, indexes } = addrExport.addrTypes[i]
      for (let j = 0; j < indexes.length; j += 1) {
        const { start, end } = indexes[j]
        exportList.push({ start, end, type, offset: start - 1, limit: end - start + 1 })
      }
    }
    exportList.sort((a, b) => a.offset - b.offset)
    return exportList
  }

  async sqlite () {
    const { logger, model } = this
    await unlock(model)
    const { sql } = await inquirer.prompt(sqliteQuestions)
    const res = await model.cipherDb.prepare(sql).all([])
    await this.closeModel()
    logger.info('result', res)
  }

  async addEncryption () {
    const { logger, model } = this
    try {
      const encrypted = await model.checkLock()
      if (!encrypted) {
        model.lock()
        logger.info('Encryption added')
      } else {
        logger.warn('Encryption already exist')
      }
    } catch (err) {
      if (err.message === 'file is not a database') {
        logger.warn('Encryption already added')
      } else {
        logger.error(err.message)
      }
    }
  }

  async delEncryption () {
    const { logger, model } = this
    try {
      const encrypted = await model.checkLock()
      if (encrypted) {
        model.rmLock()
        logger.info('Encryption deleted')
      } else {
        logger.warn('Encryption not exist')
      }
    } catch (err) {
      if (err.message === 'file is not a database') {
        logger.warn('Encryption already deleted')
      } else {
        logger.error(err.message)
      }
    }
  }

  async checkEncryption () {
    const { logger, model } = this
    try {
      const encrypted = await model.checkLock()
      logger.info(`Encryption ${encrypted ? 'already' : 'not'} exist`)
    } catch (err) {
      logger.error(err.message)
    }
  }

  async closeModel () {
    const { logger, model } = this
    try {
      model.cipherDb.close()
      await model.sequelize.close()
    } catch (err) {
      logger.error(err.message)
    }
  }

  async changeSignerPassword () {
    const { config, chain, logger, model } = this
    const { addrExport } = config.signer
    const pwdPath = `${config.signer.db.dbPath}/${chain}`
    const { single, oldPwd1, oldPwd2, newPwd1, newPwd2 } = await inquirer.prompt([
      {
        type: 'input',
        name: 'single',
        message: 'Input single number of account to change password',
        default: 1000,
        validate: intValidate
      },
      {
        type: 'input',
        name: 'oldPwd1FileName',
        message: 'Input filename corresponding to the 1st old password',
        default: 'pwd1',
        validate (input) {
          if (!input) {
            return 'Please input filename corresponding to the 1st old password'
          }
          const pwd1File = `${pwdPath}/${input}`
          if (!fs.existsSync(pwd1File)) {
            return 'filename corresponding to the 1st old password is not exist'
          }
          return true
        }
      },
      {
        type: 'password',
        name: 'oldPwd1',
        mask: '*',
        message: 'Input 1st old password',
        validate (input, answers) {
          const pwd1File = `${pwdPath}/${answers.oldPwd1FileName}`
          if (!verifyPwdWithFile(input, pwd1File)) {
            return '1st password error'
          }
          return true
        }
      },
      {
        type: 'input',
        name: 'oldPwd2FileName',
        message: 'Input filename corresponding to the 2nd old password',
        default: 'pwd2',
        validate (input) {
          if (!input) {
            return 'Please input filename corresponding to the 2nd old password'
          }
          const pwd2File = `${pwdPath}/${input}`
          if (!fs.existsSync(pwd2File)) {
            return 'filename corresponding to the 2nd old password is not exist'
          }
          return true
        }
      },
      {
        type: 'password',
        name: 'oldPwd2',
        mask: '*',
        message: 'Input 2nd old password',
        validate (input, answers) {
          const pwd2File = `${pwdPath}/${answers.oldPwd2FileName}`
          if (!verifyPwdWithFile(input, pwd2File)) {
            return '2nd password error'
          }
          return true
        }
      },
      {
        type: 'password',
        name: 'newPwd1',
        mask: '*',
        message: 'Input 1st new password',
        validate: pwdValidate
      },
      {
        type: 'password',
        name: 'newPwd1Confirm',
        mask: '*',
        message: 'Input 1st new password again',
        validate (input, answers) {
          const r = pwdValidate(input)
          if (r !== true) {
            return r
          }
          if (input !== answers.newPwd1) {
            return '1st new password not match'
          }
          return true
        }
      },
      {
        type: 'password',
        name: 'newPwd2',
        mask: '*',
        message: 'Input 2nd new password',
        validate: pwdValidate
      },
      {
        type: 'password',
        name: 'newPwd2Confirm',
        mask: '*',
        message: 'Input 2nd new password again',
        validate (input, answers) {
          const r = pwdValidate(input)
          if (r !== true) {
            return r
          }
          if (input !== answers.newPwd2) {
            return '2nd new password not match'
          }
          return true
        }
      }
    ])

    if (!this.pwdCheck(newPwd1, newPwd2)) {
      return
    }
    const oldPassword = oldPwd1 + oldPwd2
    const newPassword = newPwd1 + newPwd2

    await unlock(model)

    const { total } = (await model.cipherDb.prepare('select count(*) as total from keystore').all([]))[0]
    const { times } = getTimes(total, single)
    if (total === 0) {
      logger.error(`Can not find ${chain} address info`)
      process.exit(1)
    }

    try {
      const bar = new ProgressBar('Signer password updating [:bar] :current/:total ', { total: +times })
      for (let t = 0; t < times; t += 1) {
        const { sql, params } = assembleSqlParams({
          attributes: ['id', 'private_key'],
          where: { platform: addrExport.platform.trim() },
          offset: t * single,
          limit: single
        })
        const list = await model.cipherDb.prepare(sql).all(params)
        const count = list.length
        for (let i = 0; i < count; i += 1) {
          const { id, address, private_key: oldEncryptPrivKey } = list[i]
          const decryptPrivKey = Aes.decrypt(oldEncryptPrivKey, oldPassword, '')
          const newEncryptPrivKey = Aes.encrypt(decryptPrivKey, newPassword, '')
          const sql = 'update keystore set private_key = ? WHERE id = ?'
          const { changes } = await model.cipherDb.prepare(sql).run([newEncryptPrivKey, id])
          if (!changes) {
            logger.error(`account ${address} replace password failed`)
          }
        }
        bar.tick()
      }
      await this.closeModel()
      logger.info('success', `${total} private key has been updated`)
    } catch (e) {
      logger.error('replace password error', `${e.message}, ${e.stack}`)
    }
  }
}
