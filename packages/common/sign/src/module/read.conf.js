import fs from 'fs'
import path from 'path'
import { parse } from 'toml'
import { relToAbsPath } from './utils.js'

export class BaseConfig {
  #cwd = process.cwd()
  #DEFAULT_DB_NAME = 'wallet' // default db name
  #DEFAULT_TABLE_NAME = 'keystore' // default table name
  #DEFAULT_DB_PATH = path.join(this.#cwd, 'walletdb') // default db path is <project root>/walletdb

  constructor (cmdPath) {
    this.initPath(cmdPath)
    this.initNode()
    this.initSigner()
    this.initServer()
  }

  initPath (cmdPath) {
    let configPath = path.join(this.#cwd, 'config') // path.resolve(__dirname);
    if (cmdPath && typeof cmdPath === 'string') {
      const stat = fs.statSync(cmdPath)
      if (stat.isDirectory()) {
        configPath = relToAbsPath(this.#cwd, cmdPath)
      }
    }

    this.path = configPath
  }

  initNode () {
    // 加载节点配置，兼容没有node配置的情况
    const nodePath = path.join(this.path, 'node.toml')
    if (fs.existsSync(nodePath)) {
      this.node = parse(fs.readFileSync(nodePath))
    }
  }

  initServer () {
    const server = parse(fs.readFileSync(path.join(this.path, 'server.toml')))
    // https server
    server.ssl_ca = relToAbsPath(this.path, server.ssl_ca)
    server.ssl_key = relToAbsPath(this.path, server.ssl_key)
    server.ssl_cert = relToAbsPath(this.path, server.ssl_cert)
    // rsa key pair for secret decrypt
    server.rsaSecret.privKey = fs.readFileSync(relToAbsPath(this.path, server.rsaSecret.privKey), 'utf8')
    // rsa key pair for sign data encrypt
    server.rsaSign.pubKey = fs.readFileSync(relToAbsPath(this.path, server.rsaSign.pubKey), 'utf8')
    server.ecdh.pubKeyHash = relToAbsPath(this.path, server.ecdh.pubKeyHash)

    this.server = server
  }

  initSigner () {
    const signer = parse(fs.readFileSync(path.join(this.path, 'signer.toml')))
    // jwt key pair
    signer.addrExport.jwt_pub = relToAbsPath(this.path, signer.addrExport.jwt_pub)
    signer.addrExport.jwt_priv = relToAbsPath(this.path, signer.addrExport.jwt_priv)
    signer.db.dbPath = signer.db.dbPath || this.#DEFAULT_DB_PATH // sqlite store path
    signer.db.dbName = signer.db.dbName || this.#DEFAULT_DB_NAME // sqlite db filename
    signer.db.tableName = signer.db.tableName || this.#DEFAULT_TABLE_NAME // sqlite table name for keys
    // 优先使用配置文件中的chain，配置文件中可以不配置chain，则使用app中固定值。
    if (signer.chain) {
      this.chain = signer.chain.trim() // java配置中不小心加入空格出现过一次事故
    }
    // 此逻辑用于地址复用换链的需求场景，配置文件中可以不配置dbChain。
    if (signer.dbChain) {
      this.dbChain = signer.dbChain.trim()
    }

    this.signer = signer
  }
}
