import http from 'http'
import Koa from 'koa'
import BodyParser from 'koa-bodyparser'
import KoaRouter from 'koa-router'
import { BaseController } from './controller.js'

export class BaseServerOffer {
  constructor ({ config, logger, transaction, chain } = {}) {
    this.config = config
    this.logger = logger
    this.transaction = transaction
    this.chain = chain
  }

  getRouter () {
    const { logger, transaction, chain } = this
    const router = new KoaRouter()
    const [catchError, , checkRequest] = BaseController.sign.middleware({ config: {}, logger, transaction, chain })

    router.post('/sign/:chain', catchError, checkRequest, async ctx => {
      const { body } = ctx.request

      const signResult = await transaction.signByPrivateKey(body)
      if (signResult.error) {
        ctx.body = {
          result: false,
          ...signResult
        }
        return
      }
      ctx.body = {
        result: true,
        ...signResult
      }
    })

    return router
  }

  async start () {
    const { config, logger } = this

    const app = new Koa()
    const router = this.getRouter()
    const { server } = config
    const port = server.port || 3000
    const hostname = server.host || 'localhost'

    app.use(BodyParser())

    app.use(router.routes()).use(router.allowedMethods())

    this.server = http.createServer(app.callback()).listen(port, hostname, () => {
      console.info('success', `Server start on http://${hostname}:${port}`)
      logger.info('success', `Server start on http://${hostname}:${port}`)
    })
  }
}
