import fs from 'fs'
import { intValidate, pwdValidate, totpTokenValidate } from './utils.js'

export const REQ_ERRORS = {
  AUTH_INVALID: 403,
  REQUEST_PARAM_ERROR: -1,
  NO_SUPPORT_CHAIN: -2,
  KEY_NOT_FOUND: -3,
  PROCESS_ERROR: -10
}

export const generateQuestions = [
  {
    type: 'input',
    name: 'count',
    message: 'Input number of account to generate',
    validate: intValidate
  },
  {
    type: 'password',
    name: 'pwd1',
    mask: '*',
    message: 'Input 1st part password',
    validate: pwdValidate
  },
  {
    type: 'password',
    name: 'pwd1Confirm',
    mask: '*',
    message: 'Input 1st part password again',
    validate (input, answers) {
      const r = pwdValidate(input)
      if (r !== true) {
        return r
      }
      if (input !== answers.pwd1) {
        return '1st password not match'
      }
      return true
    }
  },
  {
    type: 'password',
    name: 'pwd2',
    mask: '*',
    message: 'Input 2nd part password',
    validate: pwdValidate
  },
  {
    type: 'password',
    name: 'pwd2Confirm',
    mask: '*',
    message: 'Input 2nd part password again',
    validate (input, answers) {
      const r = pwdValidate(input)
      if (r !== true) {
        return r
      }
      if (input !== answers.pwd2) {
        return '2nd password not match'
      }
      return true
    }
  }
]

export const pwdValidateQuestions = [
  {
    type: 'input',
    name: 'single',
    message: 'Input single number of account to validate',
    validate: intValidate
  },
  {
    type: 'password',
    name: 'pwd1',
    mask: '*',
    message: 'Input 1st part password'
  },
  {
    type: 'password',
    name: 'pwd2',
    mask: '*',
    message: 'Input 2nd part password'
  }
]

export const sqliteQuestions = [
  {
    type: 'input',
    name: 'sql',
    message: 'Input SQL to CRUD for wallet.db. like "select count(*) from keystore"'
  }
]

export const pwdQuestions = [
  {
    type: 'password',
    name: 'pwd1',
    mask: '*',
    message: 'Input 1st part password'
  },
  {
    type: 'password',
    name: 'pwd2',
    mask: '*',
    message: 'Input 2nd part password'
  }
]

export const secretQuestions = [
  {
    type: 'confirm',
    name: 'enTotp',
    message: 'Do you want to enable TOTP authentication?',
    default: false
  },
  {
    type: 'password',
    name: 'secret',
    mask: '*',
    message: 'Input totp secret',
    when (res) {
      return res.enTotp
    },
    validate (input) {
      if (!input) {
        return 'Please input totp secret'
      }
      return true
    }
  },
  {
    type: 'password',
    name: 'token',
    mask: '*',
    message: 'Input totp token',
    when (res) {
      return res.enTotp && res.secret
    },
    validate: totpTokenValidate
  }
]

export const generateQuestionsWithPrefix = [
  ...generateQuestions,
  {
    type: 'input',
    name: 'prefix',
    message: 'Input address prefix',
    validate (input) {
      if (!input) {
        return 'Please input address prefix'
      }
      return true
    }
  }
]

export const generateQuestionsWithSignMsg = [
  {
    type: 'input',
    name: 'msg',
    message: 'Input sign message str',
    validate (input) {
      if (!input) {
        return 'Please input sign message str'
      }
      return true
    }
  },
  ...pwdQuestions,
  {
    type: 'confirm',
    name: 'needFilter',
    message: 'Enter filter address account file?',
    default: false
  },
  {
    type: 'input',
    name: 'filename',
    message: 'Input filename which is specified address range',
    when (res) {
      return res.needFilter
    },
    validate (input) {
      if (!input) {
        return 'Please input filename which is specified address range'
      }
      return true
    }
  }
]

export const exportQuestions = [
  {
    type: 'input',
    name: 'platform',
    message: 'Input address platform',
    validate (input) {
      if (!input) {
        return 'Please input address platform'
      }
      return true
    }
  },
  {
    type: 'input',
    name: 'addrType',
    message: 'Input address type',
    validate (input) {
      if (!input) {
        return 'Please input address type'
      }
      return true
    }
  },
  {
    type: 'input',
    name: 'offset',
    message: 'Input record offset',
    validate (input) {
      if (!input) {
        return 'Input record offset'
      }
      return true
    }
  },
  {
    type: 'input',
    name: 'limit',
    message: 'Input export number',
    validate (input) {
      if (!input) {
        return 'Input export number'
      }
      return true
    }
  }
]

export const migrateQuestions = [
  {
    type: 'list',
    name: 'mType',
    message: 'Please select migrate type',
    choices: [
      {
        name: 'file',
        value: 'file'
      },
      {
        name: 'sqlite',
        value: 'sqlite'
      }
    ]
  },
  {
    type: 'input',
    name: 'filename',
    message: 'Please input absolute file path for keystore',
    validate (input) {
      const stats = fs.statSync(input)
      if (!stats.isFile()) {
        return 'File not exist'
      }
      return true
    }
  },
  ...pwdQuestions
]

export const getTargetFieldQuestions = [
  {
    type: 'number',
    name: 'targetIndex',
    message: 'Index of target field',
    default: 0
  },
  {
    type: 'input',
    name: 'separator',
    message: 'Separator of fields',
    default: ','
  },
  {
    type: 'input',
    name: 'fieldName',
    message: 'Name of target field',
    default: 'address'
  }
]

export const cipherQuestions = [
  {
    type: 'password',
    name: 'cipher',
    mask: '*',
    message: 'Input cipher for walletdb',
    validate: pwdValidate
  },
  {
    type: 'password',
    name: 'cipherConfirm',
    mask: '*',
    message: 'Input cipher for walletdb again',
    validate (input, answers) {
      const r = pwdValidate(input)
      if (r !== true) {
        return r
      }
      if (input !== answers.cipher) {
        return 'cipher not match'
      }
      return true
    }
  }
]
