import { checkAws4QueryAuth } from '@common/utils'
import { REQ_ERRORS } from './constants.js'
import {
  verifyECDHPubkeyWithFile,
  storeECDHPubkeyHashWithFile,
  createSuccessResponse,
  createErrorResponse
} from './utils.js'

function commonMiddleware ({ config, logger, chain: chainName }) {
  return [
    async function catchError (ctx, next) {
      const { chain } = ctx.params
      try {
        await next()
      } catch (e) {
        logger.error({ chain }, `Error: ${e.message}, Stack: ${e.stack}`)
        ctx.body = createErrorResponse(REQ_ERRORS.PROCESS_ERROR, `Error: ${e.message}, Stack: ${e.stack}`)
      }
    },
    async function checkAws (ctx, next) {
      const { chain } = ctx.params
      const { body } = ctx.request
      const { awsKeys } = config.server
      const check = checkAws4QueryAuth(
        {
          path: ctx.request.url,
          query: ctx.query,
          headers: ctx.request.header,
          body: JSON.stringify(body)
        },
        awsKeys
      )
      if (!check) {
        logger.error({ chain }, 'AWS auth invalid')
        ctx.body = createErrorResponse(REQ_ERRORS.AUTH_INVALID, 'AWS auth invalid')
        return
      }
      await next()
    },
    async function checkRequest (ctx, next) {
      const { chain } = ctx.params
      const { body } = ctx.request
      if (chainName.toLowerCase() !== chain.toLowerCase()) {
        logger.error({ chain }, 'Chain is not support')
        ctx.body = createErrorResponse(REQ_ERRORS.NO_SUPPORT_CHAIN, 'Chain is not support')
        return
      }
      if (!body) {
        logger.error({ chain }, 'Request body error')
        ctx.body = createErrorResponse(REQ_ERRORS.REQUEST_PARAM_ERROR, 'Request body error')
        return
      }
      await next()
    }
  ]
}

export const BaseController = {
  sign: {
    path: '/sign/:chain',
    middleware ({ config, logger, transaction, chain: chainName, password }) {
      return [
        ...commonMiddleware({ config, logger, chain: chainName }),
        async function signTx (ctx) {
          const { body } = ctx.request
          const { platform_outer: platformOuter } = ctx.query
          const { error, txhash, encrypted, enCipher, extra } = await transaction.sign(password, body, platformOuter)
          if (error !== false) {
            ctx.body = createErrorResponse(error.code, error.message)
            return
          }
          ctx.body = createSuccessResponse({
            encrypt_data: encrypted,
            extra: {
              ...(extra || {}),
              txhash,
              cipher: enCipher
            }
          })
        }
      ]
    }
  },
  signv1: {
    path: '/sign/v1/:chain',
    middleware ({ config, logger, transaction, chain: chainName, password, totp }) {
      return [
        async function catchError (ctx, next) {
          const { chain } = ctx.req
          try {
            await next()
          } catch (e) {
            logger.error({ chain }, `Error: ${e.message}, Stack: ${e.stack}`)
            ctx.res = createErrorResponse(REQ_ERRORS.PROCESS_ERROR, `Error: ${e.message}, Stack: ${e.stack}`)
          }
        },
        async function checkRequest (ctx, next) {
          const { chain } = ctx.req
          if (chainName.toLowerCase() !== chain.toLowerCase()) {
            logger.error({ chain }, 'Chain is not support')
            ctx.res = createErrorResponse(REQ_ERRORS.NO_SUPPORT_CHAIN, 'Chain is not support')
            return
          }
          if (!ctx.req) {
            logger.error({ chain }, 'Request body error')
            ctx.res = createErrorResponse(REQ_ERRORS.REQUEST_PARAM_ERROR, 'Request body error')
            return
          }
          await next()
        },
        async function checkECDHPubkey (ctx, next) {
          const { chain, ecdh_public_key: ecdhPublicKey, totp_token: totpToken } = ctx.req
          if (!ecdhPublicKey) {
            logger.error({ chain }, 'Request body error')
            ctx.res = createErrorResponse(REQ_ERRORS.REQUEST_PARAM_ERROR, 'Request body error')
            return
          } else {
            if (totpToken) {
              try {
                if (!totp) {
                  logger.error({ chain }, 'Totp secret is not configured correctly')
                  ctx.res = createErrorResponse(REQ_ERRORS.PROCESS_ERROR, 'Totp secret is not configured correctly')
                  return
                }
                totp.validateTotp(totpToken)
                storeECDHPubkeyHashWithFile(ecdhPublicKey, config.server.ecdh.pubKeyHash)
              } catch (error) {
                logger.error({ chain }, 'Request body totp_token error')
                ctx.res = createErrorResponse(REQ_ERRORS.REQUEST_PARAM_ERROR, 'Request body totp_token error')
                return
              }
            } else {
              const res = verifyECDHPubkeyWithFile(ecdhPublicKey, config.server.ecdh.pubKeyHash)
              if (!res) {
                logger.error({ chain }, 'Request body ecdh_public_key error')
                ctx.res = createErrorResponse(REQ_ERRORS.REQUEST_PARAM_ERROR, 'Request body ecdh_public_key error')
                return
              }
            }
          }
          await next()
        },
        async function signTx (ctx) {
          const { addrs, data, encrypt_params: encryptParams, chain, secret, platform_outer: platformOuter, ecdh_public_key: ecdhPublicKey, use_rsa_tps: useRsaTps = true } = ctx.req
          const { error, txhash, encrypted, enCipher, extra, ecdhPublicKey: respEcdhPublicKey, txKey } = await transaction.sign(
            password,
            {
              addrs,
              data,
              encrypt_params: encryptParams,
              chain,
              secret,
              ecdh_public_key: ecdhPublicKey,
              use_rsa_tps: useRsaTps
            },
            platformOuter
          )
          if (error !== false) {
            ctx.res = createErrorResponse(error.code, error.message)
            return
          }
          ctx.res = createSuccessResponse({
            encrypt_data: encrypted,
            extra: {
              ...(extra || {}),
              txhash,
              cipher: enCipher,
              ecdh_public_key: respEcdhPublicKey,
              txkey: txKey
            }
          })
        }
      ]
    }
  }
}
