syntax = "proto3";

package signer;

message SignRequest {
  repeated string addrs = 1;
  string data = 2;
  string encrypt_params = 3;
  string chain = 4;
  string secret = 5;
  string platform_outer = 6;
  string ecdh_public_key = 7;
  string totp_token = 8;
}

message Extra {
  string txhash = 1;
  string cipher = 2;
  string ecdh_public_key = 3;
  string txkey = 4;
}

message Data {
  string encrypt_data = 1;
  Extra extra = 2;
}

message Error {
  sint32 code = 1;
  string message = 2;
}

message SignResponse {
  bool result = 1;
  Error error = 2;
  Data data = 3;
}

service SignerService {
  rpc sign (SignRequest) returns (SignResponse) {}
}
