import { Jwt } from '@common/crypto'
import uuid from 'uuid'
import path from 'path'
import { BaseAccount } from '../src/module/account'

describe('generate command test', () => {
  class TestGenerateCmd extends BaseAccount {
    async iGenerate (password) {
      return {
        address: 'address',
        private_key: password,
        public_key: 'public_key'
      }
    }
  }

  it('test iGenerate()', async () => {
    const cmd = new TestGenerateCmd()
    const account = await cmd.iGenerate('********')
    expect(account).toEqual({
      address: 'address',
      private_key: '********',
      public_key: 'public_key'
    })
  })

  it('test internalGenerate transform === undefined', async () => {
    const cmd = new TestGenerateCmd()
    cmd.iGenerate = async password => ({
      address: '',
      private_key: password,
      public_key: 'public_key'
    })
    const result = await cmd.internalGenerate({ password: 'asdf' })
    expect(result).toEqual({
      address: '',
      private_key: 'asdf',
      public_key: 'public_key'
    })
  })

  it('test internalGenerate transform !== undefined', async () => {
    const cmd = new TestGenerateCmd()
    const address = uuid.v4()
    cmd.iGenerate = async password => ({
      address: '',
      private_key: password,
      public_key: 'public_key'
    })
    const result = await cmd.internalGenerate({
      password: 'asdf',
      transform (account) {
        account.address = account.address ? account.address : address
        account.private_key = 'pwd'
        account.platform = 'platform'
      }
    })
    expect(result).toEqual({
      address,
      private_key: 'pwd',
      public_key: 'public_key',
      platform: 'platform'
    })
  })

  it.skip('test generateV2() count < MAX_COUNT', async () => {
    const cmd = new TestGenerateCmd()
    cmd.chain = 'test_coin'
    cmd.config = { signer: { addrExport: { platform: 'huobipro' } } }
    cmd.logger = { error: console.error, done: console.log }
    cmd.getGenerateInfo = jest.fn(() => ({
      pwd1: '1234',
      pwd2: '5678',
      password: '********',
      count: 10
    }))
    const bulkCreate = jest.fn(() => { })
    cmd.model = { bulkCreate }
    await cmd.generate()
    expect(bulkCreate.mock.calls.length).toBe(1)
    expect(bulkCreate.mock.calls[0][0][0]).toEqual({
      address: 'address',
      coin: 'test_coin',
      platform: 'huobipro',
      private_key: '********',
      public_key: 'public_key'
    })
  })

  it.skip('test generateV2() count > MAX_COUNT', async () => {
    const cmd = new TestGenerateCmd()
    cmd.chain = 'test_coin'
    cmd.config = { signer: { addrExport: { platform: 'huobipro' } } }
    cmd.logger = { error: console.error, done: console.log }
    cmd.getGenerateInfo = jest.fn(() => ({
      pwd1: '1234',
      pwd2: '5678',
      password: '********',
      count: 9999
    }))
    const bulkCreate = jest.fn(() => { })
    cmd.model = { bulkCreate }
    await cmd.generate()
    expect(bulkCreate.mock.calls.length).toBe(10)
    expect(bulkCreate.mock.calls[0][0][998]).toEqual({
      address: 'address',
      coin: 'test_coin',
      platform: 'huobipro',
      private_key: '********',
      public_key: 'public_key'
    })
  })
})

describe('validate command test', () => {
  class TestValidateCmd extends BaseAccount {
    iValidate (address, encryptPrivKey, password, publicKey) {
      return !!address && encryptPrivKey === password && !!publicKey
    }
  }

  it.skip('validate test case, all validate success', async () => {
    const cmd = new TestValidateCmd()
    cmd.chain = 'test_validate_coin'
    cmd.getValidateInfo = jest.fn(() => ({
      pwd1: '1234',
      pwd2: '1234',
      password: '********',
      single: 1
    }))
    cmd.logger = {
      error: () => { },
      done: () => { }
    }
    const testData = [
      { address: '1', encryptPrivKey: 'password', password: 'password', public_key: 'public_key' },
      { address: '2', encryptPrivKey: 'password', password: 'password', public_key: 'public_key' },
      { address: '3', encryptPrivKey: 'password', password: 'password', public_key: 'public_key' },
      { address: '4', encryptPrivKey: 'password', password: 'password', public_key: '' },
      { address: '5', encryptPrivKey: 'password', password: 'password', public_key: 'public_key' }
    ]
    const findAllMock = jest.fn(() => testData)
    const countMock = jest.fn(() => testData.length)
    cmd.model = { count: countMock, findAll: findAllMock }
    await cmd.validate()
    expect(findAllMock.mock.calls.length).toEqual(1)
  })
})

describe('export command test', () => {
  const jwt = new Jwt({
    pubKey: path.join(process.cwd(), 'config', 'crypto', 'jwt_public_sample.pem'),
    privKey: path.join(process.cwd(), 'config', 'crypto', 'jwt_private_sample.pem')
  })
  class TestExportCmd extends BaseAccount { }

  it.skip('export test case, expect test', async () => {
    const cmd = new TestExportCmd()
    cmd.chain = 'test_export_coin'
    cmd.logger = {
      error: console.error.bind(console),
      done: console.log.bind(console)
    }
    cmd.config = {
      signer: {
        addrExport: {
          jwtOptions: {
            algorithm: 'ES256',
            noTimestamp: true
          },
          platform: 'huobipro',
          addrTypes: [
            {
              type: 4,
              indexes: [{ start: 1, end: 4 }]
            }
          ]
        }
      }
    }
    const findAllMock = jest.fn(() => [
      {
        address: 'address1',
        platform: 'huobipro',
        private_key: 'private_key1',
        public_key: 'public_key1'
      },
      {
        address: 'address2',
        platform: 'huobipro',
        private_key: 'private_key2',
        public_key: 'public_key2'
      },

      {
        address: 'address3',
        platform: 'huobipro',
        private_key: 'private_key3',
        public_key: 'public_key4'
      },
      {
        address: 'address4',
        platform: 'huobipro',
        private_key: 'private_key3',
        public_key: 'public_key4'
      }
    ])
    cmd.model = { findAll: findAllMock }

    const writeRowsMock = jest.fn()
    const getExportPathMock = jest.fn(() => '/tmp')
    const jwtMock = jest.fn(() => jwt)
    cmd.writeRows = writeRowsMock
    cmd.getExportPath = getExportPathMock
    cmd.getJwtTool = jwtMock
    await cmd.export()
    expect(writeRowsMock.mock.calls.length).toBe(1)

    // export 4 items + '\n'
    expect(writeRowsMock.mock.calls[0][0].trim('\n').split('\n').length).toBe(4)
    expect(writeRowsMock.mock.calls[0][0].trim('\n').split('\n')[0].split(',').length).toBe(4)
    expect(writeRowsMock.mock.calls[0][0].trim('\n').split('\n')[0].split(',').slice(0, 3)).toEqual(['address1', 'huobipro', '4'])
    expect(writeRowsMock.mock.calls.length).toBe(1)
  })

  it.skip('export test case, getRowsByConf', async () => {
    const cmd = new TestExportCmd()
    cmd.chain = 'test_export_coin'
    cmd.config = {
      signer: {
        addrExport: {
          jwtOptions: {
            algorithm: 'ES256',
            noTimestamp: true
          },
          platform: 'huobipro',
          addrTypes: [
            {
              type: 4,
              indexes: [{ start: 1, end: 2 }]
            }
          ]
        }
      }
    }
    cmd.model = {
      findAll: jest.fn().mockReturnValue([
        {
          address: 'address1',
          platform: 'huobipro',
          private_key: 'private_key1',
          public_key: 'public_key1'
        }
      ])
    }
    const values = await cmd.getRowsByConf(
      {
        exportFields: ['address'],
        coin: 'test_export_coin',
        platform: 'huobipro'
      },
      { start: 0, end: 1 }
    )
    expect(values).toEqual([
      {
        address: 'address1',
        platform: 'huobipro',
        private_key: 'private_key1',
        public_key: 'public_key1'
      }
    ])
  })

  it('export test case, getConfigExportList test', async () => {
    const cmd = new TestExportCmd()
    cmd.chain = 'test_export_coin'
    cmd.config = {
      signer: {
        addrExport: {
          jwtOptions: {
            algorithm: 'ES256',
            noTimestamp: true
          },
          platform: 'huobipro',
          addrTypes: [
            {
              type: 4,
              indexes: [{ start: 1, end: 50 }]
            },
            {
              type: 16,
              indexes: [{ start: 51, end: 100 }]
            }
          ]
        }
      }
    }
    const configExports = cmd.getConfiguredExportList()
    expect(configExports).toEqual([
      { end: 50, limit: 50, offset: 0, start: 1, type: 4 },
      { end: 100, limit: 50, offset: 50, start: 51, type: 16 }
    ])
  })

  it('export test case, getExportFields', async () => {
    const cmd = new TestExportCmd()
    cmd.chain = 'test_export_coin'
    cmd.config = {
      signer: {
        addrExport: {
          jwtOptions: {
            algorithm: 'ES256',
            noTimestamp: true
          },
          platform: 'huobipro',
          addrTypes: [
            {
              type: 4,
              indexes: [{ start: 1, end: 2 }]
            }
          ]
        }
      }
    }
    expect(cmd.getExportFields()).toEqual(['address'])
  })

  it('export test case, getExportFields with extra', async () => {
    const cmd = new TestExportCmd()
    cmd.chain = 'test_export_coin'
    cmd.config = {
      signer: {
        addrExport: {
          jwtOptions: {
            algorithm: 'ES256',
            noTimestamp: true
          },
          platform: 'huobipro',
          addrTypes: [
            {
              type: 4,
              indexes: [{ start: 1, end: 2 }]
            }
          ]
        }
      }
    }
    expect(cmd.getExportFields('platform')).toEqual(['address', 'platform'])
  })

  it('export test case, getExportPath', async () => {
    const cmd = new TestExportCmd()
    cmd.chain = 'test_export_coin'
    cmd.config = {
      signer: {
        addrExport: {
          exportPath: '/tmp',
          jwtOptions: {
            algorithm: 'ES256',
            noTimestamp: true
          },
          platform: 'huobipro',
          addrTypes: [
            {
              type: 4,
              indexes: [{ start: 1, end: 2 }]
            }
          ]
        }
      }
    }
    try {
      cmd.getExportPath(false)
    } catch (error) {
      expect(error).not.toBeNull()
    }
  })

  it('export test case, parseRow', async () => {
    const cmd = new TestExportCmd()
    cmd.chain = 'test_export_coin'
    const value = await cmd.parseRow(
      {
        address: 'address1',
        platform: 'huobipro',
        private_key: 'private_key1',
        public_key: 'public_key1'
      },
      {
        coin: 'test_export_coin',
        exportFields: ['address', 'platform'],
        jwtTool: jwt,
        addrExport: {
          jwtOptions: {
            algorithm: 'ES256',
            noTimestamp: true
          }
        }
      },
      { type: 4 }
    )
    expect(value.split(',').length).toBe(4)
    expect(value.split(',').slice(0, 3)).toEqual(['address1', 'huobipro', '4'])
  })
})
