import { isFunction, isAsyncFunction, getTimes } from '../src/module/utils'

describe('util test case', () => {
  it('isFunction', () => {
    expect(isFunction(() => {})).toBeTruthy()
    expect(isFunction(undefined)).toBeFalsy()
    expect(isFunction(null)).toBeFalsy()
  })

  it('isAsyncFunction', () => {
    expect(isAsyncFunction(async () => {})).toBeTruthy()
    expect(isAsyncFunction(() => {})).toBeFalsy()
    expect(isFunction(undefined)).toBeFalsy()
    expect(isFunction(null)).toBeFalsy()
  })

  it('getTimes', () => {
    const { times: times1, timeCount: timeCount1 } = getTimes(11, 10)
    expect(times1).toEqual(2)
    expect(timeCount1(1)).toEqual(10)
    expect(timeCount1(2)).toEqual(1)

    const { times: times2, timeCount: timeCount2 } = getTimes(10, 10)
    expect(times2).toEqual(1)
    expect(timeCount2(1)).toEqual(10)

    const { times: times3, timeCount: timeCount3 } = getTimes(33, 10)
    expect(times3).toEqual(4)
    expect(timeCount3(1)).toEqual(10)
    expect(timeCount3(2)).toEqual(10)
    expect(timeCount3(3)).toEqual(10)
    expect(timeCount3(4)).toEqual(3)

    const { times: times4, timeCount: timeCount4 } = getTimes(33, 12)
    expect(times4).toEqual(3)
    expect(timeCount4(1)).toEqual(12)
    expect(timeCount4(2)).toEqual(12)
    expect(timeCount4(3)).toEqual(9)
  })
})
