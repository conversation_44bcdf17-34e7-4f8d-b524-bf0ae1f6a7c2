{"name": "@common/sign", "version": "8.1.6", "description": "common sign package", "author": "blockchain-group", "license": "MIT", "type": "module", "exports": {"import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}, "publishConfig": {"access": "public"}, "files": ["dist"], "dependencies": {"@common/crypto": "workspace:*", "@common/utils": "workspace:*", "commander": "^2.20.0", "inquirer": "^6.5.2", "koa": "2.16.1", "koa-bodyparser": "4.4.1", "koa-router": "12.0.0", "koa-sslify": "5.0.1", "node-cron": "3.0.3", "progress": "^2.0.3", "shelljs": "0.8.5", "toml": "^3.0.0", "uuid": "^3.2.1", "mali": "0.47.2", "@grpc/reflection": "1.0.4", "@grpc/proto-loader": "0.7.13"}}