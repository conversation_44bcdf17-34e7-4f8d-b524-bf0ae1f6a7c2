const env = {
  disableRequestLogging: false,
  kafkaEnabled: false,
  kafkaOptions: {}
}

if (process.env.NODE_ENV === 'production') {
  env.disableRequestLogging = true
  env.kafkaEnabled = true
  env.kafkaOptions = {
    brokerUrls: [
      'kafka-1c-1.aws-jp1.huobiidc.com:9092',
      '...'
    ],
    options: {
      topic: 'blockchain-validator',
      component: 'validator',
      group: 'ens-service',
      level: 'INFO',
      platform: 'huobipro'
    }
  }
}

module.exports = {
  apps: [{
    name: 'ens-service',
    script: './dist/app.js',
    instances: '2',
    exec_mode: 'cluster',
    combine_logs: true,
    wait_ready: true,
    listen_timeout: 1000,
    env
  }]
}
