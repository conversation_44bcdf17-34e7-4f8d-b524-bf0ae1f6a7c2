{"name": "@standalone/ens-service", "version": "2.0.0", "description": "ens address format service", "author": "blockchain-group", "license": "ISC", "type": "module", "exports": {"import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}, "publishConfig": {"access": "public"}, "files": ["dist"], "scripts": {"start": "NODE_ENV=production node dist/app.js", "dev": "node dist/app.js"}, "dependencies": {"@ensdomains/address-encoder": "1.1.2", "@fastify/kafka": "2.2.1", "fastify": "5.3.2", "fastify-favicon": "4.3.0"}}