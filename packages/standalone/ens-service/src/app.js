import Fastify from 'fastify'
import fastifyFavicon from 'fastify-favicon'
import fastifyKafka from '@fastify/kafka'
import routes from './routes/index.js'
import { apps } from '../ecosystem.config.js'

const [{ env: { disableRequestLogging, kafkaEnabled, kafkaOptions: { brokerUrls, options } } }] = apps
const fastify = Fastify({ logger: true, maxParamLength: 300, disableRequestLogging })

// plugin: kafka
if (kafkaEnabled) {
  fastify
    .register(fastifyKafka, {
      producer: {
        'metadata.broker.list': brokerUrls.toString(),
        dr_cb: true
      }
    }).after(err => {
      if (err) {
        fastify.log.error(err)
        process.exit(1)
      }
    })
}
// plugin: favicon
fastify.register(fastifyFavicon)
// plugin: routes
fastify.register(routes, options)

fastify.listen({ port: 3000 }, err => {
  // only work on IPC channel derivation
  if (process.send) {
    // send the ready signal to PM2
    process.send('ready')
  }

  if (err) {
    fastify.log.error(err)
    process.exit(1)
  }
})
