import { getCoderByCoinName } from '@ensdomains/address-encoder'

const formatAddress = ({ symbol, data }) => {
  const addressBuffer = Buffer.from(data, 'hex')
  return getCoderByCoinName[symbol].encoder(addressBuffer)
}

export default (fastify, options, next) => {
  const { topic, component, group, level, platform } = options

  const kafkaPush = ({ id, params: { symbol, data } }, reply, address) => {
    const message = `${symbol}/${data}/${address}`
    const costTime = reply.getResponseTime()

    const payload = {
      component,
      group,
      ctx: { id },
      chain: 'ens',
      asset: symbol.toLowerCase(),
      platform,
      message,
      level,
      costTime
    }

    // kafka
    fastify.kafka.push({
      topic,
      payload: JSON.stringify(payload)
    })

    return { message, costTime }
  }

  fastify.get('/v1/:symbol/:data', (request, reply) => {
    // format address
    const address = formatAddress(request.params)

    // production make kafka and custom log
    if (process.env.NODE_ENV === 'production') {
      // for kafka
      request.log.info('ready for kafka push')
      const { message, costTime } = kafkaPush(request, reply, address)

      // for custom log
      request.log.info(`${message}, cost time: ${costTime} ms`)
    }

    reply.send({
      result: true,
      error: null,
      data: address
    })
  })

  next()
}
