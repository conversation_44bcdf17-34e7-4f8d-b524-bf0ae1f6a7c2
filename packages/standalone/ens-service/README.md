# ens-service

为 [ENS Domain](https://app.ens.domains/) 编写的 HTTP API 工具。
根据币链标识，格式化、转换地址，并在生产环境，将消息推送至kafka。

## 依赖
```bash
sudo apt install make g++
```

## 安装
```bash
npm install @validator/ens-service
```

## 使用

`ens-service` 在测试与生产环境，提供截然不同的两种行为模式

npm 脚本描述：

```
Ens Service 命令行模式，可用脚本如下：

  * start                     生产模式启动服务，加载kafka插件，生产者模式推送，并启动自定义终端日志模版（含单次请求耗时统计）
  * dev                       测试环境启动服务，不加载kafka插件，并启动fastify原生日志模版
  * test                      测试用例覆盖
```

pm2 配置描述：

```
Ens Service pm2模式，配置文件见根目录 ecosystem.config.js，特殊点描述如下：

  * instances                 实例数量
  * exec_mode                 搭配instances使用，cluster模式，单实例压测结果 QPS:77456次/秒
  * wait_ready                平滑启动、重启、reload服务监听
  * listen_timeout            默认3秒，本服务轻量，可降至1秒
  * env                       行为控制核心点
  ** disableRequestLogging    fastify终端日志默认模版启用开关
  ** kafkaEnabled             kafka启用开关
```

## 部署

测试环境：

```shell
> npm run dev                    # 脚本途径
> pm2 start                      # pm2途径
```

生产环境：

```shell
> npm start                      # 脚本途径
> pm2 start --env production     # pm2途径
```

## 调试

注：执行下方命令，终端需支持 jq

```shell
> curl -X 'GET' 'http://127.0.0.1:3000/v1/NEAR/36303134383361316232323639396236333666316466383030623962373039343636656261346531643563653763326531653230333137616638626264316633' -H 'accept: application/json' | jq
```

## Benchmarks

__Machine:__ EX41S-SSD, Intel Core i7, 4Ghz, 64GB RAM, 4C/8T, SSD.

__Method:__: `autocannon -c 100 -d 40 -p 10 localhost:3000` * 2, taking the
second average

| Framework          | Version                    | Router?      |  Requests/sec |
| :----------------- | :------------------------- | :----------: | ------------: |
| Express            | 4.17.3                     | &#10003;     | 14,200        |
| hapi               | 20.2.1                     | &#10003;     | 42,284        |
| Restify            | 8.6.1                      | &#10003;     | 50,363        |
| Koa                | 2.13.0                     | &#10007;     | 54,272        |
| **Fastify**        | **4.0.0**                  | **&#10003;** | **77,193**    |
| -                  |                            |              |               |
| `http.Server`      | 16.14.2	                  | &#10007;     | 74,513        |

Benchmarks taken using https://github.com/fastify/benchmarks. This is a
synthetic, "hello world" benchmark that aims to evaluate the framework overhead.
The overhead that each framework has on your application depends on your
application, you should __always__ benchmark if performance matters to you.

## TODO

- 测试用例覆盖
- 请求、响应结构体的 JSON SCHEMA 定义
- 结合 JSON SCHEMA 的结构体校验
- API 接口异常场景，错误码覆盖

## 文档

- [ENS对外服务接口规范](http://phabricator.huobidev.com/w/blockchain/innertechdoc/nodejs/ens/)
- [Registered Coin Types for BIP-0044](http://phabricator.huobidev.com/w/blockchain/innertechdoc/nodejs/ens0044/)

## License
**[MIT](https://github.com/fastify/fastify-cli/blob/master/LICENSE)**
