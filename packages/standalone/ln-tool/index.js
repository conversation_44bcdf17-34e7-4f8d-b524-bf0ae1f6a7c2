import mysql from 'promise-mysql'
import ProgressBar from 'progress'
import inquirer from 'inquirer'
import * as fs from 'fs'
import * as readline from 'readline'

const ln1Chain = 'ln1'
const ln2Chain = 'ln2'

function getMappingSql () {
  return `CREATE TABLE IF NOT EXISTS ${ln1Chain}_${ln2Chain}_address_mapping (
        id            INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
        ln1_address   VARCHAR(255)     NOT NULL,
        ln2_address   VARCHAR(255)     NOT NULL,
        created       TIMESTAMP        NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated       TIMESTAMP        NOT NULL DEFAULT CURRENT_TIMESTAMP
        ON UPDATE CURRENT_TIMESTAMP,
        INDEX (ln1_address),
        INDEX (ln2_address),
        UNIQUE (ln1_address),
        UNIQUE (ln2_address)
    )
        ENGINE = InnoDB
        DEFAULT CHARSET = utf8`
}

function getMappingDataSql (ln1Address) {
  return `select * from ${ln1Chain}_${ln2Chain}_address_mapping where ln1_address = '${ln1Address}';`
}

(async () => {
  const { path, host, port, username, password, total } = await inquirer.prompt([
    {
      type: 'input',
      name: 'path',
      message: 'Input file path'
    },
    {
      type: 'input',
      name: 'host',
      message: 'Input mysql host'
    },
    {
      type: 'input',
      name: 'port',
      message: 'Input mysql port'
    },
    {
      type: 'input',
      name: 'username',
      message: 'Input mysql username'
    },
    {
      type: 'password',
      name: 'password',
      mask: '*',
      message: 'Input mysql password'
    },
    {
      type: 'input',
      name: 'total',
      message: 'Input address total number'
    }
  ])

  const connection = await mysql.createConnection({
    host,
    port,
    user: username,
    password,
    database: 'validator',
    connectionLimit: 100
  })

  await connection.query(getMappingSql())

  const number = 10 * 10000
  const times = total / number
  let index = 1
  let data = []
  let input = 0
  let existNumber = 0
  const tableBar = new ProgressBar('import address [:bar] :current/:total', { total: times, width: 100 })

  const fileStream = fs.createReadStream(path)

  const rl = readline.createInterface({
    input: fileStream,
    crlfDelay: Infinity
  })

  for await (const line of rl) {
    const [ln1Address, ln2Address] = line.split(',')

    const mappingData = await connection.query(getMappingDataSql(ln1Address))
    if (mappingData.length <= 0) {
      data.push(`('${ln1Address}','${ln2Address}')`)
      input += 1
    } else {
      existNumber += 1
    }

    if (index % number === 0 || index === +total) {
      if (data.length > 0) {
        await connection.query(`insert into ${ln1Chain}_${ln2Chain}_address_mapping(ln1_address, ln2_address) values${data.join(',')};`)
      }

      tableBar.tick()

      data = []
    }

    index += 1
  }
  console.log('address mapping total:', total)
  console.log('mapped address total:', input)
  console.log('existed total:', existNumber)

  process.exit()
})()
