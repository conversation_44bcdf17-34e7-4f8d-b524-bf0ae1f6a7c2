#!/usr/bin/env zx
/* global fs, path, minimist */

async function main () {
  let txFile
  let config

  try {
    const { _: [chain, series], b: balance, help } = minimist(process.argv.slice(2), { boolean: ['help'], alias: { h: 'help' } })

    if (help) {
      console.log(`Usage: pnpm broadcast <chain> [series] [options]

      series:
        polkadot
        cosmos
        ethlike

      options:
        -b                              余额查询
        -h, --help                      帮助信息

      eg:
        pnpm broadcast ksm polkadot     广播系列币种
        pnpm broadcast algo             广播非系列币种
        pnpm broadcast algo -b          余额查询
      `)
      process.exit(0)
    }
    if (!chain) throw new Error('请输入要测试的币种名称，eg: dot')
    if (!series) {
      if (!fs.existsSync(path.join(__dirname, '../support', `${chain}.js`))) throw new Error('非系列，且无币种文件，请检查币种名称!')
      txFile = `${chain}.js`
    } else {
      config = require(`../support/${series}/config.json`)
      if (!config?.[chain]) throw new Error('系列，无币种配置，请检查配置信息!')
      txFile = `${series}/index.js`
    }

    const { default: Transaction } = await import(`../support/${txFile}`)
    const tx = new Transaction(chain, config?.[chain])
    await tx.checkAccount()
    if (balance) process.exit(0)
    await tx.getTxData()
    await tx.signTx()
    await tx.sendTx()
    await tx.checkTx()
  } catch (error) {
    console.error(`Error: ${error.message}`)
    process.exit(1)
  }
}

main()
