import _ from 'lodash'
import BigNumber from 'bignumber.js'

const { map, reduce } = _

export function getChangeOut ({ vin, vout, fee }) {
  const vinAmounts = map(vin, 'amount')
  const voutAmounts = map(vout, 'amount')
  const vinTotal = reduce(vinAmounts, (sum, amount) => sum.plus(new BigNumber(amount)), new BigNumber(0))
  const voutTotal = reduce(voutAmounts, (sum, amount) => sum.plus(new BigNumber(amount)), new BigNumber(0))
  const changeAmount = vinTotal.minus(voutTotal).minus(new BigNumber(fee))

  // 如果小于或者等于0，表明无需找零，返回false。
  if (changeAmount.isLessThanOrEqualTo(new BigNumber(0))) {
    return false
  }

  return {
    amount: changeAmount.toString(),
    address: vin[0].address
  }
}
