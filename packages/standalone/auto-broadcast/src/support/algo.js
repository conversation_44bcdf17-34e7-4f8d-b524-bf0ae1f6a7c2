export default class Transaction {
  constructor (chain) {
    this.chain = chain
    this.config = {
      from: 'A72A5BWTNGR5N24EPCIA33ZZ7VUB76MUFP3F65APL5NGCIQ4XRBHJEI3GE',
      privateKey: 'CjYOG2GDoCH0gOaNz2F4SgxmYyX2Wp2j9Iyp2oKhcjgH9A6G02mj1uuEeJAN7zn9aB/5lCv2X3QPX1phIhy8Qg==',
      to: 'MVXUSBORCNTF5M4TK6LEPQGKUQE56KVWVXCSSOMK7MSDWYVJGW25WNWSEM',
      amount: 104400,
      precision: 6,
      apiUrl: 'https://mainnet-api.4160.nodely.dev',
      chainId: 'mainnet-v1.0|wGHE2Pwdvd7S12BL5FaOP20EGYesN73ktiC1qzkkit8=',
      website: 'https://allo.info'
    }
  }

  async checkAccount () {
    try {
      const response = await fetch(`${this.config.apiUrl}/v2/accounts/${this.config.from}`)
      if (!response.ok) throw new Error(JSON.stringify(await response.json()))
      const accountInfo = await response.json()
      this.balance = accountInfo.amount
      console.log(`地址：${this.config.from}, 余额: ${this.balance}`)
    } catch (error) {
      throw new Error(`Error: ${error.message}`)
    }
  }

  async getTxData () {
    try {
      const transParamsResponse = await fetch(`${this.config.apiUrl}/v2/transactions/params`)
      if (!transParamsResponse.ok) throw new Error(JSON.stringify(await transParamsResponse.text()))
      const transParamsInfo = await transParamsResponse.json()
      this.refer_block_height = transParamsInfo['last-round']
      console.log('refer block height:', this.refer_block_height)
      this.fee = transParamsInfo['min-fee']
      console.log('suggested fee:', this.fee)
    } catch (error) {
      throw new Error(`获取链信息失败: ${error.message}`)
    }
  }

  async signTx () {
    try {
      const { Transaction } = await import(`@tokens/${this.chain}`)
      const tx = new Transaction().hbBuild({
        from: this.config.from,
        to: this.config.to,
        amount: String(this.config.amount),
        refer_block_height: this.refer_block_height,
        expire_block_height: this.refer_block_height + 60, // 出块速度 5s * 60 block = 5min
        fee: this.fee,
        chain_id: this.config.chainId
      })
      tx.hbSign([this.config.privateKey])

      this.txHash = tx.hbGetTxHash()
      console.log(`txHash: ${this.txHash}`)
      this.rawTx = tx.hbSerialize()
      console.log(`rawTx: ${this.rawTx}`)
    } catch (error) {
      throw new Error(`签名交易失败: ${error.message}`)
    }
  }

  async sendTx () {
    try {
      const txnBuffer = Buffer.from(this.rawTx, 'base64')
      const response = await fetch(`${this.config.apiUrl}/v2/transactions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-msgpack'
        },
        body: txnBuffer
      })
      if (!response.ok) throw new Error(JSON.stringify(await response.text()))
      console.log(`txHash: ${this.txHash}`)
      console.log('broadcast response:', await response.text())
    } catch (error) {
      throw new Error(`发送交易失败: ${error.message}`)
    }
  }

  async checkTx () {
    try {
      console.log('正在检查交易状态...')
      const response = await fetch(`${this.config.apiUrl}/v2/transactions/pending/${this.txHash}`)
      const res = await response.text()
      console.log('response:', res)
      const txRes = JSON.parse(res)
      if (txRes['confirmed-round'] > 0) {
        console.log(`交易状态: success\nwebsiteUrl: ${this.config.website}/tx/${this.txHash}`)
      } else {
        if (txRes['pool-error'] !== '') {
          console.log(`交易状态: failed\nwebsiteUrl: ${this.config.website}/tx/${this.txHash}`)
        } else {
          console.log('交易未完成，等待 30 秒后重新检查...')
          setTimeout(async () => {
            await this.checkTx()
          }, 30000) // 请求间隔太短可能导致“fetch failed”，建议每次请求间隔 30 秒
        }
      }
    } catch (error) {
      throw new Error(`校验上链信息失败: ${error.message}`)
    }
  }
}
