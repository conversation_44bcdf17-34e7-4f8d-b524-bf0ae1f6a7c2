import _ from 'lodash'
import BigNumber from 'bignumber.js'
import { post } from '../lib/utils.js'
const { keyBy } = _

export default class Transaction {
  constructor (chain) {
    this.chain = chain
    this.config = {
      vin: [
        {
          address: 'iota1qrsu8r7w5e2p9v894urhz0ugq03vmpfxgpclfrgyhctxc6qmre9zx8py2pr',
          privateKey: '0xd5344ac47b714a22f0cc6f3cb1c4d72b10f9e668e63c8ead126b74c182c2f106fbed326750bdb352d414ab8d2c4d0bbefcd7f263878989a5a36608750a271fce'
        }
      ],
      vout: [
        {
          address: 'iota1qp9uyhaaqcda4zxzspvf3ryuzkpedhyk8ssz9w5s4p8ut9jh3rsycrmcg2w',
          amount: '129000' // 测试的时候尽量保证前后两笔转账数目不相同 否则很难上链
        }
      ],
      precision: 6,
      apiUrl: 'https://api.stardust-mainnet.iotaledger.net', // 有些数据只能通过apiUrl取 其余均可以走rpcUrl
      rpcUrl: 'http://qukuai-iota-1f-1.qcloud-jp-wallet.huobiidc.com:8080',
      website: 'https://explorer.iota.org'
    }
  }

  async checkAccount () {
    try {
      const basicOutputIds = []
      const addrs = this.config.vin.map(v => v.address)
      for (let i = 0; i < addrs.length; i++) {
        const response = await fetch(`${this.config.apiUrl}/api/indexer/v1/outputs/basic?address=${addrs[i]}`)
        const basicOutput = await response.json()
        basicOutput.items.forEach(item => {
          basicOutputIds.push(item)
        })
      }
      const basicOutputs = []
      this.balance = new BigNumber(0)
      for (let j = 0; j < basicOutputIds.length; j++) {
        const response = await fetch(`${this.config.rpcUrl}/api/core/v2/outputs/${basicOutputIds[j]}`)
        const output = await response.json()
        this.balance = this.balance.plus(output.output.amount)
        basicOutputs.push(output)
      }
      this.basicOutputs = basicOutputs
      console.log(`各vin地址总余额: ${this.balance.toString()}`)
      const totalVoutAmount = this.config.vout[0].amount
      console.log(`各vout地址总花费金额: ${this.config.vout[0].amount}`) // 只需指定一个output 如有找零会自动追加
      if (this.balance.isLessThan(totalVoutAmount)) { // iota交易没有手续费 判断不带"="
        throw new Error('各vin地址总余额不足')
      }
    } catch (error) {
      throw new Error(`Error: ${error.message}`)
    }
  }

  async getTxData () {
    try {
      const { Client, Utils, CoinType, IOTA_BECH32_HRP } = await import(`@tokens/${this.chain}`)
      const onlineClient = new Client({
        nodes: [this.config.apiUrl]
      })
      const inputs = await onlineClient.findInputs(this.config.vin.map(v => v.address), BigInt(this.config.vout[0].amount))
      const privateKeyMap = keyBy(this.config.vin, 'address')
      this.privateKeys = []
      for (let i = 0; i < inputs.length; i++) {
        const outputInfo = this.basicOutputs.find(v => v.metadata.transactionId === inputs[i].transactionId)
        const unlockCondition = outputInfo.output.unlockConditions[0]
        const addr = Utils.hexToBech32(unlockCondition.address.pubKeyHash, IOTA_BECH32_HRP)
        this.privateKeys.push(privateKeyMap[addr].privateKey)
      }
      const preparedTransaction = await onlineClient.prepareTransaction(
        undefined,
        {
          coinType: CoinType.IOTA,
          inputs,
          output: { address: this.config.vout[0].address, amount: this.config.vout[0].amount } // 只需指定一个output 如有找零会自动追加
        }
      )
      delete preparedTransaction.remainder
      preparedTransaction.essence.outputs.map(output => {
        output.unlockConditions.map(unlockCondition => {
          unlockCondition.address.pubKeyHash = Utils.hexToBech32(unlockCondition.address.pubKeyHash, IOTA_BECH32_HRP)
          return unlockCondition
        })
        return output
      })
      preparedTransaction.inputsData.map(inputData => {
        inputData.output.unlockConditions.map(unlockCondition => {
          unlockCondition.address.pubKeyHash = Utils.hexToBech32(unlockCondition.address.pubKeyHash, IOTA_BECH32_HRP)
          return unlockCondition
        })
        return inputData
      })
      this.preparedTransaction = JSON.stringify(preparedTransaction)
      console.log(`Prepared transaction ${this.preparedTransaction}`)
    } catch (error) {
      throw new Error(`获取链信息失败: ${error.message}`)
    }
  }

  async signTx () {
    try {
      const { Transaction } = await import(`@tokens/${this.chain}`)
      const tx = new Transaction().hbBuild({
        // iota构建交易不用传vin & vout
        to_tag: this.preparedTransaction
      })
      await tx.hbSign(this.privateKeys)

      this.txHash = tx.hbGetTxHash()
      console.log(`txHash: ${this.txHash}`)
      this.rawTx = tx.hbSerialize()
      console.log(`rawTx: ${this.rawTx}`)
    } catch (error) {
      throw new Error(`签名交易失败: ${error.message}`)
    }
  }

  async sendTx () {
    try {
      const response = await post(`${this.config.rpcUrl}/api/core/v2/blocks`, JSON.parse(this.rawTx))
      console.log(`txHash: ${this.txHash}`)
      console.log('broadcast response:', await response)
      this.blockId = response.blockId
    } catch (error) {
      throw new Error(`发送交易失败: ${error.message}`)
    }
  }

  async checkTx () {
    try {
      console.log('正在检查交易状态...')
      const response = await fetch(`${this.config.rpcUrl}/api/core/v2/outputs/${this.txHash}`)
      const { metadata } = await response.json()
      if (metadata?.transactionId && metadata?.transactionId === this.txHash && metadata?.blockId === this.blockId) {
        console.log(`交易状态: success\nwebsiteUrl: ${this.config.website}/mainnet/transaction/${this.txHash}`)
      } else {
        console.log('交易未完成，等待 30 秒后重新检查...')
        setTimeout(async () => {
          await this.checkTx()
        }, 30000) // 有时候上链比较慢
      }
    } catch (error) {
      throw new Error(`校验上链信息失败: ${error.message}`)
    }
  }
}
