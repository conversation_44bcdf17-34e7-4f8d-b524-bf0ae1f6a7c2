export default class Transaction {
  constructor (chain, config) {
    this.chain = chain
    this.config = config
  }

  async checkAccount () {
    try {
      const response = await fetch(`${this.config.apiUrl}/api/v2/scan/search`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ key: this.config.from })
      })
      if (!response.ok) throw new Error(JSON.stringify(await response.json()))
      const { data: { account: { balance, nonce } = {} } = {} } = await response.json()
      this.nonce = nonce
      this.balance = balance
      console.log(`地址：${this.config.from}, 余额: ${this.balance}`)
    } catch (error) {
      throw new Error(`Error: ${error.message}`)
    }
  }

  async getTxData () {
    try {
      const response = await fetch(`${this.config.rpcUrl}:8080/transaction/material?metadata=scale`)
      if (!response.ok) throw new Error(JSON.stringify(await response.json()))
      const { at: { hash, height }, genesisHash, chainName, specName, specVersion, txVersion, metadata } = await response.json()
      this.blockNumber = height
      this.blockHash = hash
      this.genesisHash = genesisHash
      this.transactionVersion = txVersion
      this.chainName = chainName
      this.specName = specName
      this.specVersion = specVersion
      this.metadata = metadata
    } catch (error) {
      throw new Error(`获取链信息失败: ${error.message}`)
    }
  }

  async signTx () {
    try {
      const { Transaction } = await import(`@tokens/${this.chain}`)
      const tx = await new Transaction().hbBuild({
        era_period: 300,
        tip: 0,
        block_number: this.blockNumber,
        block_hash: this.blockHash,
        genesis_hash: this.genesisHash,
        transaction_version: this.transactionVersion,
        chain_name: this.chainName,
        spec_name: this.specName,
        spec_version: this.specVersion,
        metadata: this.metadata,
        from: this.config.from,
        to: this.config.to,
        amount: this.config.amount,
        nonce: this.nonce
      }).hbSign([this.config.privateKey])
      this.rawTx = tx.hbSerialize()
      console.log(`rawTx: ${this.rawTx}`)
    } catch (error) {
      throw new Error(`签名交易失败: ${error.message}`)
    }
  }

  async sendTx () {
    try {
      const response = await fetch(`${this.config.rpcUrl}:8080/transaction`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ tx: this.rawTx })
      })
      if (!response.ok) throw new Error(JSON.stringify(await response.json()))
      this.txHash = (await response.json())?.hash
      console.log(`txHash: ${this.txHash}`)
    } catch (error) {
      throw new Error(`发送交易失败: ${error.message}`)
    }
  }

  async checkTx () {
    try {
      const response = await fetch(`${this.config.apiUrl}/api/scan/extrinsic`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ hash: this.txHash })
      })
      if (!response.ok) throw new Error(JSON.stringify(await response.json()))
      const { data: { finalized, success } } = await response.json()
      if (finalized) {
        console.log(`交易状态: ${success}\nwebsiteUrl: ${this.config.website}/${this.txHash}`)
      } else {
        console.log('交易未完成，等待 10 秒后重新检查...')
        setTimeout(async () => {
          await this.checkTx()
        }, 10000)
      }
    } catch (error) {
      throw new Error(`校验上链信息失败: ${error.message}`)
    }
  }
}
