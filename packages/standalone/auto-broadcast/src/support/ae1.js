import BigNumber from 'bignumber.js'
import { post } from '../lib/utils.js'

export default class Transaction {
  constructor (chain) {
    this.chain = chain
    this.config = {
      from: 'ak_Yrf6EBis6nsmXXC4cpCQnsTnACFNyaXCMJjCg9E1LboDRJ9VE',
      privateKey: '47d6452bd1cc7adf8fe61e6099f95ec3181dfdf44cae4c22e3e442b51c05bdba485552465dc651363b7dadaa428a103621ca30b596b5e3ce500dfb4df26c996b',
      to: 'ak_2DJZegQneyS5Unwc9t6rsJZGWaXwiPtQQfH4iVFvAzitdDc9C9',
      amount: ****************00,
      fee: ****************,
      precision: 18,
      rpcUrl: 'http://qukuai-ae-1a-1.aws-jp1.huobiidc.com:8080',
      website: 'https://aescan.io'
    }
  }

  async checkAccount () {
    try {
      const response = await fetch(`${this.config.rpcUrl}/v2/accounts/${this.config.from}`)
      if (!response.ok) throw new Error(JSON.stringify(await response.json()))
      const { nonce, balance } = await response.json()
      this.nonce = nonce
      this.balance = new BigNumber(balance)
      console.log(`地址：${this.config.from}, 余额: ${this.balance.dividedBy(10 ** this.config.precision).toString()}, nonce: ${this.nonce}`)
    } catch (error) {
      throw new Error(`Error: ${error.message}`)
    }
  }

  async getTxData () {
    try {
      const response = await fetch(`${this.config.rpcUrl}/v2/blocks/top`)
      if (!response.ok) throw new Error(JSON.stringify(await response.json()))
      const { micro_block: { height } } = await response.json()
      this.height = height
      console.log('height: ', this.height)
    } catch (error) {
      throw new Error(`获取链信息失败: ${error.message}`)
    }
  }

  async signTx () {
    try {
      const { Transaction } = await import(`@tokens/${this.chain}`)
      const tx = await new Transaction().hbBuild({
        chain_id: 'ae_mainnet',
        from: this.config.from,
        to: this.config.to,
        amount: this.config.amount,
        expire_block_height: this.height + 20,
        fee: this.config.fee,
        nonce: this.nonce + 1,
        to_tag: 'test'
      }).hbSign([this.config.privateKey])
      this.rawTx = tx.hbSerialize()
      console.log(`rawTx: ${this.rawTx}`)
    } catch (error) {
      throw new Error(`签名交易失败: ${error.message}`)
    }
  }

  async sendTx () {
    try {
      const response = await post(`${this.config.rpcUrl}/v2/transactions`, {
        tx: this.rawTx
      })
      console.log('broadcast response:', response)
      this.txHash = response.tx_hash
      console.log('txHash:', this.txHash)
    } catch (error) {
      throw new Error(`发送交易失败: ${error.message}`)
    }
  }

  async checkTx () {
    try {
      const response = await fetch(`${this.config.rpcUrl}/v2/transactions/${this.txHash}`)
      if (!response.ok) throw new Error(JSON.stringify(await response.json()))
      const { block_hash: blockHash } = await response.json()
      if (blockHash) {
        console.log(`交易状态: success\nwebsiteUrl: ${this.config.website}/transactions/${this.txHash}`)
      } else {
        console.log('交易未完成，等待 10 秒后重新检查...')
        setTimeout(async () => {
          await this.checkTx()
        }, 10000)
      }
    } catch (error) {
      throw new Error(`校验上链信息失败: ${error.message}`)
    }
  }
}
