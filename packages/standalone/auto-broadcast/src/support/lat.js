export default class Transaction {
  constructor (chain) {
    this.chain = chain
    this.config = {
      privateKey: '0x494b292cfbb4162df741367e9d30eaeaa5fba51376437494bdb9dbd12d30be0d',
      from: 'lat1mdakyc6n665txvm9ejgfchwx84p3eq6m2ur0md',
      to: 'lat1vwjqnnrj9ws03cwaakardkd6jdrqd0flqgjhxu',
      to_tag: 'test',
      amount: '***********00000000',
      chain_id: '210309',
      nonce: '1',
      fee_price: '***********',
      fee_step: '942477',
      apiUrl: 'https://openapi2.platon.network/rpc',
      website: 'https://scan.platon.network/trade-detail?txHash='
    }
  }

  async checkAccount () {
    try {
      const blockRes = await fetch(`${this.config.apiUrl}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          jsonrpc: '2.0',
          bech32: true,
          method: 'platon_blockNumber',
          params: [],
          id: 67
        })
      })
      if (!blockRes.ok) throw new Error(JSON.stringify(await blockRes.json()))
      const blockInfo = await blockRes.json()
      if (blockInfo.error) throw new Error(blockInfo.error.message)

      console.log(`blockNumber: ${parseInt(blockInfo.result, 16)}`)
      const accountRes = await fetch(`${this.config.apiUrl}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          jsonrpc: '2.0',
          bech32: true,
          method: 'platon_getProof',
          params: [this.config.from, [], `${blockInfo.result}`],
          id: 67
        })
      })
      if (!accountRes.ok) throw new Error(JSON.stringify(await accountRes.json()))
      const accountInfo = await accountRes.json()
      if (accountInfo.error) throw new Error(accountInfo.error.message)
      this.config.nonce = `${parseInt(accountInfo.result.nonce, 16)}`
      console.log(`地址：${this.config.from},余额: ${parseInt(accountInfo.result.balance, 16)},nonce: ${this.config.nonce}`)
    } catch (error) {
      throw new Error(`Error: ${error.message}`)
    }
  }

  async getTxData () {
    try {
      // nonce 已在checkAccount中获取，无需额外获取
    } catch (error) {
      throw new Error(`获取链信息失败: ${error.message}`)
    }
  }

  async signTx () {
    try {
      const { Transaction } = await import(`@tokens/${this.chain}`)
      const tx = await new Transaction().hbBuild({
        to: this.config.to,
        amount: this.config.amount,
        chain_id: this.config.chain_id,
        fee_price: this.config.fee_price,
        fee_step: this.config.fee_step,
        nonce: this.config.nonce,
        to_tag: this.config.to_tag
      }).hbSign([this.config.privateKey])
      this.txHash = tx.hbGetTxHash()
      console.log(`txHash: ${this.txHash}`)
      this.rawTx = tx.hbSerialize()
      console.log(`rawTx: ${this.rawTx}`)
    } catch (error) {
      throw new Error(`签名交易失败: ${error.message}`)
    }
  }

  async sendTx () {
    try {
      const response = await fetch(`${this.config.apiUrl}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          jsonrpc: '2.0',
          bech32: true,
          method: 'platon_sendRawTransaction',
          params: [{ data: this.rawTx }],
          id: 67
        })
      })
      if (!response.ok) throw new Error(JSON.stringify(await response.json()))
      const txInfo = await response.json()
      if (txInfo.error) throw new Error(txInfo.error.message)
      this.txHash = txInfo.result
    } catch (error) {
      throw new Error(`发送交易失败: ${error.message}`)
    }
  }

  async checkTx () {
    try {
      console.log('正在检查交易状态...')
      const response = await fetch(`${this.config.apiUrl}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          jsonrpc: '2.0',
          bech32: true,
          method: 'platon_getTransactionReceipt',
          params: [this.txHash],
          id: 67
        })
      })
      if (!response.ok) throw new Error(JSON.stringify(await response.json()))
      const txInfo = await response.json()
      if (txInfo?.result?.transactionHash === this.txHash) {
        console.log(`交易状态: success\nwebsiteUrl: ${this.config.website}${this.txHash}`)
      } else {
        console.log('交易未完成，等待 30 秒后重新检查...')
        setTimeout(async () => {
          await this.checkTx()
        }, 30000) // 有时候上链比较慢
      }
    } catch (error) {
      throw new Error(`校验上链信息失败: ${error.message}`)
    }
  }
}
