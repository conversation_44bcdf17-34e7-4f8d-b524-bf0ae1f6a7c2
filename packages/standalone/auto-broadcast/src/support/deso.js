import { post } from '../lib/utils.js'

export default class Transaction {
  constructor (chain) {
    this.chain = chain
    this.config = {
      from: 'BC1YLj9ndHWYVG3Hx931fdTMXUK76yguwCGofwQBct6rTmA92sukMpL',
      privateKey: '1610e1ae2f9aaf9169a00bfa893f41deed037d9e753f2dd122fc3dc8017bd4a0',
      to: 'BC1YLjRF7NGA9x6mSXDaiHNige5KnjRro1BzR11kZL2GEivcNq7u2nX',
      amount: '168000',
      apiUrl: 'https://node.deso.org',
      website: 'https://explorer.deso.com'
    }
  }

  async checkAccount () {
    try {
      const response = await post(`${this.config.apiUrl}/api/v1/balance`, {
        PublicKeyBase58Check: this.config.from
      })
      this.balance = response.ConfirmedBalanceNanos
      console.log(`地址：${this.config.from}, 余额: ${this.balance}`)
    } catch (error) {
      throw new Error(`Error: ${error.message}`)
    }
  }

  async getTxData () {
    try {
      const response = await post(`${this.config.apiUrl}/api/v1/node-info`, {})
      const { LatestBlockHeight } = response.DeSoStatus
      this.expire_block_height = LatestBlockHeight + Math.floor(Math.random() * 275)
      this.partial_id = Math.floor(Math.random() * Number.MAX_SAFE_INTEGER)
      console.log('expire_block_height:', this.expire_block_height)
      console.log('partial_id:', this.partial_id)
    } catch (error) {
      throw new Error(`获取链信息失败: ${error.message}`)
    }
  }

  async signTx () {
    try {
      const { Transaction } = await import(`@tokens/${this.chain}`)
      const tx = await new Transaction().hbBuild({
        from: this.config.from,
        to: this.config.to,
        amount: this.config.amount,
        fee: '666', // fee是一个变化的值 e.g: "TxnFeeNanos": 248 一般设置1000以内即可
        expire_block_height: this.expire_block_height,
        partial_id: this.partial_id
      })
      await tx.hbSign([this.config.privateKey])
      this.rawTx = tx.hbSerialize()
      console.log(`rawTx: ${this.rawTx}`)
    } catch (error) {
      throw new Error(`签名交易失败: ${error.message}`)
    }
  }

  async sendTx () {
    try {
      const response = await post(`${this.config.apiUrl}/api/v0/submit-transaction`, {
        TransactionHex: this.rawTx
      })
      this.txHash = response.TxnHashHex
      console.log(`txHash: ${this.txHash}`)
      console.log('broadcast response:', response)
    } catch (error) {
      throw new Error(`发送交易失败: ${error.message}`)
    }
  }

  async checkTx () {
    try {
      console.log('正在检查交易状态...')
      const response = await post(`${this.config.apiUrl}/api/v0/get-txn`, {
        TxnHashHex: this.txHash
      })
      const { TxnFound } = response
      if (TxnFound) {
        console.log(`交易状态: success\nwebsiteUrl: ${this.config.website}/txn/${this.txHash}`)
      } else {
        console.log('交易未完成，等待 10 秒后重新检查...')
        setTimeout(async () => {
          await this.checkTx()
        }, 10000) // 交易成功以后 查询交易状态一定要耐心等待...
      }
    } catch (error) {
      throw new Error(`校验上链信息失败: ${error.message}`)
    }
  }
}
