import { post } from '../lib/utils.js'

export default class Transaction {
  constructor (chain) {
    this.chain = chain
    this.config = {
      from: 'htx-testa',
      privateKey: '5Kg4e7T7dX6G3zmZphpVoKrp4WwMJN98vyMTkBAF7xdnarqc8XB',
      to: 'htx-testb',
      amount: '0.256',
      rpcUrl: 'http://qukuai-steem-1a-1.aws-jp1.huobiidc.com:8080',
      website: 'https://steemworld.org'
    }
  }

  async checkAccount () {
    try {
      const response = await post(`${this.config.rpcUrl}`, {
        id: '1',
        jsonrpc: '2.0',
        method: 'condenser_api.get_accounts',
        params: [[this.config.from]]
      })
      this.balance = response.result[0]?.balance
      console.log(`地址：${this.config.from}, 余额: ${this.balance}`)
    } catch (error) {
      throw new Error(`Error: ${error.message}`)
    }
  }

  async getTxData () {
    try {
      const response = await post(`${this.config.rpcUrl}`, {
        id: '1',
        jsonrpc: '2.0',
        method: 'condenser_api.get_dynamic_global_properties',
        params: []
      })
      this.refer_block_height = response.result.head_block_number
      this.block_id = response.result.head_block_id
      this.expire_date = new Date(new Date(`${response.result.time}.000z`).getTime() + 3600 * 1000).toISOString().slice(0, 19)
      console.log('refer_block_height:', this.refer_block_height)
      console.log('block_id:', this.block_id)
      console.log('expire_date:', this.expire_date)
    } catch (error) {
      throw new Error(`获取链信息失败: ${error.message}`)
    }
  }

  async signTx () {
    try {
      const { Transaction } = await import(`@tokens/${this.chain}`)
      const tx = new Transaction().hbBuild({
        tx_type: 'transfer',
        from: this.config.from,
        to: this.config.to,
        amount: this.config.amount,
        amount_asset: 'STEEM',
        refer_block_height: this.refer_block_height,
        block_id: this.block_id,
        expire_date: this.expire_date
      }).hbSign([this.config.privateKey])
      // 注意：交易上链前无法拿到交易hash
      this.rawTx = tx.hbSerialize()
      console.log(`rawTx: ${this.rawTx}`)
    } catch (error) {
      throw new Error(`签名交易失败: ${error.message}`)
    }
  }

  async sendTx () {
    try {
      const response = await post(`${this.config.rpcUrl}`, {
        id: '1',
        jsonrpc: '2.0',
        method: 'condenser_api.broadcast_transaction_synchronous',
        params: [JSON.parse(this.rawTx)]
      })
      this.block_num = response.result.block_num
      this.trx_num = response.result.trx_num
      // 注意：交易上链后才能拿到交易hash
      this.txHash = response.result.id
      console.log(`txHash: ${this.txHash}`)
      console.log('broadcast response:', response)
    } catch (error) {
      throw new Error(`发送交易失败: ${error.message}`)
    }
  }

  async checkTx () {
    try {
      console.log('正在检查交易状态...')
      const response = await post(`${this.config.rpcUrl}`, {
        id: '1',
        jsonrpc: '2.0',
        method: 'condenser_api.get_transaction',
        params: [this.txHash]
      })
      const { result, error } = response
      if (result && result.transaction_id === this.txHash) {
        console.log(`交易状态: success\nwebsiteUrl: ${this.config.website}/block/${this.block_num}/${this.block_num}-${this.trx_num}`)
      } else {
        console.log('交易未完成，等待 10 秒后重新检查...')
        if (error) {
          console.log('交易状态:', error.message, '请耐心等待...')
        }
        setTimeout(async () => {
          await this.checkTx()
        }, 10000) // 交易成功以后 查询交易状态一定要耐心等待...
      }
    } catch (error) {
      throw new Error(`校验上链信息失败: ${error.message}`)
    }
  }
}
