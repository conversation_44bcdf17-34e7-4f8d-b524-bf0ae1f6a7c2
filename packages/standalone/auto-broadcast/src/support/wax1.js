export default class Transaction {
  constructor (chain) {
    this.chain = chain
    this.config = {
      from: 'axrjocxepu35',
      privateKey: '5KW1j8u89Xt8YBtRMCM2jXthYzPs1fk3goZaVxuyPyQ7ZGy7ARS',
      to: 'adknxcavif31',
      amount: 100000,
      precision: 8,
      rpcUrl: 'http://qukuai-wax-1f-1.qcloud-jp-wallet.huobiidc.com:8080',
      // 上面的rpcUrl不支持/v1/history/get_transaction接口，请使用greymass的api（如果请求不通 请先翻墙）
      apiUrl: 'https://wax.greymass.com',
      website: 'https://wax.bloks.io/transaction'
    }
  }

  async checkAccount () {
    try {
      const response = await fetch(`${this.config.rpcUrl}/v1/chain/get_account`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ account_name: this.config.from })
      })
      if (!response.ok) throw new Error(JSON.stringify(await response.json()))
      const accountInfo = await response.json()
      this.balance = accountInfo.core_liquid_balance
      console.log(`地址：${this.config.from}, 余额: ${this.balance}`)
    } catch (error) {
      throw new Error(`Error: ${error.message}`)
    }
  }

  async getTxData () {
    try {
      const chainResponse = await fetch(`${this.config.rpcUrl}/v1/chain/get_info`)
      if (!chainResponse.ok) throw new Error(JSON.stringify(await chainResponse.text()))
      const chainInfo = await chainResponse.json()
      this.chain_id = chainInfo.chain_id
      this.refer_block_height = chainInfo.head_block_num
      console.log('chain_id:', this.chain_id)
      console.log('refer_block_height:', this.refer_block_height)
      const blockResponse = await fetch(`${this.config.rpcUrl}/v1/chain/get_block`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ block_num_or_id: this.refer_block_height })
      })
      if (!blockResponse.ok) throw new Error(JSON.stringify(await blockResponse.text()))
      const blockInfo = await blockResponse.json()
      this.ref_block_prefix = blockInfo.ref_block_prefix
      console.log('ref_block_prefix:', this.ref_block_prefix)
      this.expire_time = new Date().getTime() + 3600 * 1000
      console.log('expire_time:', this.expire_time)
    } catch (error) {
      throw new Error(`获取链信息失败: ${error.message}`)
    }
  }

  async signTx () {
    try {
      const { Transaction } = await import(`@tokens/${this.chain}`)
      const tx = await new Transaction().hbBuild({
        from: this.config.from,
        to: this.config.to,
        to_tag: '',
        amount: String(this.config.amount),
        refer_block_height: this.refer_block_height,
        ref_block_prefix: this.ref_block_prefix,
        expire_time: this.expire_time,
        chain_id: this.chain_id
      })
      await tx.hbSign([this.config.privateKey])

      this.txHash = tx.hbGetTxHash()
      console.log(`txHash: ${this.txHash}`)
      this.rawTx = tx.hbSerialize()
      console.log(`rawTx: ${this.rawTx}`)
    } catch (error) {
      throw new Error(`签名交易失败: ${error.message}`)
    }
  }

  async sendTx () {
    try {
      const response = await fetch(`${this.config.rpcUrl}/v1/chain/push_transaction `, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: `${this.rawTx}`
      })
      if (!response.ok) throw new Error(JSON.stringify(await response.text()))
      console.log(`txHash: ${this.txHash}`)
      console.log('broadcast response:', await response.text())
    } catch (error) {
      throw new Error(`发送交易失败: ${error.message}`)
    }
  }

  async checkTx () {
    try {
      console.log('正在检查交易状态...')
      const response = await fetch(`${this.config.apiUrl}/v1/history/get_transaction`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ id: this.txHash })
      })
      const res = await response.text()
      const { error, id, irreversible } = JSON.parse(res)
      if (error === 'not found') {
        console.log('交易未完成，等待 10 秒后重新检查...')
        setTimeout(async () => {
          await this.checkTx()
        }, 10000)
      } else {
        if (id && this.txHash === id && irreversible) {
          console.log(`交易状态: success\nwebsiteUrl: ${this.config.website}/${this.txHash}`)
        }
      }
    } catch (error) {
      throw new Error(`校验上链信息失败: ${error.message}`)
    }
  }
}
