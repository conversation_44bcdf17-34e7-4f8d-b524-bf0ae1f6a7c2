export default class Transaction {
  constructor (chain) {
    this.chain = chain
    this.config = {
      from: '5Q3nFODEAY7Vb7kh-bf3CzBsUeCbvbLXmn-LhLdPKe0',
      privateKey: `**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`,
      to: 'SLu2UkE6pxTiQ98qddvZ7tdQzGVTTqyTIPRuUhHITZc',
      amount: 1000000,
      precision: 12,
      externalFee: ***********,
      rpcUrl: 'http://qukuai-ar-1e-1.qcloud-jp-wallet.huobiidc.com:8080',
      website: 'https://viewblock.io/arweave/tx'
    }
  }

  async checkAccount () {
    try {
      const response = await fetch(`${this.config.rpcUrl}/wallet/${this.config.from}/balance`)
      if (!response.ok) throw new Error(JSON.stringify(await response.json()))
      const balance = await response.json()
      this.balance = balance
      console.log(`地址：${this.config.from}, 余额: ${this.balance}`)
    } catch (error) {
      throw new Error(`Error: ${error.message}`)
    }
  }

  async getTxData () {
    try {
      const anchorResponse = await fetch(`${this.config.rpcUrl}/tx_anchor`)
      if (!anchorResponse.ok) throw new Error(JSON.stringify(await anchorResponse.text()))
      const randomFeeResponse = await fetch(`${this.config.rpcUrl}/price/0/${this.config.to} `)
      if (!randomFeeResponse.ok) throw new Error(JSON.stringify(await randomFeeResponse.json()))
      this.text_nonce = await anchorResponse.text()
      console.log('nonce:', this.text_nonce)
      const randomFee = await randomFeeResponse.json()
      console.log('randomFee:', randomFee)
      if (randomFee > this.externalFee) {
        this.fee = String(randomFee)
      } else {
        this.fee = String(randomFee + Math.floor(200000000 * (1 + Math.random())))
      }
      console.log('fee:', this.fee)
    } catch (error) {
      throw new Error(`获取链信息失败: ${error.message}`)
    }
  }

  async signTx () {
    try {
      const { Transaction } = await import(`@tokens/${this.chain}`)
      const tx = await new Transaction().hbBuild({
        from: this.config.from,
        to: this.config.to,
        to_tag: '',
        amount: String(this.config.amount),
        fee: this.fee,
        text_nonce: this.text_nonce
      }).hbSign([this.config.privateKey])

      this.txHash = tx.hbGetTxHash()
      console.log(`txHash: ${this.txHash}`)
      this.rawTx = tx.hbSerialize()
      console.log(`rawTx: ${this.rawTx}`)
    } catch (error) {
      throw new Error(`签名交易失败: ${error.message}`)
    }
  }

  async sendTx () {
    try {
      const response = await fetch(`${this.config.rpcUrl}/tx`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: `${this.rawTx}`
      })
      if (!response.ok) throw new Error(JSON.stringify(await response.text()))
      console.log(`txHash: ${this.txHash}`)
      console.log('broadcast response:', await response.text())
    } catch (error) {
      throw new Error(`发送交易失败: ${error.message}`)
    }
  }

  async checkTx () {
    try {
      console.log('正在检查交易状态...')
      const response = await fetch(`${this.config.rpcUrl}/tx/${this.txHash}`)
      const res = await response.text()
      if (res === 'Pending' || res === 'Not Found.') {
        console.log('交易未完成，等待 60 秒后重新检查...')
        setTimeout(async () => {
          await this.checkTx()
        }, 60000) // ar上链比较慢 checkTx间隔时间改为1分钟
      } else {
        const { id, target } = JSON.parse(res)
        if (id && target &&
          this.txHash === id &&
          this.config.to === target
        ) {
          console.log(`交易状态: success\nwebsiteUrl: ${this.config.website}/${this.txHash}`)
        }
      }
    } catch (error) {
      throw new Error(`校验上链信息失败: ${error.message}`)
    }
  }
}
