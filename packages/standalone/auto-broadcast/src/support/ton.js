import BigNumber from 'bignumber.js'
import { post } from '../lib/utils.js'

export default class Transaction {
  constructor (chain) {
    this.chain = chain
    this.config = {
      from: 'EQDE33kQQTlr86KQOzqXtszFi9bmpAVshCkqzTF339hsucIH',
      privateKey: 'b6cfb1bbbb11a1ed563040b7c5e22521c066c099abb9929200361f4b1dcd2ac662f0ff21302435cdeaa208403286d79ff43e467a42ec3b5f0734fa20355c0ee3',
      to: 'EQBadiNqr5oQcDK5VOFaoElVBTux3bFhosPyw-Tq3MZZTPbs',
      amount: '0.0168',
      to_tag: 'test168',
      precision: 9,
      rpcUrl: 'http://qukuai-ton-1a-1.aws-jp1.huobiidc.com:8080',
      apiUrl: 'https://tonapi.io', // 因为广播上链之前无法获取到txhash rpcUrl不能直接获取交易信息 所以用apiUrl从另一个角度获取信息进行比对
      website: 'https://tonscan.org'
    }
  }

  async checkAccount () {
    try {
      const response = await fetch(`${this.config.rpcUrl}/getExtendedAddressInformation?address=${this.config.from}`)
      if (!response.ok) throw new Error(JSON.stringify(await response.json()))
      const accountInfo = await response.json()
      this.balance = new BigNumber(accountInfo.result.balance)
      console.log(`地址：${this.config.from}, 余额: ${this.balance.dividedBy(10 ** this.config.precision).toString()}`)
      this.sequence = accountInfo.result.account_state.seqno
      console.log(`sequence: ${this.sequence}`)
    } catch (error) {
      throw new Error(`Error: ${error.message}`)
    }
  }

  async getTxData () {
    try {
      this.expire_time = Math.floor(new Date().getTime() / 1000) + 600
      console.log('expire_time:', this.expire_time)
    } catch (error) {
      throw new Error(`获取链信息失败: ${error.message}`)
    }
  }

  async signTx () {
    try {
      const { Transaction } = await import(`@tokens/${this.chain}`)
      const tx = await new Transaction().hbBuild({
        from: this.config.from,
        to: this.config.to,
        nonce: this.sequence,
        expire_time: this.expire_time,
        amount: this.config.amount,
        to_tag: this.config.to_tag
      })
      await tx.hbSign([this.config.privateKey])
      // TON链交易在广播上链之前 无法获取到txhash
      this.rawTx = tx.hbSerialize()
      console.log(`rawTx: ${this.rawTx}`)
    } catch (error) {
      throw new Error(`签名交易失败: ${error.message}`)
    }
  }

  async sendTx () {
    try {
      const response = await post(`${this.config.rpcUrl}/sendBoc`, JSON.parse(this.rawTx))
      if (!response.ok) throw new Error(JSON.stringify(await response.text()))
      console.log('broadcast response:', response)
    } catch (error) {
      throw new Error(`发送交易失败: ${error.message}`)
    }
  }

  async checkTx () {
    try {
      console.log('正在检查交易状态...')
      const response = await fetch(`${this.config.apiUrl}/v2/blockchain/accounts/${this.config.from}/transactions?limit=1&sort_order=desc`)
      const txInfo = await response.json()
      const { success, in_msg: inMsg, hash } = txInfo.transactions?.[0] || {}
      if (success && inMsg.decoded_body.seqno === this.sequence) {
        console.log(`交易状态: success\nwebsiteUrl: ${this.config.website}/tx/${hash}`)
      } else {
        console.log('交易未完成，等待 15 秒后重新检查...')
        setTimeout(async () => {
          await this.checkTx()
        }, 15000)
      }
    } catch (error) {
      throw new Error(`校验上链信息失败: ${error.message}`)
    }
  }
}
