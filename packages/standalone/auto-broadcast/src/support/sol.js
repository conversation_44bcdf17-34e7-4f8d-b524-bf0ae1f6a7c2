import BigNumber from 'bignumber.js'
import { post } from '../lib/utils.js'

export default class Transaction {
  constructor (chain) {
    this.chain = chain
    this.config = {
      from: 'DRcye8qLyA86ZZkPNTfjzowCrxzDd1PhjXJ27ybgLQZP',
      privateKey: '162d6d2bae540c3c4b209ff5d92511f8172d470b62f72c4bb4659e74e2ec931bb89adc81d0844e3a22e0c6bfc0c87d00ae8b737d007bae9d872bf4fe101c633a',
      to: 'Asd97moUYcZEKDYaxoNoNZBkHfzNDTJTc5FcpCqsaCZr',
      amount: 100000,
      precision: 9,
      rpcUrl: 'http://qukuai-sol-1c-2.aws-jp1.huobiidc.com:8080',
      website: 'https://solscan.io'
    }
  }

  async checkAccount () {
    try {
      const { result: { value } } = await post(`${this.config.rpcUrl}`, {
        id: 1,
        jsonrpc: '2.0',
        method: 'getBalance',
        params: [this.config.from]
      })
      this.balance = new BigNumber(value)
      console.log(`地址：${this.config.from}, 余额: ${this.balance.dividedBy(10 ** this.config.precision).toString()}`)
    } catch (error) {
      throw new Error(`Error: ${error.message}`)
    }
  }

  async getTxData () {
    try {
      const { result: { value: { blockhash } } } = await post(`${this.config.rpcUrl}`, {
        id: 1,
        jsonrpc: '2.0',
        method: 'getLatestBlockhash'
      })
      this.blockHash = blockhash
    } catch (error) {
      throw new Error(`获取链信息失败: ${error.message}`)
    }
  }

  async signTx () {
    try {
      const { Transaction } = await import(`@tokens/${this.chain}`)
      const tx = await new Transaction().hbBuild({
        tx_type: 'Transfer',
        amount: this.config.amount,
        block_hash: this.blockHash,
        from: this.config.from,
        to: this.config.to
      })
      await tx.hbSign([this.config.privateKey])
      this.txHash = tx.hbGetTxHash()
      console.log(`txHash: ${this.txHash}`)
      this.rawTx = tx.hbSerialize()
      console.log(`rawTx: ${this.rawTx}`)
    } catch (error) {
      throw new Error(`签名交易失败: ${error.message}`)
    }
  }

  async sendTx () {
    try {
      const response = await post(`${this.config.rpcUrl}`, {
        id: 1,
        jsonrpc: '2.0',
        method: 'sendTransaction',
        params: [this.rawTx]
      })
      console.log('broadcast response:', response)
    } catch (error) {
      throw new Error(`发送交易失败: ${error.message}`)
    }
  }

  async checkTx () {
    try {
      const response = await post(`${this.config.rpcUrl}`, {
        id: 1,
        jsonrpc: '2.0',
        method: 'getTransaction',
        params: [this.txHash, 'json']
      })
      const { status, err } = response?.result?.meta ?? {}
      if (status) {
        console.log(`交易状态: ${err ? `${status}, ${err}` : 'success'}\nwebsiteUrl: ${this.config.website}/tx/${this.txHash}`)
      } else {
        console.log('交易未完成，等待 30 秒后重新检查...')
        setTimeout(async () => {
          await this.checkTx()
        }, 30000)
      }
    } catch (error) {
      throw new Error(`校验上链信息失败: ${error.message}`)
    }
  }
}
