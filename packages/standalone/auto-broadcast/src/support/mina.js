import BigNumber from 'bignumber.js'
import { post } from '../lib/utils.js'

const SUCCESS_STATUS = 'INCLUDED'
const PENDING_STATUS = 'PENDING'

export default class Transaction {
  constructor (chain) {
    this.chain = chain
    this.config = {
      from: 'B62qk7oEJ5Ry8zcFXuehBbpJTYnBQdiuzoDpwVSVVQNo12eLWY1Ktcu',
      to: 'B62qrpnvHDDba9Kn7g4aewwFGgSSK6xgkDjo4vQ6W5x82BZkfT5JLrm',
      amount: '200',
      /**
       * 手续费给的太低上链时间会很久, 例如手续费给0.001, 上链可能需要几分钟到几十分钟甚至更久
       * 如果发现很久未上链, 可以加高手续费, 重新转账
      */
      fee: '*********',
      to_tag: 'memo',
      privateKey: 'EKDrTk7rWVTCp1z3u1NXtrdqgxx5KyzqxBC6eHG16jhhDvEFAh1M',
      precision: 9,
      rpcUrl: 'http://qukuai-mina-1e-1.qcloud-jp-wallet.huobiidc.com:8080/graphql',
      website: 'https://minaexplorer.com'
    }
  }

  async checkAccount () {
    try {
      const { data: { account: { balance: { total } } } } = await post(
        this.config.rpcUrl,
        {
          query: `query { account(publicKey: "${this.config.from}") { balance { total } } }`
        }
      )
      this.balance = new BigNumber(total)
      console.log(`地址：${this.config.from}, 余额: ${this.balance.dividedBy(10 ** this.config.precision).toString()}`)
    } catch (error) {
      throw new Error(`Error: ${error.message}`)
    }
  }

  async getTxData () {
    try {
      const { data: { account: { nonce } } } = await post(
        this.config.rpcUrl,
        {
          query: `query { account(publicKey: "${this.config.from}") { nonce } }`
        }
      )
      this.config.nonce = nonce
    } catch (error) {
      throw new Error(`获取链信息失败: ${error.message}`)
    }
  }

  async signTx () {
    try {
      const { Transaction } = await import(`@tokens/${this.chain}`)
      const { from, to, amount, nonce, fee, to_tag: toTag } = this.config
      const tx = new Transaction().hbBuild({
        from,
        to,
        amount,
        nonce,
        fee,
        to_tag: toTag
      })
      tx.hbSign([this.config.privateKey])

      this.rawTx = tx.hbSerialize()
      console.log(`rawTx: ${this.rawTx}`)
    } catch (error) {
      throw new Error(`签名交易失败: ${error.message}`)
    }
  }

  async sendTx () {
    try {
      const txInfo = JSON.parse(this.rawTx)
      const response = await post(
        this.config.rpcUrl,
        {
          query: `mutation { sendPayment(
signature:{
  field: "${txInfo.signature.field}",
  scalar: "${txInfo.signature.scalar}"},
input: {
  from: "${txInfo.data.from}",
  to: "${txInfo.data.to}",
  fee: "${txInfo.data.fee}",
  amount: "${txInfo.data.amount}",
  nonce: "${txInfo.data.nonce}",
  memo: "${txInfo.data.memo}",
  validUntil: "${txInfo.data.validUntil}"
} ) { payment { id hash } } }`
        }
      )

      this.txHash = response.data.sendPayment.payment.hash
      this.txId = response.data.sendPayment.payment.id
      console.log(`txHash: ${this.txHash}`)
      console.log('broadcast response:', JSON.stringify(response))
    } catch (error) {
      throw new Error(`发送交易失败: ${error.message}`)
    }
  }

  async checkTx () {
    try {
      console.log('正在检查交易状态...')
      const response = await post(
        this.config.rpcUrl,
        {
          query: `query { transactionStatus(payment: "${this.txId}") }`
        }
      )
      console.log('response:', response)
      const transactionStatus = response.data.transactionStatus
      const transactionInfoUrl = `${this.config.website}/transaction/${this.txHash}`
      if (transactionStatus === SUCCESS_STATUS) {
        console.log(`上链状态: success\n具体交易情况请从浏览器上查看\nwebsiteUrl: ${transactionInfoUrl}`)
      } else if (transactionStatus === PENDING_STATUS) {
        console.log('交易未完成，等待 30 秒后重新检查...')
        setTimeout(async () => {
          await this.checkTx()
        }, 30000)
      } else {
        console.log(`交易状态: failed\nwebsiteUrl: ${this.config.website}/transaction/${this.txHash}`)
      }
    } catch (error) {
      throw new Error(`校验上链信息失败: ${error.message}`)
    }
  }
}
