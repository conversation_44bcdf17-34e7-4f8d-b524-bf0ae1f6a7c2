import { post } from '../lib/utils.js'

export default class Transaction {
  constructor (chain) {
    this.chain = chain
    this.config = {
      // 因为主网地址（即测试环境签名机地址）上没啥钱 加上每次测试还要充资源 比较麻烦 所以这里仅提供测试网demo 如对主网有兴趣可以替换成主网地址及对应网络进行测试
      from: 'gvvevjtzpetn',
      privateKey: '5HrEbBhWFh7ZiD4AgU2TK8yVH3YjVuA15PLCqzEJkWdH3rEv1wx',
      to: 'dib5tgl34rbo',
      amount: 0.1368,
      toTag: 'eos@1368',
      precision: 4,
      binAsset: 'EOS',
      contractAccount: 'eosio.token',
      txType: 'transfer',
      rpcUrl: 'https://jungle4.greymass.com',
      apiUrl: 'https://jungle4.cryptolions.io',
      website: 'https://jungle4.cryptolions.io'
    }
  }

  async checkAccount () {
    try {
      const response = await fetch(`${this.config.rpcUrl}/v1/chain/get_account`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ account_name: this.config.from })
      })
      if (!response.ok) throw new Error(JSON.stringify(await response.json()))
      const accountInfo = await response.json()
      this.balance = accountInfo.core_liquid_balance
      console.log(`地址：${this.config.from}, 余额: ${this.balance}`)
    } catch (error) {
      throw new Error(`Error: ${error.message}`)
    }
  }

  async getTxData () {
    try {
      const chainResponse = await fetch(`${this.config.rpcUrl}/v1/chain/get_info`)
      if (!chainResponse.ok) throw new Error(JSON.stringify(await chainResponse.text()))
      const chainInfo = await chainResponse.json()
      this.chain_id = chainInfo.chain_id
      this.refer_block_height = chainInfo.head_block_num
      console.log('chain_id:', this.chain_id)
      console.log('refer_block_height:', this.refer_block_height)
      const blockResponse = await fetch(`${this.config.rpcUrl}/v1/chain/get_block`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ block_num_or_id: this.refer_block_height })
      })
      if (!blockResponse.ok) throw new Error(JSON.stringify(await blockResponse.text()))
      const blockInfo = await blockResponse.json()
      this.ref_block_prefix = blockInfo.ref_block_prefix
      console.log('ref_block_prefix:', this.ref_block_prefix)
      this.expire_time = new Date(new Date().getTime() + 3600 * 1000).toISOString().split('.')[0]
      console.log('expire_time:', this.expire_time)
      const rawAbiResponse = await post(`${this.config.rpcUrl}/v1/chain/get_raw_abi`, { account_name: 'eosio.token' })
      this.raw_abi = rawAbiResponse.abi
    } catch (error) {
      throw new Error(`获取链信息失败: ${error.message}`)
    }
  }

  async signTx () {
    try {
      const { Transaction } = await import(`@tokens/${this.chain}`)
      const tx = new Transaction().hbBuild({
        bin_asset: this.config.binAsset,
        decimal: this.config.precision,
        from: this.config.from,
        to: this.config.to,
        to_tag: this.config.toTag,
        amount: String(this.config.amount),
        chain_id: this.chain_id,
        tx_type: this.config.txType,
        contract_account: this.config.contractAccount,
        ref_block_num: this.refer_block_height,
        ref_block_prefix: this.ref_block_prefix,
        expiration: this.expire_time,
        raw_abi: this.raw_abi
      })
      await tx.hbSign([this.config.privateKey])

      this.txHash = tx.hbGetTxHash()
      console.log(`txHash: ${this.txHash}`)
      this.rawTx = tx.hbSerialize()
      console.log(`rawTx: ${this.rawTx}`)
    } catch (error) {
      throw new Error(`签名交易失败: ${error.message}`)
    }
  }

  async sendTx () {
    try {
      const response = await post(`${this.config.rpcUrl}/v1/chain/push_transaction `, JSON.parse(this.rawTx))
      console.log(`txHash: ${this.txHash}`)
      console.log('broadcast response:', response)
    } catch (error) {
      throw new Error(`发送交易失败: ${error.message}`)
    }
  }

  async checkTx () {
    try {
      console.log('正在检查交易状态...')
      const response = await fetch(`${this.config.apiUrl}/v2/history/get_transaction?id=${this.txHash}`)
      if (!response.ok) throw new Error(JSON.stringify(await response.text()))
      const res = await response.json()
      const { executed, trx_id: txId } = res
      if (executed && txId && this.txHash === txId) {
        console.log(`交易状态: success\nwebsiteUrl: ${this.config.website}/v2/explore/transaction/${this.txHash}`)
      } else {
        console.log('交易未完成，等待 10 秒后重新检查...')
        setTimeout(async () => {
          await this.checkTx()
        }, 10000)
      }
    } catch (error) {
      throw new Error(`校验上链信息失败: ${error.message}`)
    }
  }
}
