export default class Transaction {
  constructor (chain) {
    this.chain = chain
    this.config = {
      from: '4c2a44a441df342b4938e615c22133063834d5913b6bcea5e4c66c26a5128a1d',
      privateKey: 'aca74c0f055884a1468ed199529b7fb8683bc3785b36fa32aa9f1f94dcfba03b3cce13514e8c94db435e6343a7b574a728a4ec14f9eb630c57404d4e88e03b8e',
      to: 'fda11375c1d9576749a17306a1320179a26741e5b61b8d63140e664a5ad4aa82',
      amount: 2000000,
      precision: 8,
      apiUrl: 'https://fullnode.mainnet.aptoslabs.com',
      website: 'https://aptoscan.com'
    }
  }

  async checkAccount () {
    try {
      const response = await fetch(`${this.config.apiUrl}/v1/accounts/${this.config.from}/balance/0x1::aptos_coin::AptosCoin`)
      if (!response.ok) throw new Error(JSON.stringify(await response.json()))
      this.balance = await response.json()
      console.log(`地址：${this.config.from}, 余额: ${this.balance}`)
    } catch (error) {
      throw new Error(`Error: ${error.message}`)
    }
  }

  async getTxData () {
    try {
      const chainResponse = await fetch(`${this.config.apiUrl}/v1/`)
      const nonceResponse = await fetch(`${this.config.apiUrl}/v1/accounts/${this.config.from}`)
      const feePriceResponse = await fetch(`${this.config.apiUrl}/v1/estimate_gas_price`)
      if (!chainResponse.ok) throw new Error(JSON.stringify(await chainResponse.text()))
      if (!nonceResponse.ok) throw new Error(JSON.stringify(await nonceResponse.text()))
      if (!feePriceResponse.ok) throw new Error(JSON.stringify(await feePriceResponse.text()))
      const chainInfo = await chainResponse.json()
      const nonceInfo = await nonceResponse.json()
      const feePriceInfo = await feePriceResponse.json()
      this.chain_id = chainInfo.chain_id
      this.nonce = nonceInfo.sequence_number
      this.fee_price = feePriceInfo.prioritized_gas_estimate // 选高优先的gas价格 可以快速上链确认交易
      console.log('chain_id:', this.chain_id)
      console.log('nonce:', this.nonce)
      console.log('fee_price:', this.fee_price)
      this.expire_time = new Date().getTime() + 600 * 1000 // 普通转账设置交易过期时间：5-10分钟 不要超过1 小时
      console.log('expire_time:', this.expire_time)
    } catch (error) {
      throw new Error(`获取链信息失败: ${error.message}`)
    }
  }

  async signTx () {
    try {
      const { Transaction } = await import(`@tokens/${this.chain}`)
      const tx = new Transaction().hbBuild({
        asset: 'apt',
        chain_id: this.chain_id,
        from: this.config.from,
        to: this.config.to,
        amount: String(this.config.amount),
        fee_price: this.fee_price,
        fee_step: 1000, // 标准 APT 转账通常消耗约 1000 gas units
        nonce: this.nonce,
        expire_time: this.expire_time,
        tx_type: 'transfer'
      }).hbSign([this.config.privateKey])

      this.txHash = `0x${tx.hbGetTxHash()}`
      console.log(`txHash: ${this.txHash}`)
      this.rawTx = tx.hbSerialize()
      console.log(`rawTx: ${this.rawTx}`)
    } catch (error) {
      throw new Error(`签名交易失败: ${error.message}`)
    }
  }

  async sendTx () {
    try {
      const txnBytes = new Uint8Array(
        this.rawTx.match(/.{1,2}/g).map(byte => parseInt(byte, 16))
      )
      const response = await fetch(`${this.config.apiUrl}/v1/transactions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x.aptos.signed_transaction+bcs'
        },
        body: txnBytes
      })
      if (!response.ok) throw new Error(JSON.stringify(await response.text()))
      console.log(`txHash: ${this.txHash}`)
      console.log('broadcast response:', await response.text())
    } catch (error) {
      throw new Error(`发送交易失败: ${error.message}`)
    }
  }

  async checkTx () {
    try {
      console.log('正在检查交易状态...')
      const response = await fetch(`${this.config.apiUrl}/v1/transactions/by_hash/${this.txHash}`)
      const { version, hash, vm_status: vmStatus } = await response.json()
      if (version && hash && hash === this.txHash && vmStatus === 'Executed successfully') {
        console.log(`交易状态: success\nwebsiteUrl: ${this.config.website}/transaction/${version}`)
      } else {
        console.log('交易未完成，等待 10 秒后重新检查...')
        setTimeout(async () => {
          await this.checkTx()
        }, 10000)
      }
    } catch (error) {
      throw new Error(`校验上链信息失败: ${error.message}`)
    }
  }
}
