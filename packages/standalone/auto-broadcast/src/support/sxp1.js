export default class Transaction {
  constructor (chain) {
    this.chain = chain
    this.config = {
      from: 'SNikbTEzxBSnoJ2kTDH1g5iNEqGMqcg9op',
      privateKey: '75d6c56c1a1466f898f9d96f6b60f83d0893f1d7aac0ecd231504db0e3af1990',
      to: 'SeDJNXiiCzU9z4nSvbFHrekN8nS6twKPec',
      amount: 2500000,
      precision: 8,
      rpcUrl: 'http://qukuai-sxp-1a-1.aws-jp1.huobiidc.com:8080',
      website: 'https://solarscan.com'
    }
  }

  async checkAccount () {
    try {
      const response = await fetch(`${this.config.rpcUrl}/api/wallets/${this.config.from}`)
      if (!response.ok) throw new Error(JSON.stringify(await response.json()))
      const accountInfo = await response.json()
      this.nonce = +accountInfo.data.nonce + 1
      console.log(`nonce: ${this.nonce}`)
      this.balance = accountInfo.data.balance
      console.log(`地址：${this.config.from}, 余额: ${this.balance}`)
    } catch (error) {
      throw new Error(`Error: ${error.message}`)
    }
  }

  async getTxData () {
    try {
      const feeResponse = await fetch(`${this.config.rpcUrl}/api/node/fees?days=1`)
      if (!feeResponse.ok) throw new Error(JSON.stringify(await feeResponse.text()))
      const feeInfo = await feeResponse.json()
      this.fee = feeInfo.data['1'].transfer.avg
      console.log('avg fee of transfer:', this.fee)
    } catch (error) {
      throw new Error(`获取链信息失败: ${error.message}`)
    }
  }

  async signTx () {
    try {
      const { Transaction } = await import(`@tokens/${this.chain}`)
      const tx = new Transaction().hbBuild({
        from: this.config.from,
        to: this.config.to,
        amount: String(this.config.amount),
        to_tag: 'Hello World',
        fee: this.fee,
        nonce: this.nonce
      })
      tx.hbSign([this.config.privateKey])

      this.txHash = tx.hbGetTxHash()
      console.log(`txHash: ${this.txHash}`)
      this.rawTx = tx.hbSerialize()
      console.log(`rawTx: ${this.rawTx}`)
    } catch (error) {
      throw new Error(`签名交易失败: ${error.message}`)
    }
  }

  async sendTx () {
    try {
      const response = await fetch(`${this.config.rpcUrl}/api/transactions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: `${this.rawTx}`
      })
      if (!response.ok) throw new Error(JSON.stringify(await response.text()))
      console.log(`txHash: ${this.txHash}`)
      console.log('broadcast response:', await response.text())
    } catch (error) {
      throw new Error(`发送交易失败: ${error.message}`)
    }
  }

  async checkTx () {
    try {
      console.log('正在检查交易状态...')
      const response = await fetch(`${this.config.rpcUrl}/api/transactions/${this.txHash}`)
      const res = await response.text()
      const { data } = JSON.parse(res)
      if (data && this.txHash === data.id) {
        console.log(`交易状态: success\nwebsiteUrl: ${this.config.website}/transaction/${this.txHash}`)
      } else {
        console.log('交易未完成，等待 10 秒后重新检查...')
        setTimeout(async () => {
          await this.checkTx()
        }, 10000)
      }
    } catch (error) {
      throw new Error(`校验上链信息失败: ${error.message}`)
    }
  }
}
