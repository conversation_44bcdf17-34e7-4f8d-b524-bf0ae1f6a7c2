export default class Transaction {
  constructor (chain) {
    this.chain = chain
    this.config = {
      // xrp必须先激活才能进行转账交易 给待激活地址转帐10xrp即可激活
      // 接收地址如果不存在则会报错: tecNO_DST_INSUF_XRP & Destination does not exist. Too little XRP sent to create it.
      // 鉴于以上原因 & xrp比较贵 我们直接找两个已经激活的有钱的地址进行转账交易即可
      from: 'rNqA2TbdeUP22DvqyjRAjmokibYaihW62t',
      privateKey: 'shJVcn45NkBgfgMXa344PNCcDyCnA',
      to: 'r3wb89BUT4DZ4KAckup2K2itXCqMhZcDPJ',
      amount: '0.050000',
      precision: 6,
      rpcUrl: 'https://s2.ripple.com:51234',
      website: 'https://xrpscan.com',
      toTag: 9527 // 交易标签 非必填 但是有些目标地址设置过 Destination tag: REQUIRED 则必须填写 是否设置得看目标地址的要求 & ⚠️注意: 类型为number
    }
  }

  async checkAccount () {
    try {
      const response = await fetch(`${this.config.rpcUrl}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ method: 'account_info', params: [{ account: this.config.from, ledger_index: 'current', queue: true }] })
      })
      if (!response.ok) throw new Error(JSON.stringify(await response.json()))
      const accountInfo = await response.json()
      this.balance = accountInfo.result.account_data.Balance
      console.log(`地址：${this.config.from}, 余额: ${this.balance}`)
      this.sequence = accountInfo.result.account_data.Sequence
      console.log(`sequence: ${this.sequence}`)
    } catch (error) {
      throw new Error(`Error: ${error.message}`)
    }
  }

  async getTxData () {
    try {
      const response = await fetch(`${this.config.rpcUrl}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ method: 'fee', params: [{}] })
      })
      if (!response.ok) throw new Error(JSON.stringify(await response.text()))
      const feeInfo = await response.json()
      this.fee = feeInfo.result.drops.minimum_fee
      console.log('fee:', this.fee)
    } catch (error) {
      throw new Error(`获取链信息失败: ${error.message}`)
    }
  }

  async signTx () {
    try {
      const { Transaction } = await import(`@tokens/${this.chain}`)
      const tx = await new Transaction().hbBuildTx({
        from: this.config.from,
        payment: {
          source: {
            address: this.config.from,
            maxAmount: {
              value: this.config.amount,
              currency: 'XRP'
            }
          },
          destination: {
            address: this.config.to,
            amount: {
              value: this.config.amount,
              currency: 'XRP'
            },
            tag: this.config.toTag || 0 // 如果给明确要求带tag的地址转账，则必须设置tag 如果不设置报错 tecDST_TAG_NEEDED & A destination tag is required.
          }
        },
        instructions: {
          maxLedgerVersion: null,
          sequence: this.sequence,
          fee: String(this.fee / 10 ** this.config.precision)
        }
      })
      await tx.hbSign([this.config.privateKey])

      this.txHash = tx.hbGetTxHash()
      console.log(`txHash: ${this.txHash}`)
      this.rawTx = tx.hbSerialize()
      console.log(`rawTx: ${this.rawTx}`)
    } catch (error) {
      throw new Error(`签名交易失败: ${error.message}`)
    }
  }

  async sendTx () {
    try {
      const response = await fetch(`${this.config.rpcUrl}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ method: 'submit', params: [{ tx_blob: this.rawTx }] })
      })
      if (!response.ok) throw new Error(JSON.stringify(await response.text()))
      console.log(`txHash: ${this.txHash}`)
      console.log('broadcast response:', await response.text())
    } catch (error) {
      throw new Error(`发送交易失败: ${error.message}`)
    }
  }

  async checkTx () {
    try {
      console.log('正在检查交易状态...')
      const response = await fetch(`${this.config.rpcUrl}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ method: 'tx', params: [{ transaction: this.txHash, binary: false }] })
      })
      const res = await response.json()
      const { result: { status } } = res
      if (status === 'success') {
        console.log(`交易状态: success\nwebsiteUrl: ${this.config.website}/tx/${this.txHash}`)
      } else {
        console.log('交易未完成，等待 15 秒后重新检查...')
        setTimeout(async () => {
          await this.checkTx()
        }, 15000)
      }
    } catch (error) {
      throw new Error(`校验上链信息失败: ${error.message}`)
    }
  }
}
