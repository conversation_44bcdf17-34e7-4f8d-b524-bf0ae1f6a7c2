export default class Transaction {
  constructor (chain) {
    this.chain = chain
    this.config = {
      // xtz地址需要激活才能转账，激活步骤：1、先给待激活地址充钱 2、发送地址激活的交易
      from: 'tz1Z7VDmuojG4yyyQZmmLhtBkSRxR2TNEADw',
      privateKey: 'edskRk3fqtCbaiNFtTrGupxCYHJvJs1y2cmv2vmFfjap3wNMce6C3fGF9XLcsTMom63nfQ17sMRYYoSuVu1peruk68KkdiWtkg',
      to: 'tz1g6KPqzMiUdrtUTkWv6voCubxLq6rjr9BZ',
      // 对一个新地址进行激活（Reveal 操作）需要考虑以下费用：
      // 0.6425 XTZ（642,500 mutez）作为账户基础准备金
      // Reveal 操作本身大约需要 0.001275 XTZ（1,275 mutez）
      // 推荐最低转账金额 = 0.65 XTZ（650,000 mutez）
      amount: '50000', // 如果只是用于转账测试 金额可随意设置 Reveal时需保证待激活地址余额大于等于上述提及账户基础准备金
      precision: 6,
      // Up to 10 calls per sec
      // Up to 500k calls per day
      // If you exceed the limit, the API will respond with HTTP 429 status code.
      apiUrl: 'https://api.tzkt.io', // API URL for Tezos Mainnet: https://api.tzkt.io/ or https://api.mainnet.tzkt.io/
      // API URL & RPC URL 优势互补 可以方便取数据
      rpcUrl: 'http://qukuai-xtz-1e-1.qcloud-jp-wallet.huobiidc.com:8080',
      website: 'https://tzkt.io',
      isReveal: false, // 根据实际情况设置 true/false 记得设置好待激活地址及其keys
      revealAddr: 'tz1g6KPqzMiUdrtUTkWv6voCubxLq6rjr9BZ',
      revealPubKey: 'edpkti5dgJ4Fhpj9927UHrQRXT2KvvDcgAMYy3rUeRsD7XxxPLYM67',
      revealPrivKey: 'edskRdnThw5jMCUXtHzHd3uUM85MJ7Thw29GLsum1A69dTwgos9ooPCxW29rSbuoVXZgdoGo8xU32pCJbyGnHck7GCkH8AMq3z'
    }
  }

  async checkAccount () {
    try {
      const response = await fetch(`${this.config.apiUrl}/v1/accounts/${this.config.isReveal ? this.config.revealAddr : this.config.from}/balance`)
      if (!response.ok) throw new Error(JSON.stringify(await response.text()))
      this.balance = await response.json()
      console.log(`地址：${this.config.isReveal ? this.config.revealAddr : this.config.from}, 余额: ${this.balance}`)
    } catch (error) {
      throw new Error(`Error: ${error.message}`)
    }
  }

  async getTxData () {
    try {
      const headResponse = await fetch(`${this.config.apiUrl}/v1/head`)
      const counterResponse = await fetch(`${this.config.apiUrl}/v1/accounts/${this.config.isReveal ? this.config.revealAddr : this.config.from}/counter`)
      if (!headResponse.ok) throw new Error(JSON.stringify(await headResponse.text()))
      if (!counterResponse.ok) throw new Error(JSON.stringify(await counterResponse.text()))
      const headInfo = await headResponse.json()
      const counter = await counterResponse.json()
      this.head = headInfo.hash
      this.counter = counter
      console.log('head:', this.head)
      console.log('counter:', this.counter)
      const optObj = {
        branch: this.head,
        contents: [
          {
            kind: 'transaction',
            source: this.config.from,
            fee: '20000',
            counter: String(this.counter + 1), // Counter值+1
            gas_limit: '12000',
            storage_limit: '300'
          }
        ]
      }
      if (this.config.isReveal && this.config.revealAddr && this.config.revealPubKey) {
        optObj.contents[0].kind = 'reveal'
        optObj.contents[0].source = this.config.revealAddr
        optObj.contents[0].public_key = this.config.revealPubKey
      } else {
        optObj.contents[0].amount = this.config.amount
        optObj.contents[0].destination = this.config.to
      }
      const operationDataResponse = await fetch(`${this.config.rpcUrl}/chains/main/blocks/head/helpers/forge/operations`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(optObj)
      })
      this.operation_data = await operationDataResponse.json()
      console.log('operation_data:', this.operation_data)
    } catch (error) {
      throw new Error(`获取链信息失败: ${error.message}`)
    }
  }

  async signTx () {
    try {
      const { Transaction } = await import(`@tokens/${this.chain}`)
      const tx = new Transaction().hbSign(this.operation_data, [this.config.isReveal ? this.config.revealPrivKey : this.config.privateKey])
      this.txHash = tx.hbGetTxHash()
      console.log(`txHash: ${this.txHash}`)
      this.rawTx = tx.hbSerialize()
      console.log(`rawTx: ${this.rawTx}`)
    } catch (error) {
      throw new Error(`签名交易失败: ${error.message}`)
    }
  }

  async sendTx () {
    try {
      const response = await fetch(`${this.config.rpcUrl}/injection/operation`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(this.rawTx)
      })
      if (!response.ok) throw new Error(JSON.stringify(await response.text()))
      console.log(`txHash: ${this.txHash}`)
      console.log('broadcast response:', await response.text())
    } catch (error) {
      throw new Error(`发送交易失败: ${error.message}`)
    }
  }

  async checkTx () {
    try {
      console.log('正在检查交易状态...')
      // path: /v1/operations/{type}/{hash}  type e.g: transactions or reveals
      const type = this.config.isReveal ? 'reveals' : 'transactions'
      const response = await fetch(`${this.config.apiUrl}/v1/operations/${type}/${this.txHash}`)
      const res = await response.json()
      const [operation] = res
      if (operation && operation.status === 'applied') {
        console.log(`交易状态: success\nwebsiteUrl: ${this.config.website}/${operation.hash}/${operation.counter}`)
      } else {
        console.log('交易未完成，等待 15 秒后重新检查...')
        setTimeout(async () => {
          await this.checkTx()
        }, 15000)
      }
    } catch (error) {
      throw new Error(`校验上链信息失败: ${error.message}`)
    }
  }
}
