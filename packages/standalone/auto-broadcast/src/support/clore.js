import _ from 'lodash'
import BigNumber from 'bignumber.js'
import { post } from '../lib/utils.js'
import { getChangeOut } from '../lib/utxo-utils.js'
const { keyBy } = _

export default class Transaction {
  constructor (chain) {
    this.chain = chain
    this.config = {
      vin: [
        {
          address: 'AHdi4REXxe9Exmm6Yf48Bi2gUk7iRkdrev',
          privateKey: 'HfuNPSBP9EyXyF7rtvZDm2nupc8jMFhX2gsPEYgLNpKd6Nt4eNFz'
        }
      ],
      vout: [
        {
          address: 'ALjQBFmZ3nwga31JSEQdERkZgCf3cueXac',
          amount: '********'
        }
      ],
      precision: 8,
      rpcUrl: 'http://qukuai-clore-1e-1.qcloud-jp-wallet.huobiidc.com:8080',
      username: 'cloreuser',
      password: '7X5&k3cD7k934',
      apiUrl: 'https://blockbook.clore.ai',
      website: 'https://clore.cryptoscope.io'
    }
  }

  async checkAccount () {
    try {
      let balance = new BigNumber('0')
      for (const { address } of this.config.vin) {
        const response = await fetch(`${this.config.apiUrl}/api/v2/address/${address}`)
        if (!response.ok) throw new Error(JSON.stringify(await response.json()))
        const Account = await response.json()
        balance = balance.plus(new BigNumber(Account.balance))
        console.log(`地址：${address}, 余额: ${balance.dividedBy(10 ** this.config.precision).toString()}`)
      }
      console.log(`各vin地址总余额: ${balance.dividedBy(10 ** this.config.precision).toString()}`)
    } catch (error) {
      throw new Error(`Error: ${error.message}`)
    }
  }

  async getTxData () {
    try {
      const feeResponse = await fetch(`${this.config.apiUrl}/api/v2/estimatefee/1`)
      if (!feeResponse.ok) throw new Error(JSON.stringify(await feeResponse.json()))
      let { result: fee } = await feeResponse.json()
      console.log(`fee: ${fee}`)
      fee = fee * (10 ** this.config.precision)

      const utxoList = []
      for (const { address } of this.config.vin) {
        const utxoResponse = await fetch(`${this.config.apiUrl}/api/v2/utxo/${address}`)
        if (!utxoResponse.ok) throw new Error(JSON.stringify(await utxoResponse.json()))
        const utxos = await utxoResponse.json()
        utxoList.push(...utxos.map(item => ({ ...item, address })))
      }
      const privateKeyMap = keyBy(this.config.vin, 'address')
      this.privateKeys = []
      this.config.vin = utxoList.map(utxo => {
        this.privateKeys.push(privateKeyMap[utxo.address].privateKey)
        return {
          hash: utxo.txid,
          address: utxo.address,
          amount: Number(utxo.value).toString(10),
          vout_index: utxo.vout
        }
      })
      const changeOut = getChangeOut({ vin: this.config.vin, vout: this.config.vout, fee })
      if (changeOut) this.config.vout.push(changeOut)
      console.log(`vin: ${JSON.stringify(this.config.vin)}`)
      console.log(`vout: ${JSON.stringify(this.config.vout)}`)
    } catch (error) {
      throw new Error(`获取链信息失败: ${error.message}`)
    }
  }

  async signTx () {
    try {
      const { Transaction } = await import(`@tokens/${this.chain}`)
      const tx = new Transaction().hbBuild({
        vin: this.config.vin,
        vout: this.config.vout
      }).hbSign(this.privateKeys)
      this.txHash = tx.hbGetTxHash()
      console.log(`txHash: ${this.txHash}`)
      this.rawTx = tx.hbSerialize()
      console.log(`rawTx: ${this.rawTx}`)
    } catch (error) {
      throw new Error(`签名交易失败: ${error.message}`)
    }
  }

  async sendTx () {
    try {
      const response = await post(`${this.config.rpcUrl}`, {
        method: 'sendrawtransaction',
        params: [this.rawTx]
      }, { Authorization: `Basic ${btoa(this.config.username + ':' + this.config.password)}` })
      console.log('broadcast response:', response)
    } catch (error) {
      throw new Error(`发送交易失败: ${error.message}`)
    }
  }

  async checkTx (index = 0) {
    try {
      console.log('正在检查交易状态...')
      const response = await fetch(`${this.config.apiUrl}/api/v2/tx/${this.txHash}`)
      if (!response.ok) throw new Error(JSON.stringify(await response.json()))
      const { blockHash } = await response.json() || {}
      if (blockHash) {
        console.log(`交易状态: success\nwebsiteUrl: ${this.config.website}/tx?txid=${this.txHash}`)
      } else {
        console.log('交易未完成，等待 30 秒后重新检查...')
        setTimeout(async () => {
          await this.checkTx()
        }, 30000)
      }
    } catch (error) {
      // 上链较慢，所以前几次查询交易详情可能报错，增加重试次数
      if (index > 4) {
        throw new Error(`校验上链信息失败: ${error.message}`)
      } else {
        setTimeout(async () => {
          await this.checkTx(index + 1)
        }, 30000)
      }
    }
  }
}
