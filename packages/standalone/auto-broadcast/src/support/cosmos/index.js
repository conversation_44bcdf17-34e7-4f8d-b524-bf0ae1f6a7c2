export default class Transaction {
  constructor (chain, config) {
    this.chain = chain
    this.config = config
  }

  async checkAccount () {
    try {
      const response = await fetch(`${this.config.apiUrl}/cosmos/bank/v1beta1/balances/${this.config.from}`, {
        method: 'get',
        headers: {
          'Content-Type': 'application/json'
        }
      })
      if (!response.ok) throw new Error(JSON.stringify(await response.json()))
      const { balances } = await response.json()
      for (const item of balances) {
        console.log(`地址：${this.config.from}, token:${item.denom}余额: ${item.amount}`)
      }
    } catch (error) {
      throw new Error(`Error: ${error.message}`)
    }
  }

  async getTxData () {
    try {
      const response = await fetch(`${this.config.apiUrl}/cosmos/auth/v1beta1/accounts/${this.config.from}`)
      if (!response.ok) throw new Error(JSON.stringify(await response.json()))
      const { account: { account_number: accountNumber, sequence } } = await response.json()
      this.config.nonce = sequence
      this.config.account_number = accountNumber
      console.log(this.config)
    } catch (error) {
      throw new Error(`获取链信息失败: ${error.message}`)
    }
  }

  async signTx () {
    try {
      const { Transaction } = await import(`@tokens/${this.chain}`)
      const tx = await new Transaction().hbBuild(this.config).hbSign([this.config.privateKey])
      this.rawTx = tx.hbSerialize()
      console.log(`rawTx: ${this.rawTx}`)
    } catch (error) {
      throw new Error(`签名交易失败: ${error.message}`)
    }
  }

  async sendTx () {
    try {
      const response = await fetch(`${this.config.apiUrl}/cosmos/tx/v1beta1/txs`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          tx_bytes: this.rawTx,
          mode: 'BROADCAST_MODE_SYNC'
        })
      })
      const data = await response.json()
      if (!response.ok) throw new Error(JSON.stringify(data))
      const { tx_response: { txhash, code, raw_log: rawLog } } = data
      if (code !== 0) {
        throw Error(`code:${code},message:${rawLog}`)
      }
      this.txhash = txhash
      console.log(`txHash: ${txhash}`)
    } catch (error) {
      throw new Error(`发送交易失败: ${error.message}`)
    }
  }

  async checkTx () {
    try {
      const response = await fetch(`${this.config.apiUrl}/cosmos/tx/v1beta1/txs/${this.txhash}`)
      const data = await response.json()
      if (data.tx && data.tx_response) {
        console.log(`交易状态: success\nwebsiteUrl: ${this.config.website}/${this.txhash}`)
        return
      }
      if (data.code) {
        console.log(`交易未完成，code:${data.code},message:${data.message}等待 10 秒后重新检查...`)
        setTimeout(async () => {
          await this.checkTx()
        }, 10000)
      }
    } catch (error) {
      throw new Error(`校验上链信息失败: ${error.message}`)
    }
  }
}
