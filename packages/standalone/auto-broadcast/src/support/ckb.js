import _ from 'lodash'
import BigNumber from 'bignumber.js'
import { getChangeOut } from '../lib/utxo-utils.js'
import { post } from '../lib/utils.js'
const { keyBy } = _

export default class Transaction {
  constructor (chain) {
    this.chain = chain
    this.config = {
      vin: [
        {
          address: 'ckb1qyqwzrn7tzkchv504r8lnna23nk32dwkhxpsh7ulv6',
          privateKey: '0xe4fe38d9f4325a070b72ff9f1a5e7cf905920cba37d56a9440c85f74c1b2dfa6'
        }
      ],
      vout: [
        {
          address: 'ckb1qyqxgqly06w4p65thgxlv9er50ldtz48z9uque607s',
          amount: '6150000000'
        }
      ],
      precision: 8,
      rpcUrl: 'https://mainnet.ckb.dev',
      website: 'https://explorer.nervos.org',
      hashType: 'type',
      firstBlockSecondTxHash: '0x71a7ba8fc96349fea0ed3a5c47992e3b4084b031a42264a018e0072e8172e46c',
      constCodeHash: {
        multipleCodeHash: '0x5c5069eb0857efc65e1bca0c07df34c31663b3622fd3876c876320fc9634e2a8',
        singleCodeHash: '0x9bd7e06f3ecf4be0f2fcd2188b23f1b9fcc88e5d4b65a8637b17723bbda3cce8'
      },
      minAmt: ********** // 币种特殊性: 任意一笔vout的金额都要大于等于这个值
    }
  }

  async checkAccount () {
    try {
      const { addrToPubHash } = await import(`@tokens/${this.chain}`)
      this.balance = new BigNumber(0)
      for (const { address } of this.config.vin) {
        const response = await post(`${this.config.rpcUrl}`, {
          id: 1,
          jsonrpc: '2.0',
          method: 'get_cells_capacity',
          params: [
            {
              script: {
                code_hash: this.config.constCodeHash.singleCodeHash,
                hash_type: this.config.hashType,
                args: addrToPubHash(address)
              },
              script_type: 'lock'
            }
          ]
        })
        const capacity = response.result.capacity
        console.log('capacity', capacity)
        this.balance = this.balance.plus(new BigNumber(capacity))
        console.log(`地址：${address}, 余额: ${new BigNumber(capacity).toString()}`)
      }
      console.log(`各vin地址总余额: ${this.balance.toString()}`)
      const totalVoutAmount = this.config.vout.reduce((total, item) => {
        return total.plus(item.amount)
      }, new BigNumber(0))
      console.log(`各vout地址总花费金额: ${totalVoutAmount.toString()}`)
      if (this.balance.isLessThanOrEqualTo(totalVoutAmount)) {
        throw new Error('各vin地址总余额不足')
      }
    } catch (error) {
      throw new Error(`Error: ${error.message}`)
    }
  }

  async getTxData () {
    try {
      const { addrToPubHash, pubHashToAddr } = await import(`@tokens/${this.chain}`)
      const feeResponse = await post(`${this.config.rpcUrl}`, {
        id: 1,
        jsonrpc: '2.0',
        method: 'get_fee_rate_statistics',
        params: []
      })
      const mean = feeResponse.result.median
      this.fee = Number(mean).toString(10)
      console.log(`fee: ${this.fee}`)

      const cellsList = []
      for (const { address } of this.config.vin) {
        const cellsResponse = await post(`${this.config.rpcUrl}`, {
          id: 1,
          jsonrpc: '2.0',
          method: 'get_cells',
          params: [
            {
              script: {
                code_hash: this.config.constCodeHash.singleCodeHash,
                hash_type: this.config.hashType,
                args: addrToPubHash(address)
              },
              script_type: 'lock'
            },
            'asc',
            '0x64'
          ]
        })
        const cells = cellsResponse.result.objects
        cellsList.push(...cells)
      }
      const privateKeyMap = keyBy(this.config.vin, 'address')
      this.privateKeys = []
      this.config.vin = cellsList.map(cell => {
        const address = pubHashToAddr(cell.output.lock.args, cell.output.lock.code_hash, this.config.constCodeHash)
        this.privateKeys.push(privateKeyMap[address].privateKey)
        return {
          hash: cell.out_point.tx_hash,
          address,
          amount: Number(cell.output.capacity).toString(10),
          vout_index: cell.out_point.index,
          asset: this.chain
        }
      })

      // 检查输出是否满足最小金额要求
      this.config.vout.forEach((vout) => {
        if (new BigNumber(vout.amount).minus(this.config.minAmt).isLessThan(0)) {
          throw new Error(`任意一笔vout的金额都要>=${this.config.minAmt} error vout: ${JSON.stringify(vout)}`)
        }
      })
      const changeOut = getChangeOut({ vin: this.config.vin, vout: this.config.vout, fee: this.fee })
      if (changeOut) {
        if (new BigNumber(changeOut.amount).minus(this.config.minAmt).isLessThan(0)) {
          // 由于币种特殊性 如果changeOut不满足最小金额要求 直接将changeOut的金额加到vout[0]中
          this.config.vout[0].amount = new BigNumber(this.config.vout[0].amount).plus(changeOut.amount).toString()
        } else {
          this.config.vout.push(changeOut)
        }
      }
      console.log(`vin: ${JSON.stringify(this.config.vin)}`)
      console.log(`vout: ${JSON.stringify(this.config.vout)}`)
    } catch (error) {
      throw new Error(`获取链信息失败: ${error.message}`)
    }
  }

  async signTx () {
    try {
      const { Transaction } = await import(`@tokens/${this.chain}`)
      const tx = new Transaction().hbBuild({
        vin: this.config.vin,
        vout: this.config.vout,
        fee: this.fee,
        multiple_code_hash: this.config.constCodeHash.multipleCodeHash,
        single_code_hash: this.config.constCodeHash.singleCodeHash,
        first_block_second_tx_hash: this.config.firstBlockSecondTxHash
      }).hbSign(this.privateKeys)

      this.txHash = tx.hbGetTxHash()
      console.log(`txHash: ${this.txHash}`)
      this.rawTx = tx.hbSerialize()
      console.log(`rawTx: ${this.rawTx}`)
    } catch (error) {
      throw new Error(`签名交易失败: ${error.message}`)
    }
  }

  async sendTx () {
    try {
      const response = await post(`${this.config.rpcUrl}`, {
        id: 1,
        jsonrpc: '2.0',
        method: 'send_transaction',
        params: [
          JSON.parse(this.rawTx)
        ]
      })
      console.log(`txHash: ${this.txHash}`)
      console.log('broadcast response:', await response)
    } catch (error) {
      throw new Error(`发送交易失败: ${error.message}`)
    }
  }

  async checkTx () {
    try {
      console.log('正在检查交易状态...')
      const response = await post(`${this.config.rpcUrl}`, {
        id: '1',
        jsonrpc: '2.0',
        method: 'get_transaction',
        params: [this.txHash]
      })
      const { result: { transaction, tx_status: txStatus } } = response
      if (transaction && transaction.hash === this.txHash && txStatus.status === 'committed') {
        console.log(`交易状态: success\nwebsiteUrl: ${this.config.website}/transaction/${this.txHash}`)
      } else {
        console.log('交易未完成，等待 30 秒后重新检查...')
        setTimeout(async () => {
          await this.checkTx()
        }, 30000) // 有时候上链比较慢
      }
    } catch (error) {
      throw new Error(`校验上链信息失败: ${error.message}`)
    }
  }
}
