export default class Transaction {
  constructor (chain) {
    this.chain = chain
    this.config = {
      from: 'cd3563dbdd0638b5',
      privateKey: 'a5912869529cfa40e07a4fe2d34c7dd5e803e010761b3ab152cf3654976bd203',
      to: '9777aac3733325f4',
      amount: '0.01', // e.g: amount为"1.23"代表1.23个FLOW & 采用UFix64格式
      precision: 8,
      apiUrl: 'https://rest-mainnet.onflow.org',
      website: 'https://www.flowscan.io',
      network: 'mainnet'
    }
  }

  async checkAccount () {
    try {
      const response = await fetch(`${this.config.apiUrl}/v1/accounts/${this.config.from}`)
      if (!response.ok) throw new Error(JSON.stringify(await response.json()))
      this.balance = (await response.json()).balance
      console.log(`地址：${this.config.from}, 余额: ${this.balance}`)
    } catch (error) {
      throw new Error(`Error: ${error.message}`)
    }
  }

  async getTxData () {
    try {
      const nonceResponse = await fetch(`${this.config.apiUrl}/v1/accounts/${this.config.from}/keys/0`)
      const blocksResponse = await fetch(`${this.config.apiUrl}/v1/blocks?height=sealed`)
      if (!nonceResponse.ok) throw new Error(JSON.stringify(await nonceResponse.text()))
      if (!blocksResponse.ok) throw new Error(JSON.stringify(await blocksResponse.text()))
      this.nonce = (await nonceResponse.json()).sequence_number
      this.lastBlockId = (await blocksResponse.json())[0].header.id
      console.log('nonce:', this.nonce)
      console.log('typeof nonce:', typeof this.nonce)
      console.log('lastBlockId:', this.lastBlockId)
    } catch (error) {
      throw new Error(`获取链信息失败: ${error.message}`)
    }
  }

  async signTx () {
    try {
      const { Transaction, FlowTool } = await import(`@tokens/${this.chain}`)
      FlowTool.initSdkConfig(this.config.apiUrl, this.config.network)
      this.FlowTool = FlowTool
      const tx = await new Transaction().hbBuildTx({
        asset: 'flow',
        from: this.config.from,
        to: this.config.to,
        amount: String(this.config.amount),
        fee_step: 100,
        keyIndex: 0,
        nonce: Number(this.nonce),
        tx_type: 'transfer'
      }, [this.config.privateKey])
      await tx.hbSign()

      this.rawTx = tx.hbSerialize()
      console.log(`rawTx: ${this.rawTx}`)
      this.resolvedTxIx = tx.resolvedTxIx
    } catch (error) {
      throw new Error(`签名交易失败: ${error.message}`)
    }
  }

  async sendTx () {
    try {
      const broadcastRes = await this.FlowTool.sendResolvedTx(this.resolvedTxIx, this.config.apiUrl)
      console.log('broadcast response:', broadcastRes)
      this.txHash = broadcastRes.transactionId
      console.log(`txHash: ${this.txHash}`)
    } catch (error) {
      throw new Error(`发送交易失败: ${error.message}`)
    }
  }

  async checkTx () {
    try {
      console.log('正在检查交易状态...')
      const response = await fetch(`${this.config.apiUrl}/v1/transactions/${this.txHash}`)
      const { id } = await response.json()
      if (id && id === this.txHash) {
        console.log(`交易状态: success\nwebsiteUrl: ${this.config.website}/tx/${this.txHash}`)
      } else {
        console.log('交易未完成，等待 10 秒后重新检查...')
        setTimeout(async () => {
          await this.checkTx()
        }, 10000)
      }
    } catch (error) {
      throw new Error(`校验上链信息失败: ${error.message}`)
    }
  }
}
