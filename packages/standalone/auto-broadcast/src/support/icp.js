import { addressToHex, bufferFromArrayBufferView } from '@tokens/icp'

export default class Transaction {
  constructor (chain) {
    this.chain = chain
    this.config = {
      from: '2c2229cc2f3665a703ceca3fb32d013aa4aa2469d18b2a882c14ef616d4174cc',
      privateKey: '23c01ada4fc4dfb894ef004410629a3188147eac12dfa26f06227d8c2144519055fa38f8b88e9c722eae653cae44b5efe620324bb1ef85b7ac4c146a461d3cfc',
      to: 'f61da8f5f17cf215532ccc9ee313e41ae7a59ad0f6adfac7cf4ad96bdc3b41e1',
      amount: 100000,
      precision: 8,
      rpcUrl: 'http://qukuai-icp-1a-1.aws-jp1.huobiidc.com:8080',
      website: 'https://dashboard.internetcomputer.org'
    }
  }

  async checkAccount () {
    try {
      const networkResponse = await fetch(`${this.config.rpcUrl}/network/list`, { method: 'POST' })
      if (!networkResponse.ok) throw new Error(JSON.stringify(await networkResponse.json()))
      const { network_identifiers: [{ blockchain, network }] } = await networkResponse.json()
      this.blockchain = blockchain
      this.network = network

      const accountResponse = await fetch(`${this.config.rpcUrl}/account/balance`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          network_identifier: {
            blockchain: this.blockchain,
            network: this.network
          },
          account_identifier: {
            address: this.config.from
          }
        })
      })
      if (!accountResponse.ok) throw new Error(JSON.stringify(await accountResponse.json()))
      const { balances: [{ value }] } = await accountResponse.json()
      this.balance = value
      console.log(`地址：${this.config.from}, 余额: ${this.balance}`)
    } catch (error) {
      throw new Error(`Error: ${error.message}`)
    }
  }

  async getTxData () {
    try {
      const metadataResponse = await fetch(`${this.config.rpcUrl}/construction/metadata`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          network_identifier: {
            blockchain: this.blockchain,
            network: this.network
          }
        })
      })
      if (!metadataResponse.ok) throw new Error(JSON.stringify(await metadataResponse.json()))
      const { metadata, suggested_fee: [{ value, currency }] } = await metadataResponse.json()
      this.metadata = metadata
      this.fee = value
      this.currency = currency

      const privateKey = Buffer.from(this.config.privateKey, 'hex')
      const pubKey = new Uint8Array(32)
      for (let i = 0; i < pubKey.length; i++) pubKey[i] = privateKey[32 + i]
      const toAddress = bufferFromArrayBufferView(Buffer.from(this.config.to, 'hex')).subarray(4)

      const payloadResponse = await fetch(`${this.config.rpcUrl}/construction/payloads`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          network_identifier: {
            blockchain: this.blockchain,
            network: this.network
          },
          operations: [
            {
              operation_identifier: { index: 0 },
              type: 'TRANSACTION',
              account: {
                address: this.config.from
              },
              amount: {
                value: `${-this.config.amount}`,
                currency: this.currency
              }
            },
            {
              operation_identifier: { index: 1 },
              type: 'TRANSACTION',
              account: {
                address: addressToHex(toAddress)
              },
              amount: {
                value: `${this.config.amount}`,
                currency: this.currency
              }
            },
            {
              operation_identifier: { index: 2 },
              type: 'FEE',
              account: {
                address: this.config.from
              },
              amount: {
                value: `${-this.fee}`,
                currency
              }
            }
          ],
          metadata: this.metadata,
          public_keys: [
            {
              hex_bytes: bufferFromArrayBufferView(pubKey).toString('hex'),
              curve_type: 'edwards25519'
            }
          ]
        })
      })
      if (!payloadResponse.ok) throw new Error(JSON.stringify(await payloadResponse.json()))
      const { unsigned_transaction: unsignedTransaction, payloads } = await payloadResponse.json()
      this.unsignedTransaction = unsignedTransaction
      this.payloads = payloads
    } catch (error) {
      throw new Error(`获取链信息失败: ${error.message}`)
    }
  }

  async signTx () {
    try {
      const { Transaction } = await import(`@tokens/${this.chain}`)
      const tx = new Transaction().hbSign({
        unsigned_transaction: this.unsignedTransaction,
        payloads: this.payloads
      }, [this.config.privateKey])

      const { signatures, unsigned_transaction: unsignedTransaction } = JSON.parse(tx.hbSerialize())
      console.log(`signatures: ${JSON.stringify(signatures)}`)
      console.log(`unsignedTransaction: ${unsignedTransaction}`)

      const response = await fetch(`${this.config.rpcUrl}/construction/combine`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          network_identifier: {
            blockchain: this.blockchain,
            network: this.network
          },
          unsigned_transaction: unsignedTransaction,
          signatures
        })
      })
      if (!response.ok) throw new Error(JSON.stringify(await response.json()))
      const { signed_transaction: signedTransaction } = await response.json()
      this.signedTransaction = signedTransaction
    } catch (error) {
      throw new Error(`签名交易失败: ${error.message}`)
    }
  }

  async sendTx () {
    try {
      const response = await fetch(`${this.config.rpcUrl}/construction/submit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          network_identifier: {
            blockchain: this.blockchain,
            network: this.network
          },
          signed_transaction: this.signedTransaction
        })
      })
      if (!response.ok) throw new Error(JSON.stringify(await response.json()))
      const { transaction_identifier: { hash } } = await response.json()
      this.txHash = hash
      console.log('txHash:', this.txHash)
    } catch (error) {
      throw new Error(`发送交易失败: ${error.message}`)
    }
  }

  async checkTx () {
    try {
      const response = await fetch(`${this.config.rpcUrl}/search/transactions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          network_identifier: {
            blockchain: this.blockchain,
            network: this.network
          },
          transaction_identifier: {
            hash: this.txHash
          }
        })
      })
      if (!response.ok) throw new Error(JSON.stringify(await response.json()))
      const { status } = (await response.json())?.transactions?.[0]?.transaction?.operations?.[0] ?? {}
      if (status) {
        console.log(`交易状态: ${status}\nwebsiteUrl: ${this.config.website}/transaction/${this.txHash}`)
      } else {
        console.log('交易未完成，等待 10 秒后重新检查...')
        setTimeout(async () => {
          await this.checkTx()
        }, 10000)
      }
    } catch (error) {
      throw new Error(`校验上链信息失败: ${error.message}`)
    }
  }
}
