import _ from 'lodash'
import BigNumber from 'bignumber.js'
import { post } from '../lib/utils.js'
import { getChangeOut } from '../lib/utxo-utils.js'

const { map, uniq, keyBy, isEmpty } = _

export default class Transaction {
  constructor (chain) {
    this.chain = chain
    this.config = {
      precision: 6,
      tokenPrecision: {
        WMT: 6,
        KUBE: 6
      },
      vin: [
        {
          address: 'addr1q9mmxz93mw6gxphrn7244d0hs4xepux64wwvcrfmvdr6xmu3c2dlv9eal9dulskyhpqdjawaa9trtqkstsejcntqm3ss09h3va',
          privateKey: 'xprv1yzsvfeu8wcqg48dj5mk8m7a47pg82pn60vxrfyn5vfamgjl0me2w4eg77fhfm5ele6t4qp57el6pu97es3437xk63w9nr0fqj23ge6e02g3434m6a6d5x4xz3tptkr9jk3kwzy403nplq9cgkuur603pdgt9cr88'
          // 下面为tokens的示例，不填写，如果有token会自动将token转给vout中的最后一个
          // tokens: [
          //   {
          //     name: '7370616365636f696e73',
          //     ScriptHash: 'd894897411707efa755a76deb66d26dfd50593f2e70863e1661e98a0',
          //     amount: '100'
          //   }
          // ]
        }
      ],
      vout: [
        {
          address: 'addr1q872d3z2745864x5gmtld63vk7q6sn5362euldvk4lp2z07l56n0l7lclvs78095g32467kdu2p0wva0v45k7nlhuskq542wne',
          // 最小接收金额为1个ada
          amount: '1000000'
          // tokens: [
          //   {
          //     name: '7370616365636f696e73',
          //     ScriptHash: 'd894897411707efa755a76deb66d26dfd50593f2e70863e1661e98a0',
          //     amount: '100'
          //   }
          // ]
        }
      ],
      fee: '200000',
      rpcUrl: 'http://qukuai-ada-1e-1.qcloud-jp-wallet.huobiidc.com:8081',
      website: 'https://cardanoscan.io'
    }
  }

  async getAccountTransactions () {
    const addresses = uniq(map(this.config.vin, 'address'))
    const accountInfo = {}
    for (const address of addresses) {
      const { data: { utxos } } = await post(
        this.config.rpcUrl,
        {
          query: `query {
  utxos(where: { address: { _eq: "${address}" }}) {
    index
    transaction {
      hash
      outputs(
        order_by:{
          index:asc
        }
      ) {
        address
        index
        value
        tokens {
          quantity
          asset {
            assetName
            policyId
            ticker
          }
        }
      }
    }
  }
}
`
        }
      )

      accountInfo[address] = utxos
    }

    return accountInfo
  }

  async checkAccount () {
    const accountInfo = await this.getAccountTransactions()
    for (const address in accountInfo) {
      let balance = new BigNumber('0')
      const tokenBalance = new Map()
      for (const { index, transaction: { outputs } } of accountInfo[address]) {
        const { value, tokens } = outputs[index]
        balance = balance.plus(new BigNumber(value))

        for (const { quantity, asset: { ticker } } of tokens) {
          if (!tokenBalance.has(ticker)) tokenBalance.set(ticker, new BigNumber('0'))

          tokenBalance.set(ticker, tokenBalance.get(ticker).plus(new BigNumber(quantity)))
        }
      }

      const tokenBalanceLog = []
      for (const [assetName, amount] of tokenBalance) {
        tokenBalanceLog.push(`${assetName}: ${amount.dividedBy(10 ** this.config.tokenPrecision[assetName]).toString()}`)
      }
      console.log(`地址：${address}, 余额: ${balance.dividedBy(10 ** this.config.precision).toString()} ${tokenBalanceLog.join(' ')}`)
    }
  }

  async getTxData () {
    try {
      const {
        data: {
          genesis: {
            alonzo: { lovelacePerUTxOWord, maxValueSize },
            shelley: {
              protocolParams: {
                maxTxSize,
                minFeeA,
                minFeeB,
                minUTxOValue,
                poolDeposit,
                keyDeposit
              }
            }
          }
        }
      } = await post(
        this.config.rpcUrl,
        {
          query: `{
  genesis {
    alonzo {
      lovelacePerUTxOWord
      maxValueSize
    }
    shelley {
      protocolParams {
        maxTxSize
        minFeeA,
        minFeeB,
        minUTxOValue,
        poolDeposit,
        keyDeposit
      }
    }
  } 
}
`
        }
      )

      const { data: { cardano: { tip: { slotNo } } } } = await post(
        this.config.rpcUrl,
        {
          query: '{cardano{tip{slotNo}}}'
        }
      )

      this.baseInfo = {
        expire_block_height: slotNo + 100,
        min_fee_a: `${minFeeA}`,
        min_fee_b: `${minFeeB}`,
        min_utxo_value: `${minUTxOValue}`,
        pool_deposit: `${poolDeposit}`,
        key_deposit: `${keyDeposit}`,
        max_value_size: `${maxValueSize}`,
        max_tx_size: `${maxTxSize}`,
        coins_per_utxo_word: `${lovelacePerUTxOWord}`
      }
    } catch (error) {
      throw new Error(`获取链信息失败: ${error.message}`)
    }
  }

  getChangeTokens (vin, vout) {
    const vinTokenAmount = new Map()
    const tokenInfo = {}
    for (const { tokens } of vin) {
      if (!isEmpty(tokens)) {
        for (const { quantity, asset: { ticker, assetName, policyId } } of tokens) {
          tokenInfo[ticker] = { assetName, policyId }
          if (!vinTokenAmount.has(ticker)) vinTokenAmount.set(ticker, new BigNumber('0'))
          vinTokenAmount.set(ticker, vinTokenAmount.get(ticker).plus(new BigNumber(quantity)))
        }
      }
    }

    const voutTokenAmount = new Map()
    for (const { tokens } of vout) {
      if (!isEmpty(tokens)) {
        for (const { quantity, asset: { ticker } } of tokens) {
          if (!voutTokenAmount.has(ticker)) voutTokenAmount.set(ticker, new BigNumber('0'))
          voutTokenAmount.set(ticker, voutTokenAmount.get(ticker).plus(new BigNumber(quantity)))
        }
      }
    }

    const changeTokens = []
    for (const [assetName, tokenAmount] of vinTokenAmount) {
      if (voutTokenAmount.has(assetName)) {
        const changeAmount = tokenAmount.minus(voutTokenAmount[assetName])
        if (!changeAmount.isLessThanOrEqualTo(new BigNumber(0))) {
          changeTokens.push({
            name: tokenInfo[assetName].assetName,
            ScriptHash: tokenInfo[assetName].policyId,
            amount: tokenAmount.minus(voutTokenAmount[assetName]).toString()
          })
        }
      } else {
        changeTokens.push({
          name: tokenInfo[assetName].assetName,
          ScriptHash: tokenInfo[assetName].policyId,
          amount: tokenAmount.toString()
        })
      }
    }

    return changeTokens
  }

  async signTx () {
    try {
      const { Transaction } = await import(`@tokens/${this.chain}`)
      const privateKeyMap = keyBy(this.config.vin, 'address')
      const accountInfo = await this.getAccountTransactions()
      const vin = []
      const privateKeys = []
      for (const address in accountInfo) {
        for (const { index, transaction: { hash, outputs } } of accountInfo[address]) {
          const { value, tokens } = outputs[index]
          for (const token of tokens) {
            token.name = token.asset.assetName
            token.ScriptHash = token.asset.policyId
            token.amount = token.quantity
          }
          vin.push({
            hash,
            vout_index: index,
            address,
            amount: value,
            tokens
          })

          privateKeys.push(privateKeyMap[address].privateKey)
        }
      }

      const { vout, fee } = this.config
      const changeOut = getChangeOut({ vin, vout, fee })
      changeOut && this.config.vout.push(changeOut)

      const changeTokens = this.getChangeTokens(vin, vout)
      if (!isEmpty(changeTokens)) {
        const out = vout[vout.length - 1]
        if (out.tokens) {
          out.tokens.push(...changeTokens)
        } else {
          out.tokens = changeTokens
        }
      }
      const tx = new Transaction()
      await tx.hbBuild({
        ...this.baseInfo,
        to: vin[0].address,
        vin,
        vout: this.config.vout
      })
      tx.hbSign(privateKeys)

      this.rawTx = tx.hbSerialize()
      console.log(`rawTx: ${this.rawTx}`)
    } catch (error) {
      throw new Error(`签名交易失败: ${error.message}`)
    }
  }

  async sendTx () {
    try {
      const { data: { submitTransaction: { hash } } } = await post(
        this.config.rpcUrl,
        {
          query: `mutation  { submitTransaction(transaction: "${this.rawTx}") { hash } }`
        }
      )

      this.txHash = hash
      console.log(`txHash: ${this.txHash}`)
    } catch (error) {
      throw new Error(`发送交易失败: ${error.message}`)
    }
  }

  async checkTx (index = 0) {
    try {
      console.log('正在检查交易状态...')
      const response = await post(
        this.config.rpcUrl,
        {
          query: `query {
  transactions (where:{ hash:{ _eq: "${this.txHash}" } } ) {
    block { number }
  }
}
`
        }
      )

      if (!isEmpty(response?.data?.transactions[0].block)) {
        console.log(`交易状态: success\nwebsiteUrl: ${this.config.website}/transaction/${this.txHash}`)
      } else {
        console.log('交易未完成，等待 30 秒后重新检查...')
        setTimeout(async () => {
          await this.checkTx()
        }, 30000)
      }
    } catch (error) {
      // 上链较慢，所以前几次查询交易详情可能报错，增加重试次数
      if (index > 4) {
        throw new Error(`校验上链信息失败: ${error.message}`)
      } else {
        setTimeout(async () => {
          await this.checkTx(index + 1)
        }, 30000)
      }
    }
  }
}
