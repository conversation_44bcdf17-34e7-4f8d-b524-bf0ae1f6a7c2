import { ethers } from 'ethers'

export default class Transaction {
  constructor (chain, config) {
    this.chain = chain
    this.config = config

    try {
      this.provider = new ethers.JsonRpcProvider(this.config.rpcUrl)
      this.wallet = new ethers.Wallet(this.config.privateKey, this.provider)
      if (this.wallet.address.toLowerCase() !== this.config.from.toLowerCase()) {
        throw new Error(`私钥与from地址不匹配: ${this.wallet.address} != ${this.config.from}`)
      }
      console.log(`初始化成功，使用地址: ${this.wallet.address}`)
    } catch (error) {
      throw new Error(`初始化失败: ${error.message}`)
    }
  }

  async checkAccount () {
    try {
      const balance = await this.wallet.provider.getBalance(this.config.from)
      this.balance = ethers.formatUnits(balance, this.config.precision || 18)

      this.nonce = await this.wallet.provider.getTransactionCount(this.config.from, 'latest')
      this.gasLimit = 25000
      console.log(`地址: ${this.config.from}`)
      console.log(`Nonce: ${this.nonce}`)
      console.log(`余额: ${this.balance} ${this.chain.toUpperCase()}`)

      if (this.config.amount) {
        const amountInWei = this.config.amount
        if (balance < amountInWei) {
          console.log('balance', balance)
          console.log('amount', amountInWei)
          throw new Error(`地址: ${this.config.from} 余额不足, balance: ${this.balance} ${this.chain.toUpperCase()}`)
        }
      }
    } catch (error) {
      throw new Error(`获取账户信息失败: ${error.message}`)
    }
  }

  async getTxData () {
    try {
      const feeData = await this.provider.getFeeData()
      const gasPrice = feeData.gasPrice
      this.gasPrice = gasPrice * BigInt(105) / BigInt(100)

      console.log(`当前Gas价格: ${ethers.formatUnits(gasPrice, 'gwei')} Gwei`)
      console.log(`使用Gas价格: ${ethers.formatUnits(this.gasPrice, 'gwei')} Gwei (增加5%)`)

      this.gasLimit = BigInt(this.gasLimit)
      console.log(`Gas限制: ${this.gasLimit}`)
    } catch (error) {
      throw new Error(`获取交易信息失败: ${error.message}`)
    }
  }

  // 签名交易 - 仅使用Type 0交易
  async signTx () {
    try {
      if (!this.gasPrice) {
        await this.getTxData()
      }

      console.log('准备签名交易...')
      console.log('使用传统交易类型 (Type 0)')

      const tx = {
        type: 0, // 明确指定Type 0交易
        to: this.config.to,
        value: this.config.amount,
        gasLimit: this.gasLimit,
        gasPrice: this.gasPrice,
        nonce: this.nonce,
        chainId: this.config.chainId,
        data: '0x' // 空数据
      }
      console.log('tx.gasPrice', tx.gasPrice)
      console.log('交易详情:', {
        type: tx.type,
        from: this.wallet.address,
        to: tx.to,
        value: ethers.formatUnits(tx.value, this.config.precision || 18),
        nonce: tx.nonce,
        gasPrice: ethers.formatUnits(tx.gasPrice, 'gwei') + ' Gwei',
        gasLimit: tx.gasLimit.toString(),
        chainId: tx.chainId
      })

      const signedTx = await this.wallet.signTransaction(tx)
      this.rawTx = signedTx

      console.log('交易签名成功')
    } catch (error) {
      throw new Error(`签名交易失败: ${error.message}`)
    }
  }

  async sendTx () {
    try {
      if (!this.rawTx) {
        await this.signTx()
      }

      console.log('发送交易...')

      const tx = await this.provider.broadcastTransaction(this.rawTx)
      this.txHash = tx.hash

      console.log(`交易已发送，Hash: ${this.txHash}`)
    } catch (error) {
      throw new Error(`发送交易失败: ${error.message}`)
    }
  }

  // 检查交易状态
  async checkTx () {
    try {
      console.log('正在检查交易状态...')

      // 获取交易收据
      const receipt = await this.provider.getTransactionReceipt(this.txHash)

      if (receipt) {
        const success = receipt.status === 1
        console.log(`交易状态: ${success ? '成功' : '失败'}`)
        console.log(`Gas使用量: ${receipt.gasUsed}`)
        console.log(`交易详情: ${this.config.website}/tx/${this.txHash}`)
        return receipt
      }

      // 如果没有收据，说明交易还在pending状态
      console.log('交易未完成，等待5秒后重新检查...')

      return new Promise(resolve => {
        setTimeout(async () => {
          const result = await this.checkTx()
          resolve(result)
        }, 5000)
      })
    } catch (error) {
      throw new Error(`校验上链信息失败: ${error.message}`)
    }
  }
}
