import { post } from '../lib/utils.js'

export default class Transaction {
  constructor (chain) {
    this.chain = chain
    this.config = {
      from: 'klyu6sge9tzx4bz8kgdhkx6mroq35jeox8vox55jj',
      privateKey: 'e0a35dae4e872173bf2b90bd3766be9a4e61a0c3980c3c0225fa98a005586230ed0cead497fd15d7cc972710f2b5c64cfca3bda78b4d3e2c89068d2b0dea1b3d',
      to: 'klyhh4w9kskj9apez53uv5gfenup9u5wux5g5qak8',
      amount: ********,
      fee: ********,
      precision: 8,
      rpcUrl: 'http://qukuai-kly-1e-1.qcloud-jp-wallet.huobiidc.com:8081',
      apiUrl: 'https://mainnet-service.klayr.xyz',
      website: 'https://explorer.klayr.xyz'
    }
  }

  async checkAccount () {
    try {
      const response = await fetch(`${this.config.rpcUrl}/api/v3/token/balances?address=${this.config.from}`)
      if (!response.ok) throw new Error(JSON.stringify(await response.json()))
      const { data: [{ availableBalance }] } = await response.json()
      this.balance = availableBalance
      console.log(`地址：${this.config.from}, 余额: ${this.balance}`)
    } catch (error) {
      throw new Error(`Error: ${error.message}`)
    }
  }

  async getTxData () {
    try {
      const response = await fetch(`${this.config.rpcUrl}/api/v3/auth?address=${this.config.from}`)
      if (!response.ok) throw new Error(JSON.stringify(await response.json()))
      const { data: { nonce } } = await response.json()
      this.nonce = nonce
    } catch (error) {
      throw new Error(`获取链信息失败: ${error.message}`)
    }
  }

  async signTx () {
    try {
      const { Transaction } = await import(`@tokens/${this.chain}`)
      const tx = new Transaction().hbSign({
        module: 'token',
        chain_id: '00000000',
        token_id: '0000000000000000',
        tx_type: 'transfer',
        nonce: this.nonce,
        to: this.config.to,
        to_tag: '',
        amount: this.config.amount,
        fee: this.config.fee
      }, [this.config.privateKey])
      this.txHash = tx.hbGetTxHash()
      console.log(`txHash: ${this.txHash}`)
      this.rawTx = tx.hbSerialize()
      console.log(`rawTx: ${this.rawTx}`)
    } catch (error) {
      throw new Error(`签名交易失败: ${error.message}`)
    }
  }

  async sendTx () {
    try {
      const response = await post(`${this.config.rpcUrl}/api/v3/transactions`, { transaction: this.rawTx })
      console.log('broadcast response:', response)
    } catch (error) {
      throw new Error(`发送交易失败: ${error.message}`)
    }
  }

  async checkTx () {
    try {
      const response = await fetch(`${this.config.apiUrl}/api/v3/transactions?transactionID=${this.txHash}`)
      if (!response.ok) throw new Error(JSON.stringify(await response.json()))
      const { executionStatus } = (await response.json())?.data?.[0] ?? {}
      if (executionStatus && executionStatus !== 'pending') {
        console.log(`交易状态: ${executionStatus}\nwebsiteUrl: ${this.config.website}/transaction/${this.txHash}`)
      } else {
        console.log('交易未完成，等待 10 秒后重新检查...')
        setTimeout(async () => {
          await this.checkTx()
        }, 10000)
      }
    } catch (error) {
      throw new Error(`校验上链信息失败: ${error.message}`)
    }
  }
}
