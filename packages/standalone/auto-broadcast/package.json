{"name": "@standalone/auto-broadcast", "version": "1.1.2", "description": "auto broadcast", "type": "module", "author": "blockchain-group", "license": "ISC", "private": true, "dependencies": {"@tokens/enj1": "workspace:*", "@tokens/ksm": "workspace:*", "@tokens/dydx1": "workspace:*", "@tokens/init": "workspace:*", "@tokens/algo": "workspace:*", "@tokens/apt": "workspace:*", "@tokens/ar": "workspace:*", "@tokens/ckb": "workspace:*", "@tokens/flow": "workspace:*", "@tokens/sxp1": "workspace:*", "@tokens/wax1": "workspace:*", "@tokens/aca": "workspace:*", "@tokens/astr": "workspace:*", "@tokens/azero": "workspace:*", "@tokens/cru1": "workspace:*", "@tokens/dbc1": "workspace:*", "@tokens/deso": "workspace:*", "@tokens/dock2": "workspace:*", "@tokens/dot": "workspace:*", "@tokens/eos1": "workspace:*", "@tokens/fis": "workspace:*", "@tokens/iota1": "workspace:*", "@tokens/kma": "workspace:*", "@tokens/nodl": "workspace:*", "@tokens/poly1": "workspace:*", "@tokens/sdn": "workspace:*", "@tokens/ahdot": "workspace:*", "@tokens/unq": "workspace:*", "@tokens/avail": "workspace:*", "@tokens/ada": "workspace:*", "@tokens/mina": "workspace:*", "@tokens/nil": "workspace:*", "@tokens/slf": "workspace:*", "@tokens/steem1": "workspace:*", "@tokens/ton": "workspace:*", "@tokens/xrp": "workspace:*", "@tokens/xtz": "workspace:*", "@tokens/icp": "workspace:*", "@tokens/clore": "workspace:*", "@tokens/kly": "workspace:*", "@tokens/sol": "workspace:*", "@tokens/ae1": "workspace:*", "@tokens/lat": "workspace:*", "bignumber.js": "9.1.1", "ethers": "6.13.5", "lodash": "4.17.21"}}