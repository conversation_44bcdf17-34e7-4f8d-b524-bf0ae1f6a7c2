# EOS区块链服务

这是一个用于获取 EOS 区块链数据的 HTTP 服务，通过 WebSocket 与 EOS 节点通信，提供简单的 HTTP API 来查询区块，并解析数据

## 系统需求

- Node.js v22 lts
- PM2 (全局安装)

## 配置

服务配置通过 `ecosystem.json` 文件进行管理。主要改动配置项如下：

- `WS_URL`: EOS 节点的 WebSocket 地址（默认：ws://your-websocket-url）
- `HTTP_PORT`: HTTP服务监听端口（默认：3000）

## 使用方法

### 启动服务 

> pm2 start ecosystem.json

### 查看日志

> pm2 logs

### 重启服务

> pm2 restart ecosystem.json

## API文档

### 获取区块数据

```
GET /block/:num
```

- `:num` - 区块编号

### 接口使用

> curl http://${host}:${port}/block/:num # n 为区块高度 e.g: 436895282

### 特殊说明

因 ws eos 节点走快照模式，没有历史记录，所以如果测试的 num 距离当前块高过远，无法返回数据属于正常情况
