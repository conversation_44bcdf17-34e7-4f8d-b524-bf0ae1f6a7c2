/* global WebSocket */
import express from 'express'
import { Serialize } from 'eosjs'
import { TextEncoder, TextDecoder } from 'node:util'

class BlockService {
  #socket
  #abi
  #pending = new Map()
  #createBuffer = () => new Serialize.SerialBuffer({ textEncoder: new TextEncoder(), textDecoder: new TextDecoder() })

  constructor (wsUrl) {
    this.#socket = new WebSocket(wsUrl) // node 22 原生支持
    this.#socket.binaryType = 'arraybuffer'
    this.#socket.onmessage = this.#handleMessage.bind(this)
  }

  #handleMessage ({ data }) {
    if (!this.#abi) {
      this.#abi = Serialize.getTypesFromAbi(Serialize.createInitialTypes(), JSON.parse(data))
      console.log('Websocket ABI init successfully')
      return
    }

    const buffer = this.#createBuffer()
    buffer.pushArray(new Uint8Array(data))
    const [, result] = this.#abi.get('result').deserialize(buffer)
    const blockNum = result.this_block?.block_num

    if (result.traces) {
      const traceBuffer = this.#createBuffer()
      traceBuffer.pushArray(Serialize.hexToUint8Array(result.traces))
      result.traces = Array.from(
        { length: traceBuffer.getVaruint32() },
        () => this.#abi.get('transaction_trace').deserialize(traceBuffer)[1]
      )
    }

    if (blockNum) {
      this.#pending.get(`${blockNum}`)?.(result)
      this.#pending.delete(`${blockNum}`)
    }
  }

  async getBlock (blockNum) {
    // 使用Promise.withResolvers()替代传统Promise构造函数
    const { promise, resolve, reject } = Promise.withResolvers()

    this.#pending.set(blockNum, resolve)

    try {
      const buffer = this.#createBuffer()
      this.#abi.get('request').serialize(buffer, [
        'get_blocks_request_v0', {
          start_block_num: `${blockNum}`,
          end_block_num: `${BigInt(blockNum) + 1n}`, // 当前块+1
          max_messages_in_flight: '1', // 只请求1块
          have_positions: [],
          irreversible_only: true, // 只请求不可逆块
          fetch_block: true,
          fetch_traces: true,
          fetch_deltas: false
        }
      ])
      this.#socket.send(buffer.asUint8Array())

      // 30 秒防请求挂起，队列清理
      setTimeout(() => {
        if (this.#pending.has(blockNum)) {
          this.#pending.delete(blockNum)
          reject(new Error(`Request timeout for block ${blockNum}`))
        }
      }, 30000)
    } catch (err) {
      this.#pending.delete(blockNum)
      reject(err)
    }

    return promise
  }
}

const service = new BlockService(process.env.WS_URL)
const app = express()

app.get('/block/:num', async (req, res) => {
  try {
    const data = await service.getBlock(req.params.num)
    res.json({ result: true, data })
  } catch (e) {
    console.error(`Error fetching block ${req.params.num}:`, e.message)
    res.status(500).json({ result: false, error: e.message })
  }
})
app.use((_, res) => res.status(404).json({ result: false, error: 'Not Found!' }))
app.listen(process.env.HTTP_PORT, () => {
  console.log(`Server running on http://localhost:${process.env.HTTP_PORT}`)
  process.send?.('ready') // pm2 专用
})
