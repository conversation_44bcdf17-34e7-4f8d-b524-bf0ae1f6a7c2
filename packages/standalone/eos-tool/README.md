# EOS Swap Tools
主要功能包括： 账号创建、RAM\CPU\NET资源购买、EOS质押、取消质押、转账等功能

## 公共步骤
1. 安装依赖
   ```sh
    # 准备 node v18.17.1 环境
    export PATH=$PATH:/path/to/bin
    mkdir eos-tool
    npm init -y
    npm install @standalone/eos-tool
   ```
2. 根据实际情况修改config.js文件中的配置信息
3. npx start

## 转账步骤
1. 执行 npx start & 选择目录树选项getChainInfo
2. 执行 npx start & 选择目录树选项transfer
3. 其他功能类似，每次操作前都需要获取新的ChainInfo
4. 交易广播（测试网另选节点地址 注：工具支持广播可选 可手动广播也可以自动广播）
   ```sh
   附：
   curl http://qukuai-eos-bcst-1a-2.aws-jp1.huobiidc.com:8080/v1/chain/push_transaction -d ''
   ```

## 修改权限步骤
注：keystore.json是与eos_wallet.dat成对生成的，修改eos权限时并没用到，但是在register时会用到
1. 执行 npx start & 选择目录树选项getChainInfo
2. 执行 npx start & 选择目录树选项transferPermission
3. 用老的eos_wallet.dat账户进行一次转账测试原有公私钥是否有权限
4. 用新的eos_wallet.dat账户进行一次转账测试新的公私钥是否有权限
5. 将新的eos_wallet.dat进行备份 而老的eos_wallet.dat & keystore.json适情况保留

## Remark
- 如果测试过程中出现Transaction exceeded the current CPU usage limit imposed on the transaction的报错信息
- 可以用别的账户给他质押stake一些eos代币到 NET bandwidth & CPU bandwidth
- 质押可以给自己质押也可以给别的地址质押 需注意在交互环节Transfer voting power and right to receiver时选择No
- 否则报错：cannot use transfer flag if delegating to self
- 取消质押可以指定退还账户，可以是发起账户也可以是其他账户接收
- 注意保管db信息(eos_wallet.dat)
- 操作之前必须找领导审批及输入密码事宜
