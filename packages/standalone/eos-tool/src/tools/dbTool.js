import inquirer from 'inquirer'
import { model } from '../conf/config.js'

export async function main () {
  const sqliteQuestions = [
    {
      type: 'list',
      name: 'choice',
      message: 'Plz choose the action of CRUD"',
      choices: ['create', 'retrive', 'update', 'delete', 'custom']
    },
    {
      type: 'input',
      name: 'sql',
      message: 'Input SQL to CRUD for eos.db. like:',
      default: 'insert into acct (account, public_key) values (\'xxx\',\'yyy\');',
      when (answers) {
        return answers.choice === 'create'
      }
    },
    {
      type: 'input',
      name: 'sql',
      message: 'Input SQL to CRUD for eos.db. like:',
      default: 'select * from acct where account=\'xxx\'',
      when (answers) {
        return answers.choice === 'retrive'
      }
    },
    {
      type: 'input',
      name: 'sql',
      message: 'Input SQL to CRUD for eos.db. like:',
      default: 'update acct set active=false where account=\'xxx\'',
      when (answers) {
        return answers.choice === 'update'
      }
    },
    {
      type: 'input',
      name: 'sql',
      message: 'Input SQL to CRUD for eos.db. like:',
      default: 'delete from acct where account=\'xxx\'',
      when (answers) {
        return answers.choice === 'delete'
      }
    },
    {
      type: 'input',
      name: 'sql',
      message: 'Input SQL to CRUD for eos.db. like:',
      default: 'select * from acct where account=\'xxx\' and active=false',
      when (answers) {
        return answers.choice === 'custom'
      }
    }
  ]
  const { sql } = await inquirer.prompt(sqliteQuestions)
  const res = await model.all(sql)
  console.log('result', res)
}
