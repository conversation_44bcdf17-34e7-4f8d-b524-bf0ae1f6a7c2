import fs from 'fs'
import inquirer from 'inquirer'
import { decrypt } from '../lib/aescrypto.js'
import {
  getChainInfo, newAccount, toAssetString,
  loadWallet, pointEosWalletFilePath,
  getInactiveAccount
} from '../lib/utils.js'
import { model, CHAININFOPATH, EOSNODEURL, ASSET, EOSRAWTXSPATH } from '../conf/config.js'
import * as accountGenerate from './accountGenerate.js'

export async function main () {
  try {
    let rows = await getInactiveAccount()
    if (rows.length === 0) {
      const { needGen } = await inquirer.prompt([{
        type: 'confirm',
        name: 'needGen',
        message: 'need generate account?',
        default: false
      }])
      if (needGen) {
        await accountGenerate.main()
        rows = await getInactiveAccount()
      } else {
        return
      }
    }

    const { eos_wallet_filepath: eosWalletFilePath } = await pointEosWalletFilePath(true)
    const wallet = await loadWallet(eosWalletFilePath)
    const questions = [
      {
        type: 'list',
        name: 'creator_account',
        message: 'Which is creator account?',
        choices: Array.from(wallet.keys())
      },
      {
        type: 'password',
        name: 'first_pass',
        message: 'Plz input the first part of password:',
        mask: '*'
      },
      {
        type: 'password',
        name: 'second_pass',
        message: 'Plz input the second part of password:',
        mask: '*'
      },
      {
        type: 'input',
        name: 'buy_ram_bytes',
        message: 'How many ram bytes do you buy?',
        default: 1024 * 8,
        validate (value) {
          const valid = Number.isInteger(parseFloat(value))
          return valid || 'Please enter a int number'
        },
        filter: Number
      },
      {
        type: 'input',
        name: 'stake_net',
        message: 'How many net do you stake?',
        default: 0.02,
        validate (value) {
          const valid = !Number.isNaN(parseFloat(value))
          return valid || 'Please enter a number'
        }
      },
      {
        type: 'input',
        name: 'stake_cpu',
        message: 'How many cpu do you stake?',
        default: 0.02,
        validate (value) {
          const valid = !Number.isNaN(parseFloat(value))
          return valid || 'Please enter a number'
        }
      },
      {
        type: 'list',
        name: 'transfer',
        message: 'Transfer voting power and right to receiver?',
        default: 0,
        choices: ['yes', 'no'],
        filter (val) {
          return val === 'yes' ? 1 : 0
        }
      },
      {
        type: 'confirm',
        name: 'toBroadcast',
        message: 'whether to broadcast?',
        default: false
      }
    ]
    const answers = await inquirer.prompt(questions)
    const password = answers.first_pass + answers.second_pass
    const creatorAccount = answers.creator_account
    const creatorPrivKey = decrypt(decrypt(wallet.get(creatorAccount), password), password)

    const info = await getChainInfo(CHAININFOPATH)
    info.buy_ram_bytes = answers.buy_ram_bytes
    info.stake_net = toAssetString(answers.stake_net, ASSET.EOS.precision, ASSET.EOS.symbol)
    info.stake_cpu = toAssetString(answers.stake_cpu, ASSET.EOS.precision, ASSET.EOS.symbol)
    info.transfer = answers.transfer

    let rawTxsStr = ''
    for (const row of rows) {
      const pubKey = row.public_key
      try {
        console.info(row.id, row.account)
        const tx = await newAccount(EOSNODEURL, creatorAccount, row.account, pubKey, creatorPrivKey, info, answers.toBroadcast)
        console.log('transaction_id:', tx.transaction_id)
        const body = JSON.stringify(tx.transaction)
        console.info('raw_tx:', body)
        if (tx.broadcast) {
          await model.update({ active: true }, { where: { account: row.account } })
        }
        rawTxsStr += `${body}\n`
      } catch (err) {
        const errcode = err.error.code
        const errname = err.error.name
        if (errcode === 3050001 && errname === 'account_name_exists_exception') {
          // 生成一个新的account 更新到sqlite db里面
          console.log('account name already exists:', row.account)
        }
      }
    }
    fs.writeFileSync(EOSRAWTXSPATH, rawTxsStr)
  } catch (e) {
    console.error(e)
  }
}
