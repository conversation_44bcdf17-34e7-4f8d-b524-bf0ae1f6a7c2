import fs from 'fs'
import inquirer from 'inquirer'
import { decrypt } from '../lib/aescrypto.js'
import {
  buyram, getChainInfo, loadWallet, toAssetString,
  pointEosWalletFilePath, pushTransaction
} from '../lib/utils.js'
import { EOSRAWTXSPATH, CHAININFOPATH, EXPIRE_IN_SECONDS, ASSET } from '../conf/config.js'

export async function main () {
  try {
    const { eos_wallet_filepath: eosWalletFilepath } = await pointEosWalletFilePath(true)
    const wallet = await loadWallet(eosWalletFilepath)
    if (wallet.length < 1) {
      console.error('wallet is empty')
      process.exit(1)
    }

    const questions = [
      {
        type: 'list',
        name: 'from',
        message: 'Which account is from?',
        choices: Array.from(wallet.keys())
      },
      {
        type: 'input',
        name: 'receiver',
        message: 'Who is receiver?',
        validate (value) {
          const valid = value.length > 0
          return valid || 'Please specify the to account'
        }
      },
      {
        type: 'input',
        name: 'buy_ram',
        message: 'How many eos would you like to spend to buy ram?',
        default: 1,
        validate (value) {
          const valid = !Number.isNaN(parseFloat(value))
          return valid || 'Please enter a number'
        }
      },
      {
        type: 'password',
        name: 'first_pass',
        message: 'Plz input the first part of password:',
        mask: '*'
      },
      {
        type: 'password',
        name: 'second_pass',
        message: 'Plz input the second part of password:',
        mask: '*'
      },
      {
        type: 'confirm',
        name: 'toBroadcast',
        message: 'whether to broadcast?',
        default: false
      }
    ]

    const answers = await inquirer.prompt(questions)
    const password = answers.first_pass + answers.second_pass

    const info = await getChainInfo(CHAININFOPATH)
    const [expiration] = new Date(new Date().getTime() + EXPIRE_IN_SECONDS * 1000).toISOString().split('.')
    info.headers.expiration = expiration
    info.buy_ram = toAssetString(answers.buy_ram, ASSET.EOS.precision, ASSET.EOS.symbol)
    const privKey = decrypt(decrypt(wallet.get(answers.from), password), password)
    const tx = await buyram(answers.from, answers.receiver, privKey, info)
    console.log('transaction_id:', tx.transaction_id)
    const body = JSON.stringify(tx.transaction)
    console.info('raw_tx:', body)
    if (answers.toBroadcast) {
      await pushTransaction(body)
    }
    fs.writeFileSync(EOSRAWTXSPATH, body)
  } catch (e) {
    console.error(e)
  }
}
