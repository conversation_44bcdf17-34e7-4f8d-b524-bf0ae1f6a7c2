import fs from 'fs'
import assert from 'assert'
import inquirer from 'inquirer'
import keythereum from 'keythereum'
import Web3 from 'web3'
import Tx from 'ethereumjs-tx'
import {
  KEYSTOREPATH, ETHRAWTXSPATH, EOS_SALE_ABI,
  EOS_SALE_CONTRACT_ADDR, ETH_NODE_URL
} from '../conf/config.js'

const web3 = new Web3()
web3.setProvider(new web3.providers.HttpProvider(ETH_NODE_URL, 10000))
const eosSale = web3.eth.contract(EOS_SALE_ABI).at(EOS_SALE_CONTRACT_ADDR)

function objToMap (obj) {
  const strMap = new Map()
  for (const k of Object.keys(obj)) {
    strMap.set(k, obj[k])
  }
  return strMap
}

function buildAndSignTx (nonce, gasPrice, data, privateKey) {
  const rawTx = {
    nonce,
    gasPrice,
    gasLimit: '0x186a0',
    to: EOS_SALE_CONTRACT_ADDR,
    value: '0x00',
    data,
    chainId: 1
  }

  const tx = new Tx(rawTx)
  tx.sign(privateKey)

  return tx
}

export async function main () {
  const questions = [
    {
      type: 'password',
      name: 'first_pass',
      message: 'Plz input the first part of password:',
      mask: '*'
    },
    {
      type: 'password',
      name: 'second_pass',
      message: 'Plz input the second part of password:',
      mask: '*'
    },
    {
      type: 'input',
      name: 'gas_price',
      message: 'Gas Price(Gwei): '
    }
  ]
  const { first_pass: firstPass, second_pass: secondPass, gas_price: gasPriceValue } = await inquirer.prompt(questions)
  const password = firstPass + secondPass
  const gasPrice = web3.toWei(gasPriceValue, 'gwei')

  let rawTxsStr = ''
  const keyMap = objToMap(JSON.parse(fs.readFileSync(KEYSTOREPATH)))
  const newKeyMap = {}
  keyMap.forEach((value, ethAddr) => {
    const ethNonce = value.nonce
    const ethEncryptedPrivKey = value.ethkey
    const eosPubKey = value.eoskey.pubkey
    newKeyMap[ethAddr] = {
      nonce: value.nonce + 1,
      ethkey: value.ethkey,
      eoskey: value.eoskey
    }

    const ethPrivKey = keythereum.recover(password, ethEncryptedPrivKey)
    const encodedData = eosSale.register.getData(eosPubKey)
    // let gasPrice = 10*1e9;
    const tx = buildAndSignTx(web3.fromDecimal(ethNonce), web3.fromDecimal(gasPrice), encodedData, ethPrivKey)
    const serializedTx = tx.serialize()
    const txhash = tx.hash(true).toString('hex')
    const rawtx = serializedTx.toString('hex')
    rawTxsStr += `${rawtx}\n`

    assert(tx.getSenderAddress().toString('hex') === ethAddr, `error: ${ethAddr}`)
    console.log('tx json:', tx.toJSON(true))
    console.log(`tx from: ${tx.getSenderAddress().toString('hex')}`)
    console.log(`tx validate: ${tx.validate()}`)
    console.log(`tx hash: ${txhash}`)
    console.log(`raw tx hex: ${rawtx}`)
    console.log('\n\n\n')
  })

  fs.writeFileSync(ETHRAWTXSPATH, rawTxsStr)
  const json = JSON.stringify(newKeyMap)
  fs.writeFileSync(KEYSTOREPATH, json)

  console.info('finish address verification!')
}
