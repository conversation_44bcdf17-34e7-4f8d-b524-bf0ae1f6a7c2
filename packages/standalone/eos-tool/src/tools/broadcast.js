import inquirer from 'inquirer'
import { EOSNODEURL } from '../conf/config.js'
import { pushTransaction } from '../lib/utils.js'

export async function main () {
  try {
    const questions = [
      {
        type: 'input',
        name: 'raw_tx',
        message: 'Plz input raw_tx'
      },
      {
        type: 'input',
        name: 'node_url',
        message: 'Plz input node_url',
        default: EOSNODEURL
      }
    ]
    const { node_url: nodeUrl, raw_tx: rawTx } = await inquirer.prompt(questions)
    await pushTransaction(rawTx, nodeUrl)
  } catch (e) {
    console.error(e)
  }
}
