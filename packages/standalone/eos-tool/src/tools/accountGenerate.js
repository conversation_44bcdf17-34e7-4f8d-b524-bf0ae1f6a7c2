import fs from 'fs'
import crypto from 'crypto'
import inquirer from 'inquirer'
import eosjsEcc from 'eosjs-ecc'
import keythereum from 'keythereum'
import { encrypt } from '../lib/aescrypto.js'
import {
  pointEosWalletFilePath, pointEosKeystoreFilePath,
  checkFilePathExist, genAccountName
} from '../lib/utils.js'
import { model, KEYSTOREPATH, EOSWALLETFILEPATH } from '../conf/config.js'
import { log } from '../lib/logger.js'

const { PrivateKey } = eosjsEcc

function genOnePrivateKey (password) {
  const params = { keyBytes: 32, ivBytes: 16 }
  const dk = keythereum.create(params)
  const options = {
    kdf: 'pbkdf2',
    cipher: 'aes-128-ctr',
    kdfparams: {
      c: 262144,
      dklen: 32,
      prf: 'hmac-sha256'
    }
  }

  return keythereum.dump(password, dk.privateKey, dk.salt, dk.iv, options)
}

export async function main () {
  let keyStorePath = ''
  let walletPath = ''
  if (fs.existsSync(KEYSTOREPATH)) {
    const { eos_keystore_filepath: eosKeystoreFilepath } = await pointEosKeystoreFilePath()
    if (eosKeystoreFilepath === KEYSTOREPATH) {
      console.error('please point a different path')
      process.exit(1)
    }
    checkFilePathExist(eosKeystoreFilepath)
    keyStorePath = eosKeystoreFilepath
  }
  if (fs.existsSync(EOSWALLETFILEPATH)) {
    const { eos_wallet_filepath: eosWalletFilepath } = await pointEosWalletFilePath()
    if (eosWalletFilepath === EOSWALLETFILEPATH) {
      console.error('please point a different path')
      process.exit(1)
    }
    checkFilePathExist(eosWalletFilepath)
    walletPath = eosWalletFilepath
  }
  keyStorePath = keyStorePath || (await pointEosKeystoreFilePath()).eos_keystore_filepath
  walletPath = walletPath || (await pointEosWalletFilePath()).eos_wallet_filepath

  const questions = [
    {
      type: 'number',
      name: 'count',
      message: 'How many addresses do you want to generate?',
      default: 1,
      validate (input) {
        if (Number.isNaN(input)) {
          return 'Please input a number!'
        }
        return true
      },
      filter: input => parseInt(input, 10)
    },
    {
      type: 'password',
      name: 'first_pass',
      message: 'Plz input the first part of password:',
      mask: '*'
    },
    {
      type: 'password',
      name: 'first_pass_repeat',
      message: 'Plz input the first part of password again:',
      mask: '*',
      validate (input, answers) {
        if (input !== answers.first_pass) {
          return 'The two passwords you typed do not match!'
        }
        return true
      }
    },
    {
      type: 'password',
      name: 'second_pass',
      message: 'Plz input the second part of password:',
      mask: '*'
    },
    {
      type: 'password',
      name: 'second_pass_repeat',
      message: 'Plz input the second part of password again:',
      mask: '*',
      validate (input, answers) {
        if (input !== answers.second_pass) {
          return 'The two passwords you typed do not match!'
        }
        return true
      }
    }
  ]
  const { count, first_pass: firstPass, second_pass: secondPass } = await inquirer.prompt(questions)
  const password = firstPass + secondPass
  const keyMap = {}

  const eosWalletFile = fs.createWriteStream(walletPath)
  eosWalletFile.on('error', err => {
    console.error(`write file ${walletPath} error: ${err}`)
  })

  for (let i = 0; i < count; i += 1) {
    // generate eth keys
    const ethKey = genOnePrivateKey(password)

    // generate eos keys
    const seed = crypto.randomBytes(32)
    const privateKey = PrivateKey.fromBuffer(seed)
    const pubKey = privateKey.toPublic()
    const eosPrivateKeyStr = privateKey.toString()
    const eosPubKeyStr = pubKey.toString()
    const eosEncryptedPrivKey = encrypt(encrypt(eosPrivateKeyStr, password), password)
    const eosKey = {
      pubkey: eosPubKeyStr,
      encryptedprivkey: eosEncryptedPrivKey
    }

    const ethAddr = ethKey.address
    keyMap[ethAddr] = {
      nonce: 0,
      ethkey: ethKey,
      eoskey: eosKey
    }

    const accountName = await genAccountName()
    eosWalletFile.write(`${eosPubKeyStr} ${eosEncryptedPrivKey} ${accountName}\n`)
    log.info(`account info: ${eosPubKeyStr} ${eosEncryptedPrivKey} ${accountName}`)
    await model.create({ account: accountName, public_key: eosPubKeyStr })
  }
  console.info(`${count} addresses have been generated!`)
  log.info(`${count} addresses have been generated!`)

  const json = JSON.stringify(keyMap)
  fs.writeFileSync(keyStorePath, json)

  eosWalletFile.end()

  return { keyStorePath, walletPath }
}
