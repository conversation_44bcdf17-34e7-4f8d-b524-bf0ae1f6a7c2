import fs from 'fs'
import inquirer from 'inquirer'
import { decrypt } from '../lib/aescrypto.js'
import {
  delegatebw, getChainInfo, toAssetString,
  loadWallet, pointEosWalletFilePath
} from '../lib/utils.js'
import { EOSRAWTXSPATH, CHAININFOPATH, EXPIRE_IN_SECONDS, ASSET, EOSNODEURL } from '../conf/config.js'

export async function main () {
  try {
    const { eos_wallet_filepath: eosWalletFilepath } = await pointEosWalletFilePath(true)
    const wallet = await loadWallet(eosWalletFilepath)
    if (wallet.length < 1) {
      console.error('wallet is empty')
      process.exit(1)
    }

    const questions = [
      {
        type: 'list',
        name: 'from',
        message: 'Which account is from?',
        choices: Array.from(wallet.keys())
      },
      {
        type: 'input',
        name: 'receiver',
        message: 'Who is receiver?',
        validate (value) {
          const valid = value.length > 0
          return valid || 'Please specify the to account'
        }
      },
      {
        type: 'input',
        name: 'stake_net',
        message: 'How many net do you stake?',
        default: 0.02,
        validate (value) {
          const valid = !Number.isNaN(parseFloat(value))
          return valid || 'Please enter a number'
        }
      },
      {
        type: 'input',
        name: 'stake_cpu',
        message: 'How many cpu do you stake?',
        default: 0.02,
        validate (value) {
          const valid = !Number.isNaN(parseFloat(value))
          return valid || 'Please enter a number'
        }
      },
      {
        type: 'list',
        name: 'transfer',
        message: 'Transfer voting power and right to receiver?',
        default: 0,
        choices: ['yes', 'no'],
        filter (val) {
          return val === 'yes' ? 1 : 0
        }
      },
      {
        type: 'password',
        name: 'first_pass',
        message: 'Plz input the first part of password:',
        mask: '*'
      },
      {
        type: 'password',
        name: 'second_pass',
        message: 'Plz input the second part of password:',
        mask: '*'
      },
      {
        type: 'confirm',
        name: 'toBroadcast',
        message: 'whether to broadcast?',
        default: false
      }
    ]

    const answers = await inquirer.prompt(questions)
    const password = answers.first_pass + answers.second_pass

    const info = await getChainInfo(CHAININFOPATH)
    const [expiration] = new Date(new Date().getTime() + EXPIRE_IN_SECONDS * 1000).toISOString().split('.')
    info.headers.expiration = expiration
    info.stake_net = toAssetString(answers.stake_net, ASSET.EOS.precision, ASSET.EOS.symbol)
    info.stake_cpu = toAssetString(answers.stake_cpu, ASSET.EOS.precision, ASSET.EOS.symbol)
    info.transfer = answers.transfer
    info.url = EOSNODEURL
    const privKey = decrypt(decrypt(wallet.get(answers.from), password), password)
    const tx = await delegatebw(answers.from, answers.receiver, privKey, info, answers.toBroadcast)
    console.log('transaction_id:', tx.transaction_id)
    const body = JSON.stringify(tx.transaction)
    console.info('raw_tx:', body)
    fs.writeFileSync(EOSRAWTXSPATH, body)
  } catch (e) {
    console.error(e)
  }
}
