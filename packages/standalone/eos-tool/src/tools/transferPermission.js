import fs from 'fs'
import inquirer from 'inquirer'
import eosjsEcc from 'eosjs-ecc'
import { decrypt } from '../lib/aescrypto.js'
import {
  updateauth, getChainInfo, loadWallet, pointEosWalletFilePath,
  getActiveAndOwnerKey, checkFilePathExist, updateAccountToTargetWallet
} from '../lib/utils.js'
import { model, CHAININFOPATH, EOSRAWTXSPATH, EXPIRE_IN_SECONDS, EOSNODEURL } from '../conf/config.js'
import * as accountGenerate from './accountGenerate.js'

const { PublicKey } = eosjsEcc

export async function main () {
  try {
    const { eos_wallet_filepath: eosWalletFilepath } = await pointEosWalletFilePath(true)
    checkFilePathExist(eosWalletFilepath, false)
    const wallet = await loadWallet(eosWalletFilepath)
    if (wallet.length < 1) {
      console.error('wallet is empty')
      process.exit(1)
    }

    const questions = [
      {
        type: 'list',
        name: 'account',
        message: 'Which account need to change auth?',
        choices: Array.from(wallet.keys())
      },
      {
        type: 'password',
        name: 'first_pass',
        message: 'Plz input the first part of password:',
        mask: '*'
      },
      {
        type: 'password',
        name: 'second_pass',
        message: 'Plz input the second part of password:',
        mask: '*'
      },
      {
        type: 'confirm',
        name: 'toGenerate',
        message: 'whether to generate new account?',
        default: false
      },
      {
        type: 'input',
        name: 'newPublicKey',
        message: 'Please input a valid public key to updateAuth',
        validate (value) {
          const valid = PublicKey.isValid(value)
          return valid || 'Please input a valid public key'
        },
        when (res) {
          return !res.toGenerate
        }
      },
      {
        type: 'confirm',
        name: 'toBroadcast',
        message: 'whether to broadcast?',
        default: false
      }
    ]
    const answers = await inquirer.prompt(questions)
    const password = answers.first_pass + answers.second_pass

    let targetWalletPath = ''
    if (answers.toGenerate) {
      const { walletPath } = await accountGenerate.main()
      targetWalletPath = walletPath
      const keyLine = fs.readFileSync(walletPath).toString().trim().split('\n')[0]
      const [publicKey] = keyLine.split(' ')
      answers.newPublicKey = publicKey
    }

    const info = await getChainInfo(CHAININFOPATH)
    const [expiration] = new Date(new Date().getTime() + EXPIRE_IN_SECONDS * 1000).toISOString().split('.')
    info.headers.expiration = expiration
    info.broadcast = false
    info.url = EOSNODEURL
    const privateKey = decrypt(decrypt(wallet.get(answers.account), password), password)

    const tx = await updateauth(answers.account, privateKey, answers.newPublicKey, info, answers.toBroadcast)
    console.log('transaction_id:', tx.transaction_id)
    const body = JSON.stringify(tx.transaction)
    console.info('raw_tx:', body)
    if (tx.broadcast) {
      // 查询旧账户在链上最新的账户信息
      const keyMap = getActiveAndOwnerKey(answers.account)
      if (keyMap.active === answers.newPublicKey && keyMap.owner === answers.newPublicKey) {
        await model.delete({ account: answers.account })
        await model.update({ account: answers.account }, { where: { public_key: answers.newPublicKey } })
        if (targetWalletPath) {
          updateAccountToTargetWallet(targetWalletPath, answers.account, answers.newPublicKey)
        }
      }
    }
    fs.writeFileSync(EOSRAWTXSPATH, body)
  } catch (e) {
    console.error(e)
  }
}
