import fs from 'fs'
import path from 'path'
import inquirer from 'inquirer'
import {
  pointEosWalletFilePath,
  eosWalletDatLineToRecord,
  getDataProcessResult
} from '../lib/utils.js'
import { EOSWALLETDIR } from '../conf/config.js'

export async function main () {
  const { eos_wallet_filepath: eosWalletFilepath } = await pointEosWalletFilePath()
  const questions = [
    {
      type: 'password',
      name: 'first_pass',
      message: '<PERSON>lz input the first part of password:',
      mask: '*'
    },
    {
      type: 'password',
      name: 'second_pass',
      message: '<PERSON>lz input the second part of password:',
      mask: '*'
    },
    {
      type: 'input',
      name: 'eos_record_filepath',
      message: 'Plz input a filename of eos record',
      default: 'eos_record.txt', // 私钥是加密的
      filter: input => {
        let inputFilePath
        if (path.isAbsolute(input)) {
          inputFilePath = input
        } else {
          inputFilePath = path.join(EOSWALLETDIR, input)
        }
        return inputFilePath
      }
    },
    {
      type: 'input',
      name: 'separator',
      message: 'Separator of fields',
      default: ','
    }
  ]
  const { first_pass: firstPass, second_pass: secondPass, eos_record_filepath: eosRecordFilepath, separator } = await inquirer.prompt(questions)
  const password = firstPass + secondPass

  const { writeLines } = await getDataProcessResult(eosWalletFilepath, password, separator, eosWalletDatLineToRecord)

  fs.writeFileSync(eosRecordFilepath, writeLines)

  console.info('finish')
}
