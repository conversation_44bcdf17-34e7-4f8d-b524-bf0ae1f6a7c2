import fs from 'fs'
import crypto from 'crypto'
import ecc from 'eosjs-ecc'
import inquirer from 'inquirer'
import { decrypt } from '../lib/aescrypto.js'
import { pointEosWalletFilePath } from '../lib/utils.js'

export async function main () {
  const msg = 'huobi@123'
  const msgHash = crypto.createHash('sha256').update(msg).digest()
  const questions = [
    {
      type: 'password',
      name: 'first_pass',
      message: '<PERSON><PERSON><PERSON> input the first part of password:',
      mask: '*'
    },
    {
      type: 'password',
      name: 'second_pass',
      message: '<PERSON><PERSON><PERSON> input the second part of password:',
      mask: '*'
    }
  ]
  const { first_pass: firstPass, second_pass: secondPass } = await inquirer.prompt(questions)
  const password = firstPass + secondPass

  const { eos_wallet_filepath: eosWalletFilepath } = await pointEosWalletFilePath(true)
  const keyList = fs.readFileSync(eosWalletFilepath).toString().split('\n')
  keyList.forEach(line => {
    line = line.trim()
    if (line) {
      const pubkeyPrivkey = line.split(' ')
      const pubKey = pubkeyPrivkey[0]
      const encryptedPrivKey = pubkeyPrivkey[1]
      const privKey = decrypt(decrypt(encryptedPrivKey, password), password)
      const signature = ecc.signHash(msgHash, privKey)
      const res = ecc.verifyHash(signature, msgHash, pubKey)
      if (res === false) {
        console.error(`${pubKey}'s private key and public key do not match!`)
      }
      const derivedPubKey = ecc.privateToPublic(privKey)
      if (pubKey !== derivedPubKey) {
        console.error(`expected public key ${pubKey} is not equal to derived public key ${derivedPubKey}!`)
      }
    }
  })
  console.info('finish address verification!')
}
