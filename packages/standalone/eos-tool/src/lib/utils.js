import fs from 'fs'
import path from 'path'
import Eos from 'eosjs'
import fuzzy from 'fuzzy'
import shelljs from 'shelljs'
import inquirer from 'inquirer'
import randomstring from 'randomstring'
import autocomplete from 'inquirer-autocomplete-prompt'
import lodash from 'lodash'
import readline from 'readline'
import ProgressBar from 'progress'
import { Aes } from '@common/crypto'
import { encrypt, decrypt } from './aescrypto.js'
import {
  model, EXPIRE_IN_SECONDS, KEYSTOREPATH,
  ASSET, EOSNODEURL, KEYSTOREPATHWITHDATETIME,
  EOSWALLETDIR, EOSWALLETFILEPATH, EOSWALLETFILEPATHWITHDATETIME
} from '../conf/config.js'

const { random } = lodash

inquirer.registerPrompt('autocomplete', autocomplete)

async function chooseTool (name, message, choice) {
  return inquirer.prompt([{
    type: 'autocomplete',
    name,
    message,
    source (answers, input = '') {
      return new Promise(resolve => {
        setTimeout(() => {
          resolve(fuzzy.filter(input, choice).map(el => el.original))
        }, random(30, 500))
      })
    },
    pageSize: 30
  }])
};

async function transfer (from, to, value, memo, privateKey, extraInfo) {
  const headers = {
    expiration: extraInfo.headers.expiration,
    ref_block_num: extraInfo.headers.ref_block_num,
    ref_block_prefix: extraInfo.headers.ref_block_prefix,
    net_usage_words: 0,
    max_cpu_usage_ms: 0,
    delay_sec: 0,
    context_free_actions: [],
    transaction_extensions: []
  }

  const eos = Eos({
    chainId: extraInfo.chain_id,
    keyProvider: [privateKey],
    httpEndpoint: null,
    transactionHeaders: (expireInSeconds, callback) => {
      callback(null/* error */, headers)
    },
    sign: true
  })

  const asset = value.split(' ')[1].split('@')[0]
  const contract = value.split('@')[1]
  eos.fc.abiCache.abi(contract, JSON.parse(ASSET[asset].abi))
  const trx = await eos.contract(contract).then(tokenAccount => tokenAccount.transfer(from, to, value, memo))

  return trx
}

async function buildVoteTx (from, to, privateKey, extraInfo) {
  const headers = {
    expiration: extraInfo.headers.expiration,
    ref_block_num: extraInfo.headers.ref_block_num,
    ref_block_prefix: extraInfo.headers.ref_block_prefix,
    net_usage_words: 0,
    max_cpu_usage_ms: 0,
    delay_sec: 0,
    context_free_actions: [],
    transaction_extensions: []
  }

  const eos = Eos({
    chainId: extraInfo.chain_id,
    keyProvider: [privateKey],
    httpEndpoint: null,
    transactionHeaders: (expireInSeconds, callback) => {
      callback(null/* error */, headers)
    },
    broadcast: false,
    sign: true
  })

  const trx = await eos.voteproducer(from, '', to)

  return trx
}

async function undelegatebw (from, receiver, privateKey, extraInfo) {
  const headers = {
    expiration: extraInfo.headers.expiration,
    ref_block_num: extraInfo.headers.ref_block_num,
    ref_block_prefix: extraInfo.headers.ref_block_prefix,
    net_usage_words: 0,
    max_cpu_usage_ms: 0,
    delay_sec: 0,
    context_free_actions: [],
    transaction_extensions: []
  }

  const eos = Eos({
    chainId: extraInfo.chain_id,
    keyProvider: [privateKey],
    httpEndpoint: null,
    transactionHeaders: (expireInSeconds, callback) => {
      callback(null/* error */, headers)
    },
    sign: true
  })

  const trx = await eos.undelegatebw({
    from,
    receiver,
    unstake_net_quantity: extraInfo.stake_net,
    unstake_cpu_quantity: extraInfo.stake_cpu
  })

  return trx
}

async function delegatebw (from, receiver, privateKey, extraInfo, toBroadcast = false) { // bw: bandwidth
  const headers = {
    expiration: extraInfo.headers.expiration,
    ref_block_num: extraInfo.headers.ref_block_num,
    ref_block_prefix: extraInfo.headers.ref_block_prefix,
    net_usage_words: 0,
    max_cpu_usage_ms: 0,
    delay_sec: 0,
    context_free_actions: [],
    transaction_extensions: []
  }

  const eos = Eos({
    chainId: extraInfo.chain_id,
    keyProvider: [privateKey],
    httpEndpoint: extraInfo.url,
    transactionHeaders: (expireInSeconds, callback) => {
      callback(null/* error */, headers)
    },
    broadcast: toBroadcast,
    sign: true
  })

  const trx = await eos.delegatebw({
    from,
    receiver,
    stake_net_quantity: extraInfo.stake_net,
    stake_cpu_quantity: extraInfo.stake_cpu,
    transfer: extraInfo.transfer || 0
  })

  return trx
}

async function buyrambytes (from, receiver, privateKey, extraInfo) {
  const headers = {
    expiration: extraInfo.headers.expiration,
    ref_block_num: extraInfo.headers.ref_block_num,
    ref_block_prefix: extraInfo.headers.ref_block_prefix,
    net_usage_words: 0,
    max_cpu_usage_ms: 0,
    delay_sec: 0,
    context_free_actions: [],
    transaction_extensions: []
  }

  const eos = Eos({
    chainId: extraInfo.chain_id,
    keyProvider: [privateKey],
    httpEndpoint: null,
    transactionHeaders: (expireInSeconds, callback) => {
      callback(null/* error */, headers)
    },
    sign: true
  })

  const trx = await eos.buyrambytes({
    payer: from,
    receiver,
    bytes: extraInfo.buy_ram_bytes
  })

  return trx
}

async function buyram (from, receiver, privateKey, extraInfo) {
  const headers = {
    expiration: extraInfo.headers.expiration,
    ref_block_num: extraInfo.headers.ref_block_num,
    ref_block_prefix: extraInfo.headers.ref_block_prefix,
    net_usage_words: 0,
    max_cpu_usage_ms: 0,
    delay_sec: 0,
    context_free_actions: [],
    transaction_extensions: []
  }

  const eos = Eos({
    chainId: extraInfo.chain_id,
    keyProvider: [privateKey],
    httpEndpoint: null,
    transactionHeaders: (expireInSeconds, callback) => {
      callback(null/* error */, headers)
    },
    sign: true
  })

  const trx = await eos.buyram({
    payer: from,
    receiver,
    quant: extraInfo.buy_ram
  })

  return trx
}

/**
 获取 chain_id 和构造交易用的 headers
 endpoint 以 http 开头则从网络获取，否则读取本地文件
 */
async function getChainInfo (endpoint) {
  if (!endpoint.startsWith('http')) {
    // 本地有文件存储，直接读取文件。只有连节点才可以更新文件
    const chainInfo = JSON.parse(fs.readFileSync(endpoint))
    return chainInfo
  }

  const config = {
    httpEndpoint: endpoint,
    expireInSeconds: EXPIRE_IN_SECONDS,
    broadcast: false
  }

  const eos = Eos(config)
  const info = await eos.getInfo({})
  const chainDate = new Date(`${info.head_block_time}Z`)

  const block = await eos.getBlock(info.last_irreversible_block_num)
  const expiration = new Date(chainDate.getTime() + config.expireInSeconds * 1000)
  const refBlockNum = info.last_irreversible_block_num & 0xFFFF

  const headers = {
    expiration: expiration.toISOString().split('.')[0],
    ref_block_num: refBlockNum,
    ref_block_prefix: block.ref_block_prefix,
    net_usage_words: 0,
    max_cpu_usage_ms: 0,
    delay_sec: 0,
    context_free_actions: [],
    actions: [],
    signatures: [],
    transaction_extensions: []
  }

  return {
    chain_id: info.chain_id,
    headers: {
      expiration: headers.expiration,
      ref_block_num: refBlockNum,
      ref_block_prefix: block.ref_block_prefix
    }
  }
}

async function getAccount (acctName, url = EOSNODEURL) {
  const config = {
    httpEndpoint: url,
    expireInSeconds: EXPIRE_IN_SECONDS,
    broadcast: false
  }

  const eos = Eos(config)

  return eos.getAccount(acctName)
}

/**
 注册新的账户的交易。交易包含 3 个 action ：newaccount buyrambytes delegatebw
 */
async function newAccount (url, creator, newact, newActPubKey, privateKey, extraInfo, toBroadcast = false) {
  const headers = {
    expiration: extraInfo.headers.expiration,
    ref_block_num: extraInfo.headers.ref_block_num,
    ref_block_prefix: extraInfo.headers.ref_block_prefix,
    net_usage_words: 0,
    max_cpu_usage_ms: 0,
    delay_sec: 0,
    context_free_actions: [],
    transaction_extensions: []
  }

  const eos = Eos({
    chainId: extraInfo.chain_id,
    keyProvider: [privateKey],
    httpEndpoint: url,
    transactionHeaders: (expireInSeconds, callback) => {
      callback(null/* error */, headers)
    },
    sign: true
  })

  return eos.transaction(_eos => {
    _eos.newaccount({
      creator,
      name: newact,
      owner: newActPubKey,
      active: newActPubKey
    })
    _eos.buyrambytes({
      payer: creator,
      receiver: newact,
      bytes: extraInfo.buy_ram_bytes
    })
    _eos.delegatebw({
      from: creator,
      receiver: newact,
      stake_net_quantity: extraInfo.stake_net,
      stake_cpu_quantity: extraInfo.stake_cpu,
      transfer: extraInfo.transfer || 0
    })
  }, { broadcast: toBroadcast })
}

async function updateauth (account, privKey, newPubKey, extraInfo, toBroadcast = false) {
  const headers = {
    expiration: extraInfo.headers.expiration,
    ref_block_num: extraInfo.headers.ref_block_num,
    ref_block_prefix: extraInfo.headers.ref_block_prefix,
    net_usage_words: 0,
    max_cpu_usage_ms: 0,
    delay_sec: 0,
    context_free_actions: [],
    transaction_extensions: []
  }

  const eos = Eos({
    chainId: extraInfo.chain_id,
    keyProvider: [privKey],
    httpEndpoint: extraInfo.url,
    transactionHeaders: (expireInSeconds, callback) => {
      callback(null/* error */, headers)
    },
    sign: true
  })

  return eos.transaction(_eos => {
    _eos.updateauth({
      account,
      permission: 'active',
      auth: newPubKey,
      parent: 'owner'
    }, { authorization: [{ actor: account, permission: 'owner' }] })
    _eos.updateauth({
      account,
      permission: 'owner',
      auth: newPubKey,
      parent: ''
    }, { authorization: [{ actor: account, permission: 'owner' }] })
  }, { broadcast: toBroadcast })
}

async function loadWallet (filePath) {
  const keyList = fs.readFileSync(filePath).toString().trim().split('\n')
  const wallet = new Map()
  for (let line of keyList) {
    line = line.trim()
    if (line) {
      const actInfo = line.split(' ')
      if (actInfo.length < 3) {
        console.error(`${actInfo} miss something, it should include 'pubkey privkey account'`)
        process.exit(1)
      }
      const privKey = actInfo[1]
      wallet.set(actInfo[2], privKey)
    }
  }

  return wallet
}

function toAssetString (num, precision, symbol) {
  const quantity = Eos.modules.format.DecimalPad(num, precision)
  return `${quantity} ${symbol}`
}

async function pointEosWalletFilePath (isCreator = false) {
  const conjunction = isCreator ? 'serve as' : 'different from'
  const defaultPath = isCreator ? EOSWALLETFILEPATH : EOSWALLETFILEPATHWITHDATETIME
  return inquirer.prompt([
    {
      type: 'input',
      name: 'eos_wallet_filepath',
      message: `Plz input a eos wallet filename ${conjunction} creator`,
      default: defaultPath,
      filter: input => {
        let inputFilePath
        if (path.isAbsolute(input)) {
          inputFilePath = input
        } else {
          inputFilePath = path.join(EOSWALLETDIR, input)
        }
        return inputFilePath
      }
    }
  ])
}

async function pointEosKeystoreFilePath (isCreator = false) {
  const conjunction = isCreator ? 'serve as' : 'different from'
  const defaultPath = isCreator ? KEYSTOREPATH : KEYSTOREPATHWITHDATETIME
  return inquirer.prompt([
    {
      type: 'input',
      name: 'eos_keystore_filepath',
      message: `Plz input a eos keystore filename ${conjunction} creator`,
      default: defaultPath,
      filter: input => {
        let inputFilePath
        if (path.isAbsolute(input)) {
          inputFilePath = input
        } else {
          inputFilePath = path.join(EOSWALLETDIR, input)
        }
        return inputFilePath
      }
    }
  ])
}

async function getInactiveAccount () {
  const sql = 'select id, account, public_key from acct where active=false order by id'
  const rows = await model.all(sql)
  if (!Array.isArray(rows) || rows.length === 0) {
    console.error('there is no inactive account')
    return []
  }
  return rows
}

async function pushTransaction (rawTx, nodeUrl = EOSNODEURL) {
  // e.g: curl  'https://jungle4.cryptolions.io:443/v1/chain/push_transaction' -X POST -d '<签名后的交易>'
  const output = await shelljs.exec(`curl '${nodeUrl}/v1/chain/push_transaction' -X POST -d '${rawTx}'`, { silent: true }).stdout
  console.log(output)
}

async function getActiveAndOwnerKey (account) {
  const keyMap = {}
  const { permissions = [] } = await getAccount(account, EOSNODEURL)
  permissions.forEach(permission => {
    keyMap[permission.perm_name] = permission.required_auth.keys[0].key
  })
  return keyMap
}

function checkFilePathExist (inputFilePath, needCreate = true) {
  if (!fs.existsSync(inputFilePath) || !fs.statSync(inputFilePath).isFile()) {
    if (needCreate) {
      fs.createWriteStream(inputFilePath, { autoClose: true })
    } else {
      console.error('file is not exist')
      process.exit(1)
    }
  }
}

function updateAccountToTargetWallet (targetWalletPath, account, publicKey) {
  let keyLines = fs.readFileSync(targetWalletPath).toString().trim().split('\n')
  keyLines = keyLines.map(keyLine => {
    if (keyLine.includes(publicKey)) {
      const arr = keyLine.split(' ')
      arr[arr.length - 1] = account
      keyLine = `${arr.join(' ')}`
    }
    return keyLine
  })

  fs.writeFileSync(targetWalletPath, keyLines.join('\n'))
}

async function genAccountName (nodeUrl = EOSNODEURL) {
  const account = randomstring.generate({
    length: 12,
    charset: '12345abcdefghijklmnopqrstuvwxyz',
    capitalization: 'lowercase'
  })

  const output = await shelljs.exec(`curl '${nodeUrl}/v1/chain/get_account' -X POST -d '{"account_name": "${account}"}'`, { silent: true }).stdout
  if (output.includes('unknown key')) {
    return account
  }

  return genAccountName()
}

function getInputFileLines (inputFilePath) {
  if (!fs.existsSync(inputFilePath) || !fs.statSync(inputFilePath).isFile()) {
    throw new Error('Input file is not exist')
  }
  const res = shelljs.exec(`wc -l ${inputFilePath}`, { silent: true })
  const totalLines = +(res.stdout.trim().split(' ')[0])

  return totalLines
}

function recordToEosWalletDatLine (line, password, separator = ',') {
  // 默认字段顺序 public_key,encrypted_private_key,account
  const [publicKey, encryptPrivKey, account] = line.split(separator)
  const plainPrivKey = Aes.decrypt(encryptPrivKey, password, '')
  const eosWalletDatEncryptedPrivKey = encrypt(encrypt(plainPrivKey, password), password)

  return `${publicKey} ${eosWalletDatEncryptedPrivKey} ${account}\n`
}

function eosWalletDatLineToRecord (line, password, separator = ',') {
  // 默认字段顺序 public_key,encrypted_private_key,account
  const [publicKey, eosWalletDatEncryptedPrivKey, account] = line.split(separator)
  const plainPrivKey = decrypt(decrypt(eosWalletDatEncryptedPrivKey, password), password)
  const encryptPrivKey = Aes.encrypt(plainPrivKey, password, '')

  return `${publicKey},${encryptPrivKey},${account}\n`
}

async function getDataProcessResult (inputFilePath, password, separator = ',', handler = null) {
  const totalLines = getInputFileLines(inputFilePath)
  const bar = new ProgressBar('Verifing signature [:bar] :current/:total ', { total: totalLines })

  return new Promise((resolve, reject) => {
    const fRead = fs.createReadStream(inputFilePath)
    const fReadline = readline.createInterface({
      input: fRead,
      terminal: true
    })
    let writeLines = ''
    fReadline.on('line', line => {
      bar.tick()
      writeLines += handler(line, password, separator)
    })
    fRead.on('end', () => {
      fRead.close()
      fReadline.close()
    })
    fReadline.on('close', () => {
      resolve({ writeLines })
    })
    fReadline.on('error', err => {
      reject(err)
    })
  })
}

async function buildRefundTx (from, privateKey, extraInfo) {
  const headers = {
    expiration: extraInfo.headers.expiration,
    ref_block_num: extraInfo.headers.ref_block_num,
    ref_block_prefix: extraInfo.headers.ref_block_prefix,
    net_usage_words: 0,
    max_cpu_usage_ms: 0,
    delay_sec: 0,
    context_free_actions: [],
    transaction_extensions: []
  }

  const eos = Eos({
    chainId: extraInfo.chain_id,
    keyProvider: [privateKey],
    httpEndpoint: null,
    transactionHeaders: (expireInSeconds, callback) => {
      callback(null/* error */, headers)
    },
    broadcast: false,
    sign: true
  })

  const trx = await eos.refund(from)

  return trx
}

export {
  chooseTool,
  transfer,
  buildVoteTx,
  undelegatebw,
  delegatebw,
  buyrambytes,
  buyram,
  getChainInfo,
  getAccount,
  newAccount,
  updateauth,
  loadWallet,
  toAssetString,
  pointEosWalletFilePath,
  pointEosKeystoreFilePath,
  getInactiveAccount,
  pushTransaction,
  checkFilePathExist,
  updateAccountToTargetWallet,
  genAccountName,
  getActiveAndOwnerKey,
  getInputFileLines,
  recordToEosWalletDatLine,
  eosWalletDatLineToRecord,
  getDataProcessResult,
  buildRefundTx
}
