import fs from 'fs'
import sqlite3 from 'sqlite3'
import Sequelize from 'sequelize'

const logging = process.env.LOG === 'true'

export class Model {
  constructor (option) {
    const { dbPath, dbName, tableName, timestamps = false } = option
    this.dbPath = dbPath
    this.dbName = dbName
    this.modelName = tableName
    this.timestamps = timestamps
    this.filename = `${this.dbPath}/${this.dbName}.db`
    this.init()
  }

  init () {
    // if db path not exist, auto create db file
    if (!fs.existsSync(this.dbPath)) {
      fs.mkdirSync(this.dbPath, { recursive: true })
    }
    // init db
    this.db = new sqlite3.Database(this.filename)
    this.sequelize = new Sequelize({
      dialect: 'sqlite',
      storage: `${this.filename}`,
      logging
    })
    // init model
    this.model = this.sequelize.define(
      this.modelName,
      {
        account: {
          type: Sequelize.STRING,
          unique: true
        },
        public_key: {
          type: Sequelize.TEXT,
          allowNull: false,
          defaultValue: ''
        },
        active: {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: false
        }
      },
      {
        timestamps: this.timestamps,
        freezeTableName: true,
        tableName: this.modelName
      }
    )
    this.model.sync({ force: false })
  }

  async create (values) {
    const res = await this.model.create(values)
    return res
  }

  async bulkCreate (list) {
    return this.model.bulkCreate(list)
  }

  async count () {
    return this.model.count()
  }

  async findAll (option) {
    return this.model.findAll(option)
  }

  async findOne (option) {
    return this.model.findOne(option)
  }

  async update (values, option) {
    const res = await this.model.update(values, option)
    return res
  }

  async upsert (values) {
    const res = await this.model.upsert(values)
    return res
  }

  async delete (condition) {
    return this.model.destroy({ where: condition })
  }

  async all (sql) {
    return new Promise((resolve, reject) => {
      this.db.all(sql, (err, table) => {
        if (err) {
          reject(err)
        }
        resolve(table)
      })
    })
  }
}
