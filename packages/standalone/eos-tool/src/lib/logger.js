import bunyan from 'bunyan'

export const log = bunyan.createLogger({
  name: 'eos-tool-operation',
  streams: [
    {
      level: 'debug',
      stream: process.stdout // log DEBUG and above to stdout
    },
    {
      level: 'info',
      type: 'rotating-file',
      path: 'logs/eos-tool-operation.log', // logs是启动目录cwd下的dir
      period: '7d',
      count: 10
    }
  ],
  src: true // set false in product, as it impact performance
})
