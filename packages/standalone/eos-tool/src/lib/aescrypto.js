import crypto from 'crypto'

export function encrypt (data, passwd) {
  // eslint-disable-next-line n/no-deprecated-api
  const cipher = crypto.createCipher('aes-128-ecb', passwd)
  cipher.setAutoPadding(true)

  let encrypted = cipher.update(data, 'utf8', 'base64')
  encrypted += cipher.final('base64')
  return encrypted
}

export function decrypt (data, passwd) {
  if (!data) {
    return null
  }

  // eslint-disable-next-line n/no-deprecated-api
  const cipher = crypto.createDecipher('aes-128-ecb', passwd)
  cipher.setAutoPadding(true)

  let decrypted = cipher.update(data, 'base64', 'utf8')
  decrypted += cipher.final('utf8')
  return decrypted
}
