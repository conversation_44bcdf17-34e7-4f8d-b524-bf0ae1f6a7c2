#!/usr/bin/env node
import fs from 'fs'
import path from 'path'
import { dirname } from 'node:path'
import { fileURLToPath } from 'node:url'
import { chooseTool } from './lib/utils.js'

(async () => {
  const eosdbDir = 'eosdb'
  const pwd = process.cwd()
  if (!fs.existsSync(path.resolve(pwd, eosdbDir))) {
    fs.mkdirSync(path.resolve(pwd, eosdbDir), { recursive: true })
  }

  const logsDir = 'logs'
  if (!fs.existsSync(path.resolve(pwd, logsDir))) {
    fs.mkdirSync(path.resolve(pwd, logsDir), { recursive: true })
  }

  const __dirname = dirname(fileURLToPath(import.meta.url))
  const toolList = fs.readdirSync(path.join(__dirname, 'tools')).map(file => path.parse(file).name)
  const { tool } = await chooseTool('tool', 'Plz choose which tool need to be executed?', toolList)
  const { main } = await import(`./tools/${tool}.js`)
  await main()

  process.exit(0)
})()
