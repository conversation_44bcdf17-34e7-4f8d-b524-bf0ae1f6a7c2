import path from 'path'
import { Model } from '../lib/model.js'

export const ASSET = {
  EOS: {
    precision: 4,
    symbol: '<EMAIL>',
    abi: '{"version": "eosio::abi/1.0", "types": [{"new_type_name": "account_name", "type": "name"}], "structs": [{"name": "transfer", "base": "", "fields": [{"name": "from", "type": "account_name"}, {"name": "to", "type": "account_name"}, {"name": "quantity", "type": "asset"}, {"name": "memo", "type": "string"}]}, {"name": "create", "base": "", "fields": [{"name": "issuer", "type": "account_name"}, {"name": "maximum_supply", "type": "asset"}]}, {"name": "issue", "base": "", "fields": [{"name": "to", "type": "account_name"}, {"name": "quantity", "type": "asset"}, {"name": "memo", "type": "string"}]}, {"name": "retire", "base": "", "fields": [{"name": "quantity", "type": "asset"}, {"name": "memo", "type": "string"}]}, {"name": "close", "base": "", "fields": [{"name": "owner", "type": "account_name"}, {"name": "symbol", "type": "symbol"}]}, {"name": "account", "base": "", "fields": [{"name": "balance", "type": "asset"}]}, {"name": "currency_stats", "base": "", "fields": [{"name": "supply", "type": "asset"}, {"name": "max_supply", "type": "asset"}, {"name": "issuer", "type": "account_name"}]}], "actions": [{"name": "transfer", "type": "transfer", "ricardian_contract": "## Transfer Terms & Conditions\\n\\nI, {{from}}, certify the following to be true to the best of my knowledge:\\n\\n1. I certify that {{quantity}} is not the proceeds of fraudulent or violent activities.\\n2. I certify that, to the best of my knowledge, {{to}} is not supporting initiation of violence against others.\\n3. I have disclosed any contractual terms & conditions with respect to {{quantity}} to {{to}}.\\n\\nI understand that funds transfers are not reversible after the {{transaction.delay}} seconds or other delay as configured by {{from}}\'s permissions.\\n\\nIf this action fails to be irreversibly confirmed after receiving goods or services from \'{{to}}\', I agree to either return the goods or services or resend {{quantity}} in a timely manner.\\n"}, {"name": "issue", "type": "issue", "ricardian_contract": ""}, {"name": "create", "type": "create", "ricardian_contract": ""}, {"name": "retire", "type": "retire", "ricardian_contract": ""}, {"name": "close", "type": "close", "ricardian_contract": ""}], "tables": [{"name": "accounts", "index_type": "i64", "key_names": ["currency"], "key_types": ["uint64"], "type": "account"}, {"name": "stat", "index_type": "i64", "key_names": ["currency"], "key_types": ["uint64"], "type": "currency_stats"}], "ricardian_clauses": [], "error_messages": [], "abi_extensions": []}'
  },
  IQ: {
    precision: 3,
    symbol: 'IQ@everipediaiq',
    abi: '{"version":"eosio::abi/1.0","types":[{"new_type_name":"account_name","type":"name"}],"structs":[{"name":"transfer","base":"","fields":[{"name":"from","type":"account_name"},{"name":"to","type":"account_name"},{"name":"quantity","type":"asset"},{"name":"memo","type":"string"}]},{"name":"create","base":"","fields":[{"name":"issuer","type":"account_name"},{"name":"maximum_supply","type":"asset"}]},{"name":"issue","base":"","fields":[{"name":"to","type":"account_name"},{"name":"quantity","type":"asset"},{"name":"memo","type":"string"}]},{"name":"brainmeiq","base":"","fields":[{"name":"staker","type":"account_name"},{"name":"amount","type":"int64"}]},{"name":"account","base":"","fields":[{"name":"balance","type":"asset"}]},{"name":"currency_stats","base":"","fields":[{"name":"supply","type":"asset"},{"name":"max_supply","type":"asset"},{"name":"issuer","type":"account_name"}]}],"actions":[{"name":"transfer","type":"transfer","ricardian_contract":""},{"name":"issue","type":"issue","ricardian_contract":""},{"name":"brainmeiq","type":"brainmeiq","ricardian_contract":""},{"name":"create","type":"create","ricardian_contract":""}],"tables":[{"name":"accounts","index_type":"i64","key_names":["currency"],"key_types":["uint64"],"type":"account"},{"name":"stat","index_type":"i64","key_names":["currency"],"key_types":["uint64"],"type":"currency_stats"}],"ricardian_clauses":[],"error_messages":[],"abi_extensions":[]}'
  },
  ADD: {
    precision: 4,
    symbol: 'ADD@eosadddddddd',
    abi: '{"version": "eosio::abi/1.0", "types": [{"new_type_name": "account_name", "type": "name"}], "structs": [{"name": "transfer", "base": "", "fields": [{"name": "from", "type": "account_name"}, {"name": "to", "type": "account_name"}, {"name": "quantity", "type": "asset"}, {"name": "memo", "type": "string"}]}, {"name": "create", "base": "", "fields": [{"name": "issuer", "type": "account_name"}, {"name": "maximum_supply", "type": "asset"}]}, {"name": "issue", "base": "", "fields": [{"name": "to", "type": "account_name"}, {"name": "quantity", "type": "asset"}, {"name": "memo", "type": "string"}]}, {"name": "account", "base": "", "fields": [{"name": "balance", "type": "asset"}]}, {"name": "currency_stats", "base": "", "fields": [{"name": "supply", "type": "asset"}, {"name": "max_supply", "type": "asset"}, {"name": "issuer", "type": "account_name"}]}], "actions": [{"name": "transfer", "type": "transfer", "ricardian_contract": ""}, {"name": "issue", "type": "issue", "ricardian_contract": ""}, {"name": "create", "type": "create", "ricardian_contract": ""}], "tables": [{"name": "accounts", "index_type": "i64", "key_names": ["currency"], "key_types": ["uint64"], "type": "account"}, {"name": "stat", "index_type": "i64", "key_names": ["currency"], "key_types": ["uint64"], "type": "currency_stats"}], "ricardian_clauses": [], "error_messages": [], "abi_extensions": []}'
  }
}
export const EOS_SALE_CONTRACT_ADDR = '0xd0a6e6c54dbc68db5db3a091b171a77407ff7ccf'
export const EOS_SALE_ABI = [{ constant: true, inputs: [{ name: '', type: 'uint256' }, { name: '', type: 'address' }], name: 'claimed', outputs: [{ name: '', type: 'bool' }], payable: false, type: 'function' }, { constant: false, inputs: [{ name: 'owner_', type: 'address' }], name: 'setOwner', outputs: [], payable: false, type: 'function' }, { constant: true, inputs: [], name: 'time', outputs: [{ name: '', type: 'uint256' }], payable: false, type: 'function' }, { constant: true, inputs: [], name: 'totalSupply', outputs: [{ name: '', type: 'uint128' }], payable: false, type: 'function' }, { constant: true, inputs: [], name: 'foundersAllocation', outputs: [{ name: '', type: 'uint128' }], payable: false, type: 'function' }, { constant: false, inputs: [{ name: 'day', type: 'uint256' }], name: 'claim', outputs: [], payable: false, type: 'function' }, { constant: true, inputs: [], name: 'foundersKey', outputs: [{ name: '', type: 'string' }], payable: false, type: 'function' }, { constant: true, inputs: [{ name: '', type: 'uint256' }, { name: '', type: 'address' }], name: 'userBuys', outputs: [{ name: '', type: 'uint256' }], payable: false, type: 'function' }, { constant: true, inputs: [{ name: 'day', type: 'uint256' }], name: 'createOnDay', outputs: [{ name: '', type: 'uint256' }], payable: false, type: 'function' }, { constant: false, inputs: [], name: 'freeze', outputs: [], payable: false, type: 'function' }, { constant: true, inputs: [{ name: '', type: 'address' }], name: 'keys', outputs: [{ name: '', type: 'string' }], payable: false, type: 'function' }, { constant: true, inputs: [], name: 'startTime', outputs: [{ name: '', type: 'uint256' }], payable: false, type: 'function' }, { constant: false, inputs: [{ name: 'authority_', type: 'address' }], name: 'setAuthority', outputs: [], payable: false, type: 'function' }, { constant: true, inputs: [{ name: '', type: 'uint256' }], name: 'dailyTotals', outputs: [{ name: '', type: 'uint256' }], payable: false, type: 'function' }, { constant: true, inputs: [], name: 'owner', outputs: [{ name: '', type: 'address' }], payable: false, type: 'function' }, { constant: false, inputs: [], name: 'buy', outputs: [], payable: true, type: 'function' }, { constant: true, inputs: [], name: 'openTime', outputs: [{ name: '', type: 'uint256' }], payable: false, type: 'function' }, { constant: true, inputs: [], name: 'EOS', outputs: [{ name: '', type: 'address' }], payable: false, type: 'function' }, { constant: true, inputs: [], name: 'today', outputs: [{ name: '', type: 'uint256' }], payable: false, type: 'function' }, { constant: true, inputs: [], name: 'authority', outputs: [{ name: '', type: 'address' }], payable: false, type: 'function' }, { constant: false, inputs: [{ name: 'eos', type: 'address' }], name: 'initialize', outputs: [], payable: false, type: 'function' }, { constant: true, inputs: [], name: 'createFirstDay', outputs: [{ name: '', type: 'uint256' }], payable: false, type: 'function' }, { constant: false, inputs: [], name: 'claimAll', outputs: [], payable: false, type: 'function' }, { constant: true, inputs: [{ name: 'timestamp', type: 'uint256' }], name: 'dayFor', outputs: [{ name: '', type: 'uint256' }], payable: false, type: 'function' }, { constant: false, inputs: [{ name: 'day', type: 'uint256' }, { name: 'limit', type: 'uint256' }], name: 'buyWithLimit', outputs: [], payable: true, type: 'function' }, { constant: false, inputs: [], name: 'collect', outputs: [], payable: false, type: 'function' }, { constant: true, inputs: [], name: 'numberOfDays', outputs: [{ name: '', type: 'uint256' }], payable: false, type: 'function' }, { constant: false, inputs: [{ name: 'key', type: 'string' }], name: 'register', outputs: [], payable: false, type: 'function' }, { constant: true, inputs: [], name: 'createPerDay', outputs: [{ name: '', type: 'uint256' }], payable: false, type: 'function' }, { inputs: [{ name: '_numberOfDays', type: 'uint256' }, { name: '_totalSupply', type: 'uint128' }, { name: '_openTime', type: 'uint256' }, { name: '_startTime', type: 'uint256' }, { name: '_foundersAllocation', type: 'uint128' }, { name: '_foundersKey', type: 'string' }], payable: false, type: 'constructor' }, { payable: true, type: 'fallback' }, { anonymous: false, inputs: [{ indexed: false, name: 'window', type: 'uint256' }, { indexed: false, name: 'user', type: 'address' }, { indexed: false, name: 'amount', type: 'uint256' }], name: 'LogBuy', type: 'event' }, { anonymous: false, inputs: [{ indexed: false, name: 'window', type: 'uint256' }, { indexed: false, name: 'user', type: 'address' }, { indexed: false, name: 'amount', type: 'uint256' }], name: 'LogClaim', type: 'event' }, { anonymous: false, inputs: [{ indexed: false, name: 'user', type: 'address' }, { indexed: false, name: 'key', type: 'string' }], name: 'LogRegister', type: 'event' }, { anonymous: false, inputs: [{ indexed: false, name: 'amount', type: 'uint256' }], name: 'LogCollect', type: 'event' }, { anonymous: false, inputs: [], name: 'LogFreeze', type: 'event' }, { anonymous: false, inputs: [{ indexed: true, name: 'authority', type: 'address' }], name: 'LogSetAuthority', type: 'event' }, { anonymous: false, inputs: [{ indexed: true, name: 'owner', type: 'address' }], name: 'LogSetOwner', type: 'event' }]
export const ETH_NODE_URL = 'http://qukuai-eth-1a-3.aws-jp1.huobiidc.com:8111'
export const producers = ['eoscannonchn', 'bp2bp2bp2bp2', 'eosbeijingbp', 'eosstorebest', 'eosliquideos', 'zbeosbp11111', 'eosfishrocks', 'eosiosg11111']

const pwd = process.cwd()
const eosdbDir = 'eosdb'
const DBCONFIG = {
  dbPath: path.resolve(pwd, eosdbDir),
  dbName: 'eos',
  tableName: 'acct'
}
export const model = new Model(DBCONFIG)
export const EOSWALLETDIR = path.resolve(pwd, eosdbDir)
export const KEYSTOREPATH = path.resolve(pwd, eosdbDir, 'keystore.json')
export const EOSWALLETFILEPATH = path.resolve(pwd, eosdbDir, 'eos_wallet.dat')
export const KEYSTOREPATHWITHDATETIME = path.resolve(pwd, eosdbDir, `keystore_${new Date().toISOString()}.json`)
export const EOSWALLETFILEPATHWITHDATETIME = path.resolve(pwd, eosdbDir, `eos_wallet_${new Date().toISOString()}.dat`)

const logsDir = 'logs'
export const EOSRAWTXSPATH = path.resolve(pwd, logsDir, `EOS--RAWTXS--${new Date().toISOString()}.txt`)
export const ETHRAWTXSPATH = path.resolve(pwd, logsDir, `ETH--RAWTXS--${new Date().toISOString()}.txt`)
export const CHAININFOPATH = path.resolve(pwd, logsDir, 'chain_info.json')

export const EOSNODEURL = 'https://jungle4.cryptolions.io:443'
export const EXPIRE_IN_SECONDS = 3000
