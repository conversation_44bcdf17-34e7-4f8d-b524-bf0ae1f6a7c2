// https://github.com/paritytech/txwrapper-core/blob/2e906b3bf84a30c913251f59f0bb58b4b21b2c91/packages/txwrapper-core/src/core/decode/decodeSignedTx.ts
import { hexToU8a } from '@polkadot/util'
import { toTxMethod, createMetadata } from '@substrate/txwrapper-polkadot'

export function decodeSignedTx (signedTx, options) {
  const { metadataRpc, registry, asCallsOnlyArg, asSpecifiedCallsOnlyV14, signedExtensions, userExtensions } = options // 新增 signedExtensions, userExtensions

  registry.setMetadata(
    createMetadata(
      registry,
      metadataRpc,
      asCallsOnlyArg,
      asSpecifiedCallsOnlyV14
    ),
    signedExtensions, // 新增
    userExtensions // 新增
  )

  const tx = registry.createType('Extrinsic', hexToU8a(signedTx), {
    isSigned: true
  })
  const methodCall = registry.createType('Call', tx.method)
  const method = toTxMethod(registry, methodCall)

  let tip
  try {
    tip = tx.tip.toNumber()
  } catch (_error) {
    tip = tx.tip.toString()
  }

  return {
    address: tx.signer.toString(),
    eraPeriod: tx.era.asMortalEra.period.toNumber(),
    metadataRpc,
    method,
    nonce: tx.nonce.toNumber(),
    tip
  }
}
