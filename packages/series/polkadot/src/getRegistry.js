import { createMetadata, getRegistryBase } from '@substrate/txwrapper-polkadot'

const registry = {}

export function getRegistry (options) {
  const { chainName, specVersion } = options

  const key = `${chainName}-${specVersion}`

  if (registry[key]) {
    return registry[key]
  }

  // when node upgrade, registry + metadata is new, then clear memory metadata from sdk
  // and add new registry(along with metadata, it's a type, so small) to memory from code.
  createMetadata.clear()

  registry[key] = getRegistryBase(options)

  return registry[key]
}
