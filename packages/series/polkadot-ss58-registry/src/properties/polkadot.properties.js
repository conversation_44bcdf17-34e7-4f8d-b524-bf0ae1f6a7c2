export const CHAIN_PROPERTIES = {
  DOT: {
    ss58Format: 0,
    tokenDecimals: 10,
    tokenSymbol: 'DOT'
  },
  KSM: {
    ss58Format: 2,
    tokenDecimals: 12,
    tokenSymbol: 'KSM'
  },
  ASTR: {
    ss58Format: 5,
    tokenDecimals: 18,
    tokenSymbol: 'ASTR'
  },
  BNC: {
    ss58Format: 6,
    tokenDecimals: 12,
    tokenSymbol: 'BNC'
  },
  EDG: {
    ss58Format: 7,
    tokenDecimals: 18,
    tokenSymbol: 'EDG'
  },
  KAR: {
    ss58Format: 8,
    tokenDecimals: 12,
    tokenSymbol: 'KAR'
  },
  REY: {
    ss58Format: 9,
    tokenDecimals: 18,
    tokenSymbol: 'REY'
  },
  ACA: {
    ss58Format: 10,
    tokenDecimals: 12,
    tokenSymbol: 'ACA'
  },
  LAMI: {
    ss58Format: 11,
    tokenDecimals: 18,
    tokenSymbol: 'LAMI'
  },
  POLYX: {
    ss58Format: 12,
    tokenDecimals: 6,
    tokenSymbol: 'POLYX',
    types: {
      Address: 'MultiAddress',
      LookupSource: 'MultiAddress',
      AccountInfo: 'AccountInfoWithDualRefCount',
      IdentityId: '[u8; 32]',
      EventDid: 'IdentityId',
      EventCounts: 'Vec<u32>',
      ErrorAt: '(u32, DispatchError)',
      InvestorUid: '[u8; 16]',
      Ticker: '[u8; 12]',
      CddId: '[u8; 32]',
      ScopeId: '[u8; 32]',
      PosRatio: '(u32, u32)',
      DocumentId: 'u32',
      DocumentName: 'Text',
      DocumentUri: 'Text',
      DocumentHash: {
        _enum: {
          None: '',
          H512: '[u8; 64]',
          H384: '[u8; 48]',
          H320: '[u8; 40]',
          H256: '[u8; 32]',
          H224: '[u8; 28]',
          H192: '[u8; 24]',
          H160: '[u8; 20]',
          H128: '[u8; 16]'
        }
      },
      DocumentType: 'Text',
      Document: {
        uri: 'DocumentUri',
        content_hash: 'DocumentHash',
        name: 'DocumentName',
        doc_type: 'Option<DocumentType>',
        filing_date: 'Option<Moment>'
      },
      Version: 'u8',
      CustomAssetTypeId: 'u32',
      AssetType: {
        _enum: {
          EquityCommon: '',
          EquityPreferred: '',
          Commodity: '',
          FixedIncome: '',
          REIT: '',
          Fund: '',
          RevenueShareAgreement: '',
          StructuredProduct: '',
          Derivative: '',
          Custom: 'CustomAssetTypeId',
          StableCoin: ''
        }
      },
      AssetIdentifier: {
        _enum: {
          CUSIP: '[u8; 9]',
          CINS: '[u8; 9]',
          ISIN: '[u8; 12]',
          LEI: '[u8; 20]'
        }
      },
      AssetOwnershipRelation: {
        _enum: {
          NotOwned: '',
          TickerOwned: '',
          AssetOwned: ''
        }
      },
      AssetName: 'Text',
      FundingRoundName: 'Text',
      VenueDetails: 'Text',
      SecurityToken: {
        total_supply: 'Balance',
        owner_did: 'IdentityId',
        divisible: 'bool',
        asset_type: 'AssetType'
      },
      PalletName: 'Text',
      DispatchableName: 'Text',
      AssetPermissions: {
        _enum: {
          Whole: '',
          These: 'Vec<Ticker>',
          Except: 'Vec<Ticker>'
        }
      },
      PortfolioPermissions: {
        _enum: {
          Whole: '',
          These: 'Vec<PortfolioId>',
          Except: 'Vec<PortfolioId>'
        }
      },
      DispatchableNames: {
        _enum: {
          Whole: '',
          These: 'Vec<DispatchableName>',
          Except: 'Vec<DispatchableName>'
        }
      },
      PalletPermissions: {
        pallet_name: 'PalletName',
        dispatchable_names: 'DispatchableNames'
      },
      ExtrinsicPermissions: {
        _enum: {
          Whole: '',
          These: 'Vec<PalletPermissions>',
          Except: 'Vec<PalletPermissions>'
        }
      },
      Permissions: {
        asset: 'AssetPermissions',
        extrinsic: 'ExtrinsicPermissions',
        portfolio: 'PortfolioPermissions'
      },
      LegacyPalletPermissions: {
        pallet_name: 'PalletName',
        total: 'bool',
        dispatchable_names: 'Vec<DispatchableName>'
      },
      LegacyPermissions: {
        asset: 'Option<Vec<Ticker>>',
        extrinsic: 'Option<Vec<LegacyPalletPermissions>>',
        portfolio: 'Option<Vec<PortfolioId>>'
      },
      Signatory: {
        _enum: {
          Identity: 'IdentityId',
          Account: 'AccountId'
        }
      },
      SecondaryKey: {
        signer: 'Signatory',
        permissions: 'Permissions'
      },
      SecondaryKeyWithAuth: {
        secondary_key: 'SecondaryKey',
        auth_signature: 'Signature'
      },
      Subsidy: {
        paying_key: 'AccountId',
        remaining: 'Balance'
      },
      IdentityRole: {
        _enum: [
          'Issuer',
          'SimpleTokenIssuer',
          'Validator',
          'ClaimIssuer',
          'Investor',
          'NodeRunner',
          'PM',
          'CDDAMLClaimIssuer',
          'AccreditedInvestorClaimIssuer',
          'VerifiedIdentityClaimIssuer'
        ]
      },
      PreAuthorizedKeyInfo: {
        target_id: 'IdentityId',
        secondary_key: 'SecondaryKey'
      },
      DidRecord: {
        primary_key: 'AccountId',
        secondary_keys: 'Vec<SecondaryKey>'
      },
      KeyIdentityData: {
        identity: 'IdentityId',
        permissions: 'Option<Permissions>'
      },
      CountryCode: {
        _enum: [
          'AF',
          'AX',
          'AL',
          'DZ',
          'AS',
          'AD',
          'AO',
          'AI',
          'AQ',
          'AG',
          'AR',
          'AM',
          'AW',
          'AU',
          'AT',
          'AZ',
          'BS',
          'BH',
          'BD',
          'BB',
          'BY',
          'BE',
          'BZ',
          'BJ',
          'BM',
          'BT',
          'BO',
          'BA',
          'BW',
          'BV',
          'BR',
          'VG',
          'IO',
          'BN',
          'BG',
          'BF',
          'BI',
          'KH',
          'CM',
          'CA',
          'CV',
          'KY',
          'CF',
          'TD',
          'CL',
          'CN',
          'HK',
          'MO',
          'CX',
          'CC',
          'CO',
          'KM',
          'CG',
          'CD',
          'CK',
          'CR',
          'CI',
          'HR',
          'CU',
          'CY',
          'CZ',
          'DK',
          'DJ',
          'DM',
          'DO',
          'EC',
          'EG',
          'SV',
          'GQ',
          'ER',
          'EE',
          'ET',
          'FK',
          'FO',
          'FJ',
          'FI',
          'FR',
          'GF',
          'PF',
          'TF',
          'GA',
          'GM',
          'GE',
          'DE',
          'GH',
          'GI',
          'GR',
          'GL',
          'GD',
          'GP',
          'GU',
          'GT',
          'GG',
          'GN',
          'GW',
          'GY',
          'HT',
          'HM',
          'VA',
          'HN',
          'HU',
          'IS',
          'IN',
          'ID',
          'IR',
          'IQ',
          'IE',
          'IM',
          'IL',
          'IT',
          'JM',
          'JP',
          'JE',
          'JO',
          'KZ',
          'KE',
          'KI',
          'KP',
          'KR',
          'KW',
          'KG',
          'LA',
          'LV',
          'LB',
          'LS',
          'LR',
          'LY',
          'LI',
          'LT',
          'LU',
          'MK',
          'MG',
          'MW',
          'MY',
          'MV',
          'ML',
          'MT',
          'MH',
          'MQ',
          'MR',
          'MU',
          'YT',
          'MX',
          'FM',
          'MD',
          'MC',
          'MN',
          'ME',
          'MS',
          'MA',
          'MZ',
          'MM',
          'NA',
          'NR',
          'NP',
          'NL',
          'AN',
          'NC',
          'NZ',
          'NI',
          'NE',
          'NG',
          'NU',
          'NF',
          'MP',
          'NO',
          'OM',
          'PK',
          'PW',
          'PS',
          'PA',
          'PG',
          'PY',
          'PE',
          'PH',
          'PN',
          'PL',
          'PT',
          'PR',
          'QA',
          'RE',
          'RO',
          'RU',
          'RW',
          'BL',
          'SH',
          'KN',
          'LC',
          'MF',
          'PM',
          'VC',
          'WS',
          'SM',
          'ST',
          'SA',
          'SN',
          'RS',
          'SC',
          'SL',
          'SG',
          'SK',
          'SI',
          'SB',
          'SO',
          'ZA',
          'GS',
          'SS',
          'ES',
          'LK',
          'SD',
          'SR',
          'SJ',
          'SZ',
          'SE',
          'CH',
          'SY',
          'TW',
          'TJ',
          'TZ',
          'TH',
          'TL',
          'TG',
          'TK',
          'TO',
          'TT',
          'TN',
          'TR',
          'TM',
          'TC',
          'TV',
          'UG',
          'UA',
          'AE',
          'GB',
          'US',
          'UM',
          'UY',
          'UZ',
          'VU',
          'VE',
          'VN',
          'VI',
          'WF',
          'EH',
          'YE',
          'ZM',
          'ZW',
          'BQ',
          'CW',
          'SX'
        ]
      },
      Scope: {
        _enum: {
          Identity: 'IdentityId',
          Ticker: 'Ticker',
          Custom: 'Vec<u8>'
        }
      },
      InvestorZKProofData: 'Signature',
      Scalar: '[u8; 32]',
      RistrettoPoint: '[u8; 32]',
      ZkProofData: {
        challenge_responses: '[Scalar; 2]',
        subtract_expressions_res: 'RistrettoPoint',
        blinded_scope_did_hash: 'RistrettoPoint'
      },
      ScopeClaimProof: {
        proof_scope_id_wellformed: 'Signature',
        proof_scope_id_cdd_id_match: 'ZkProofData',
        scope_id: 'RistrettoPoint'
      },
      Claim: {
        _enum: {
          Accredited: 'Scope',
          Affiliate: 'Scope',
          BuyLockup: 'Scope',
          SellLockup: 'Scope',
          CustomerDueDiligence: 'CddId',
          KnowYourCustomer: 'Scope',
          Jurisdiction: '(CountryCode, Scope)',
          Exempted: 'Scope',
          Blocked: 'Scope',
          InvestorUniqueness: '(Scope, ScopeId, CddId)',
          NoData: '',
          InvestorUniquenessV2: '(CddId)'
        }
      },
      ClaimType: {
        _enum: {
          Accredited: '',
          Affiliate: '',
          BuyLockup: '',
          SellLockup: '',
          CustomerDueDiligence: '',
          KnowYourCustomer: '',
          Jurisdiction: '',
          Exempted: '',
          Blocked: '',
          InvestorUniqueness: '',
          NoData: '',
          InvestorUniquenessV2: ''
        }
      },
      IdentityClaim: {
        claim_issuer: 'IdentityId',
        issuance_date: 'Moment',
        last_update_date: 'Moment',
        expiry: 'Option<Moment>',
        claim: 'Claim'
      },
      ComplianceRequirement: {
        sender_conditions: 'Vec<Condition>',
        receiver_conditions: 'Vec<Condition>',
        id: 'u32'
      },
      ComplianceRequirementResult: {
        sender_conditions: 'Vec<ConditionResult>',
        receiver_conditions: 'Vec<ConditionResult>',
        id: 'u32',
        result: 'bool'
      },
      ConditionType: {
        _enum: {
          IsPresent: 'Claim',
          IsAbsent: 'Claim',
          IsAnyOf: 'Vec<Claim>',
          IsNoneOf: 'Vec<Claim>',
          IsIdentity: 'TargetIdentity'
        }
      },
      TrustedFor: {
        _enum: {
          Any: '',
          Specific: 'Vec<ClaimType>'
        }
      },
      TrustedIssuer: {
        issuer: 'IdentityId',
        trusted_for: 'TrustedFor'
      },
      Condition: {
        condition_type: 'ConditionType',
        issuers: 'Vec<TrustedIssuer>'
      },
      ConditionResult: {
        condition: 'Condition',
        result: 'bool'
      },
      TargetIdAuthorization: {
        target_id: 'IdentityId',
        nonce: 'u64',
        expires_at: 'Moment'
      },
      TickerRegistration: {
        owner: 'IdentityId',
        expiry: 'Option<Moment>'
      },
      TickerRegistrationConfig: {
        max_ticker_length: 'u8',
        registration_length: 'Option<Moment>'
      },
      ClassicTickerRegistration: {
        eth_owner: 'EthereumAddress',
        is_created: 'bool'
      },
      ClassicTickerImport: {
        eth_owner: 'EthereumAddress',
        ticker: 'Ticker',
        is_contract: 'bool',
        is_created: 'bool'
      },
      EthereumAddress: '[u8; 20]',
      EcdsaSignature: '[u8; 65]',
      MotionTitle: 'Text',
      MotionInfoLink: 'Text',
      ChoiceTitle: 'Text',
      Motion: {
        title: 'MotionTitle',
        info_link: 'MotionInfoLink',
        choices: 'Vec<ChoiceTitle>'
      },
      BallotTitle: 'Text',
      BallotMeta: {
        title: 'BallotTitle',
        motions: 'Vec<Motion>'
      },
      BallotTimeRange: {
        start: 'Moment',
        end: 'Moment'
      },
      BallotVote: {
        power: 'Balance',
        fallback: 'Option<u16>'
      },
      MaybeBlock: {
        _enum: {
          Some: 'BlockNumber',
          None: ''
        }
      },
      Url: 'Text',
      PipDescription: 'Text',
      PipsMetadata: {
        id: 'PipId',
        url: 'Option<Url>',
        description: 'Option<PipDescription>',
        created_at: 'BlockNumber',
        transaction_version: 'u32',
        expiry: 'MaybeBlock'
      },
      Proposer: {
        _enum: {
          Community: 'AccountId',
          Committee: 'Committee'
        }
      },
      Committee: {
        _enum: {
          Technical: '',
          Upgrade: ''
        }
      },
      SkippedCount: 'u8',
      SnapshottedPip: {
        id: 'PipId',
        weight: '(bool, Balance)'
      },
      SnapshotId: 'u32',
      SnapshotMetadata: {
        created_at: 'BlockNumber',
        made_by: 'AccountId',
        id: 'SnapshotId'
      },
      SnapshotResult: {
        _enum: {
          Approve: '',
          Reject: '',
          Skip: ''
        }
      },
      Beneficiary: {
        id: 'IdentityId',
        amount: 'Balance'
      },
      DepositInfo: {
        owner: 'AccountId',
        amount: 'Balance'
      },
      PolymeshVotes: {
        index: 'u32',
        ayes: 'Vec<IdentityId>',
        nays: 'Vec<IdentityId>',
        expiry: 'MaybeBlock'
      },
      PipId: 'u32',
      ProposalState: {
        _enum: [
          'Pending',
          'Rejected',
          'Scheduled',
          'Failed',
          'Executed',
          'Expired'
        ]
      },
      Pip: {
        id: 'PipId',
        proposal: 'Call',
        state: 'ProposalState',
        proposer: 'Proposer'
      },
      ProposalData: {
        _enum: {
          Hash: 'Hash',
          Proposal: 'Vec<u8>'
        }
      },
      OffChainSignature: {
        _enum: {
          Ed25519: 'H512',
          Sr25519: 'H512',
          Ecdsa: 'H512'
        }
      },
      Authorization: {
        authorization_data: 'AuthorizationData',
        authorized_by: 'IdentityId',
        expiry: 'Option<Moment>',
        auth_id: 'u64'
      },
      AuthorizationData: {
        _enum: {
          AttestPrimaryKeyRotation: 'IdentityId',
          RotatePrimaryKey: '',
          TransferTicker: 'Ticker',
          AddMultiSigSigner: 'AccountId',
          TransferAssetOwnership: 'Ticker',
          JoinIdentity: 'Permissions',
          PortfolioCustody: 'PortfolioId',
          BecomeAgent: '(Ticker, AgentGroup)',
          AddRelayerPayingKey: '(AccountId, AccountId, Balance)',
          RotatePrimaryKeyToSecondary: 'Permissions'
        }
      },
      SmartExtensionType: {
        _enum: {
          TransferManager: '',
          Offerings: '',
          SmartWallet: '',
          Custom: 'Vec<u8>'
        }
      },
      SmartExtensionName: 'Text',
      SmartExtension: {
        extension_type: 'SmartExtensionType',
        extension_name: 'SmartExtensionName',
        extension_id: 'AccountId',
        is_archive: 'bool'
      },
      MetaUrl: 'Text',
      MetaDescription: 'Text',
      MetaVersion: 'u32',
      ExtVersion: 'u32',
      TemplateMetadata: {
        url: 'Option<MetaUrl>',
        se_type: 'SmartExtensionType',
        usage_fee: 'Balance',
        description: 'MetaDescription',
        version: 'MetaVersion'
      },
      TemplateDetails: {
        instantiation_fee: 'Balance',
        owner: 'IdentityId',
        frozen: 'bool'
      },
      AuthorizationNonce: 'u64',
      Counter: 'u64',
      Percentage: 'Permill',
      TransferManager: {
        _enum: {
          CountTransferManager: 'Counter',
          PercentageTransferManager: 'Percentage'
        }
      },
      RestrictionResult: {
        _enum: [
          'Valid',
          'Invalid',
          'ForceValid'
        ]
      },
      Memo: '[u8;32]',
      BridgeTx: {
        nonce: 'u32',
        recipient: 'AccountId',
        amount: 'Balance',
        tx_hash: 'H256'
      },
      AssetCompliance: {
        paused: 'bool',
        requirements: 'Vec<ComplianceRequirement>'
      },
      AssetComplianceResult: {
        paused: 'bool',
        requirements: 'Vec<ComplianceRequirementResult>',
        result: 'bool'
      },
      Claim1stKey: {
        target: 'IdentityId',
        claim_type: 'ClaimType'
      },
      Claim2ndKey: {
        issuer: 'IdentityId',
        scope: 'Option<Scope>'
      },
      InactiveMember: {
        id: 'IdentityId',
        deactivated_at: 'Moment',
        expiry: 'Option<Moment>'
      },
      VotingResult: {
        ayes_count: 'u32',
        ayes_stake: 'Balance',
        nays_count: 'u32',
        nays_stake: 'Balance'
      },
      ProtocolOp: {
        _enum: [
          'AssetRegisterTicker',
          'AssetIssue',
          'AssetAddDocuments',
          'AssetCreateAsset',
          'CheckpointCreateSchedule',
          'ComplianceManagerAddComplianceRequirement',
          'IdentityCddRegisterDid',
          'IdentityAddClaim',
          'IdentityAddSecondaryKeysWithAuthorization',
          'PipsPropose',
          'ContractsPutCode',
          'CorporateBallotAttachBallot',
          'CapitalDistributionDistribute'
        ]
      },
      CddStatus: {
        _enum: {
          Ok: 'IdentityId',
          Err: 'Vec<u8>'
        }
      },
      AssetDidResult: {
        _enum: {
          Ok: 'IdentityId',
          Err: 'Vec<u8>'
        }
      },
      DidRecordsSuccess: {
        primary_key: 'AccountId',
        secondary_keys: 'Vec<SecondaryKey>'
      },
      DidRecords: {
        _enum: {
          Success: 'DidRecordsSuccess',
          IdNotFound: 'Vec<u8>'
        }
      },
      VoteCountProposalFound: {
        ayes: 'u64',
        nays: 'u64'
      },
      VoteCount: {
        _enum: {
          ProposalFound: 'VoteCountProposalFound',
          ProposalNotFound: ''
        }
      },
      Vote: '(bool, Balance)',
      VoteByPip: {
        pip: 'PipId',
        vote: 'Vote'
      },
      BridgeTxDetail: {
        amount: 'Balance',
        status: 'BridgeTxStatus',
        execution_block: 'BlockNumber',
        tx_hash: 'H256'
      },
      BridgeTxStatus: {
        _enum: {
          Absent: '',
          Pending: 'u8',
          Frozen: '',
          Timelocked: '',
          Handled: ''
        }
      },
      HandledTxStatus: {
        _enum: {
          Success: '',
          Error: 'Text'
        }
      },
      CappedFee: 'u64',
      CanTransferResult: {
        _enum: {
          Ok: 'u8',
          Err: 'Vec<u8>'
        }
      },
      AuthorizationType: {
        _enum: {
          AttestPrimaryKeyRotation: '',
          RotatePrimaryKey: '',
          TransferTicker: '',
          AddMultiSigSigner: '',
          TransferAssetOwnership: '',
          JoinIdentity: '',
          PortfolioCustody: '',
          BecomeAgent: '',
          AddRelayerPayingKey: '',
          RotatePrimaryKeyToSecondary: ''
        }
      },
      ProposalDetails: {
        approvals: 'u64',
        rejections: 'u64',
        status: 'ProposalStatus',
        expiry: 'Option<Moment>',
        auto_close: 'bool'
      },
      ProposalStatus: {
        _enum: {
          Invalid: '',
          ActiveOrExpired: '',
          ExecutionSuccessful: '',
          ExecutionFailed: '',
          Rejected: ''
        }
      },
      DidStatus: {
        _enum: {
          Unknown: '',
          Exists: '',
          CddVerified: ''
        }
      },
      PortfolioName: 'Text',
      PortfolioNumber: 'u64',
      PortfolioKind: {
        _enum: {
          Default: '',
          User: 'PortfolioNumber'
        }
      },
      PortfolioId: {
        did: 'IdentityId',
        kind: 'PortfolioKind'
      },
      Moment: 'u64',
      CalendarUnit: {
        _enum: [
          'Second',
          'Minute',
          'Hour',
          'Day',
          'Week',
          'Month',
          'Year'
        ]
      },
      CalendarPeriod: {
        unit: 'CalendarUnit',
        amount: 'u64'
      },
      CheckpointSchedule: {
        start: 'Moment',
        period: 'CalendarPeriod'
      },
      CheckpointId: 'u64',
      ScheduleId: 'u64',
      StoredSchedule: {
        schedule: 'CheckpointSchedule',
        id: 'ScheduleId',
        at: 'Moment',
        remaining: 'u32'
      },
      ScheduleSpec: {
        start: 'Option<Moment>',
        period: 'CalendarPeriod',
        remaining: 'u32'
      },
      InstructionStatus: {
        _enum: {
          Unknown: '',
          Pending: '',
          Failed: ''
        }
      },
      LegStatus: {
        _enum: {
          PendingTokenLock: '',
          ExecutionPending: '',
          ExecutionToBeSkipped: '(AccountId, u64)'
        }
      },
      AffirmationStatus: {
        _enum: {
          Unknown: '',
          Pending: '',
          Affirmed: ''
        }
      },
      SettlementType: {
        _enum: {
          SettleOnAffirmation: '',
          SettleOnBlock: 'BlockNumber'
        }
      },
      LegId: 'u64',
      InstructionId: 'u64',
      Instruction: {
        instruction_id: 'InstructionId',
        venue_id: 'VenueId',
        status: 'InstructionStatus',
        settlement_type: 'SettlementType',
        created_at: 'Option<Moment>',
        trade_date: 'Option<Moment>',
        value_date: 'Option<Moment>'
      },
      Leg: {
        from: 'PortfolioId',
        to: 'PortfolioId',
        asset: 'Ticker',
        amount: 'Balance'
      },
      Venue: {
        creator: 'IdentityId',
        venue_type: 'VenueType'
      },
      Receipt: {
        receipt_uid: 'u64',
        from: 'PortfolioId',
        to: 'PortfolioId',
        asset: 'Ticker',
        amount: 'Balance'
      },
      ReceiptMetadata: 'Text',
      ReceiptDetails: {
        receipt_uid: 'u64',
        leg_id: 'LegId',
        signer: 'AccountId',
        signature: 'OffChainSignature',
        metadata: 'ReceiptMetadata'
      },
      UniqueCall: {
        nonce: 'u64',
        call: 'Call'
      },
      MovePortfolioItem: {
        ticker: 'Ticker',
        amount: 'Balance',
        memo: 'Option<Memo>'
      },
      WeightToFeeCoefficient: {
        coeffInteger: 'Balance',
        coeffFrac: 'Perbill',
        negative: 'bool',
        degree: 'u8'
      },
      TargetIdentity: {
        _enum: {
          ExternalAgent: '',
          Specific: 'IdentityId'
        }
      },
      FundraiserId: 'u64',
      FundraiserName: 'Text',
      FundraiserStatus: {
        _enum: [
          'Live',
          'Frozen',
          'Closed',
          'ClosedEarly'
        ]
      },
      FundraiserTier: {
        total: 'Balance',
        price: 'Balance',
        remaining: 'Balance'
      },
      Fundraiser: {
        creator: 'IdentityId',
        offering_portfolio: 'PortfolioId',
        offering_asset: 'Ticker',
        raising_portfolio: 'PortfolioId',
        raising_asset: 'Ticker',
        tiers: 'Vec<FundraiserTier>',
        venue_id: 'VenueId',
        start: 'Moment',
        end: 'Option<Moment>',
        status: 'FundraiserStatus',
        minimum_investment: 'Balance'
      },
      VenueId: 'u64',
      VenueType: {
        _enum: [
          'Other',
          'Distribution',
          'Sto',
          'Exchange'
        ]
      },
      ExtensionAttributes: {
        usage_fee: 'Balance',
        version: 'MetaVersion'
      },
      Tax: 'Permill',
      TargetIdentities: {
        identities: 'Vec<IdentityId>',
        treatment: 'TargetTreatment'
      },
      TargetTreatment: {
        _enum: [
          'Include',
          'Exclude'
        ]
      },
      CAKind: {
        _enum: [
          'PredictableBenefit',
          'UnpredictableBenefit',
          'IssuerNotice',
          'Reorganization',
          'Other'
        ]
      },
      CADetails: 'Text',
      CACheckpoint: {
        _enum: {
          Scheduled: '(ScheduleId, u64)',
          Existing: 'CheckpointId'
        }
      },
      RecordDate: {
        date: 'Moment',
        checkpoint: 'CACheckpoint'
      },
      RecordDateSpec: {
        _enum: {
          Scheduled: 'Moment',
          ExistingSchedule: 'ScheduleId',
          Existing: 'CheckpointId'
        }
      },
      CorporateAction: {
        kind: 'CAKind',
        decl_date: 'Moment',
        record_date: 'Option<RecordDate>',
        targets: 'TargetIdentities',
        default_withholding_tax: 'Tax',
        withholding_tax: 'Vec<(IdentityId, Tax)>'
      },
      LocalCAId: 'u32',
      CAId: {
        ticker: 'Ticker',
        local_id: 'LocalCAId'
      },
      Distribution: {
        from: 'PortfolioId',
        currency: 'Ticker',
        per_share: 'Balance',
        amount: 'Balance',
        remaining: 'Balance',
        reclaimed: 'bool',
        payment_at: 'Moment',
        expires_at: 'Option<Moment>'
      },
      SlashingSwitch: {
        _enum: [
          'Validator',
          'ValidatorAndNominator',
          'None'
        ]
      },
      PriceTier: {
        total: 'Balance',
        price: 'Balance'
      },
      PermissionedIdentityPrefs: {
        intended_count: 'u32',
        running_count: 'u32'
      },
      GranularCanTransferResult: {
        invalid_granularity: 'bool',
        self_transfer: 'bool',
        invalid_receiver_cdd: 'bool',
        invalid_sender_cdd: 'bool',
        missing_scope_claim: 'bool',
        receiver_custodian_error: 'bool',
        sender_custodian_error: 'bool',
        sender_insufficient_balance: 'bool',
        portfolio_validity_result: 'PortfolioValidityResult',
        asset_frozen: 'bool',
        statistics_result: 'Vec<TransferManagerResult>',
        compliance_result: 'AssetComplianceResult',
        result: 'bool'
      },
      PortfolioValidityResult: {
        receiver_is_same_portfolio: 'bool',
        sender_portfolio_does_not_exist: 'bool',
        receiver_portfolio_does_not_exist: 'bool',
        sender_insufficient_balance: 'bool',
        result: 'bool'
      },
      TransferManagerResult: {
        tm: 'TransferManager',
        result: 'bool'
      },
      AGId: 'u32',
      AgentGroup: {
        _enum: {
          Full: '',
          Custom: 'AGId',
          ExceptMeta: '',
          PolymeshV1CAA: '',
          PolymeshV1PIA: ''
        }
      },
      ItnRewardStatus: {
        _enum: {
          Unclaimed: 'Balance',
          Claimed: ''
        }
      }
    }
  },
  TEER: {
    ss58Format: 13,
    tokenDecimals: 12,
    tokenSymbol: 'TEER'
  },
  TOTEM: {
    ss58Format: 14,
    tokenDecimals: 0,
    tokenSymbol: 'TOTEM'
  },
  SYN: {
    ss58Format: 15,
    tokenDecimals: 12,
    tokenSymbol: 'SYN'
  },
  KLP: {
    ss58Format: 16,
    tokenDecimals: 12,
    tokenSymbol: 'KLP'
  },
  RING: {
    ss58Format: 18,
    tokenDecimals: 18,
    tokenSymbol: 'RING'
  },
  WATR: {
    ss58Format: 19,
    tokenDecimals: 18,
    tokenSymbol: 'WATR'
  },
  FIS: {
    ss58Format: 20,
    tokenDecimals: 12,
    tokenSymbol: 'FIS',
    types: {
      Address: 'IndicesLookupSource',
      LookupSource: 'IndicesLookupSource',
      RefCount: 'u32',
      ChainId: 'u8',
      ResourceId: '[u8; 32]',
      DepositNonce: 'u64',
      RateType: 'u64',
      AccountInfo: {
        nonce: 'u32',
        refcount: 'RefCount',
        data: 'AccountData'
      },
      AccountRData: {
        free: 'u128'
      },
      RSymbol: {
        _enum: [
          'RFIS',
          'RDOT',
          'RKSM',
          'RATOM'
        ]
      },
      AccountXData: {
        free: 'u128'
      },
      XSymbol: {
        _enum: [
          'WRA'
        ]
      },
      ProposalStatus: {
        _enum: [
          'Active',
          'Passed',
          'Expired',
          'Executed'
        ]
      },
      ProposalVotes: {
        voted: 'Vec<AccountId>',
        status: 'ProposalStatus',
        expiry: 'BlockNumber'
      },
      BondRecord: {
        bonder: 'AccountId',
        symbol: 'RSymbol',
        pubkey: 'Vec<u8>',
        pool: 'Vec<u8>',
        blockhash: 'Vec<u8>',
        txhash: 'Vec<u8>',
        amount: 'u128'
      },
      BondReason: {
        _enum: [
          'Pass',
          'BlockhashUnmatch',
          'TxhashUnmatch',
          'PubkeyUnmatch',
          'PoolUnmatch',
          'AmountUnmatch'
        ]
      },
      BondState: {
        _enum: [
          'Dealing',
          'Fail',
          'Success'
        ]
      },
      SigVerifyResult: {
        _enum: [
          'InvalidPubkey',
          'Fail',
          'Pass'
        ]
      },
      PoolBondState: {
        _enum: [
          'EraUpdated',
          'BondReported',
          'ActiveReported',
          'WithdrawSkipped',
          'WithdrawReported',
          'TransferReported'
        ]
      },
      BondSnapshot: {
        symbol: 'RSymbol',
        era: 'u32',
        pool: 'Vec<u8>',
        bond: 'u128',
        unbond: 'u128',
        active: 'u128',
        last_voter: 'AccountId',
        bond_state: 'PoolBondState'
      },
      LinkChunk: {
        bond: 'u128',
        unbond: 'u128',
        active: 'u128'
      },
      OriginalTxType: {
        _enum: [
          'Transfer',
          'Bond',
          'Unbond',
          'WithdrawUnbond',
          'ClaimRewards'
        ]
      },
      Unbonding: {
        who: 'AccountId',
        value: 'u128',
        recipient: 'Vec<u8>'
      },
      UserUnlockChunk: {
        pool: 'Vec<u8>',
        unlock_era: 'u32',
        value: 'u128',
        recipient: 'Vec<u8>'
      },
      RproposalStatus: {
        _enum: [
          'Initiated',
          'Approved',
          'Rejected',
          'Expired'
        ]
      },
      RproposalVotes: {
        votes_for: 'Vec<AccountId>',
        votes_against: 'Vec<AccountId>',
        status: 'RproposalStatus',
        expiry: 'BlockNumber'
      }
    }
  },
  KCOIN: {
    ss58Format: 21,
    tokenDecimals: 6,
    tokenSymbol: 'KCOIN'
  },
  DCK: {
    ss58Format: 22,
    tokenDecimals: 6,
    tokenSymbol: 'DCK',
    types: {
      Address: 'MultiAddress',
      LookupSource: 'MultiAddress',
      Keys: 'SessionKeys2',
      PerDispatchClassU32: {
        normal: 'u32',
        operational: 'u32',
        mandatory: 'u32'
      },
      BlockLength: {
        max: 'PerDispatchClassU32'
      },
      Did: '[u8;32]',
      Bytes32: {
        value: '[u8;32]'
      },
      Bytes33: {
        value: '[u8;33]'
      },
      Bytes64: {
        value: '[u8;64]'
      },
      Bytes65: {
        value: '[u8;65]'
      },
      PublicKey: {
        _enum: {
          Sr25519: 'Bytes32',
          Ed25519: 'Bytes32',
          Secp256k1: 'Bytes33'
        }
      },
      DidSignature: {
        _enum: {
          Sr25519: 'Bytes64',
          Ed25519: 'Bytes64',
          Secp256k1: 'Bytes65'
        }
      },
      KeyDetail: {
        controller: 'Did',
        public_key: 'PublicKey'
      },
      KeyUpdate: {
        did: 'Did',
        public_key: 'PublicKey',
        controller: 'Option<Did>',
        last_modified_in_block: 'BlockNumber'
      },
      DidRemoval: {
        did: 'Did',
        last_modified_in_block: 'BlockNumber'
      },
      RegistryId: '[u8;32]',
      RevokeId: '[u8;32]',
      Registry: {
        policy: 'Policy',
        add_only: 'bool'
      },
      Revoke: {
        registry_id: 'RegistryId',
        revoke_ids: 'BTreeSet<RevokeId>',
        last_modified: 'BlockNumber'
      },
      UnRevoke: {
        registry_id: 'RegistryId',
        revoke_ids: 'BTreeSet<RevokeId>',
        last_modified: 'BlockNumber'
      },
      RemoveRegistry: {
        registry_id: 'RegistryId',
        last_modified: 'BlockNumber'
      },
      PAuth: 'BTreeMap<Did, DidSignature>',
      Policy: {
        _enum: {
          OneOf: 'BTreeSet<Did>'
        }
      },
      BlobId: '[u8;32]',
      Blob: {
        id: 'BlobId',
        blob: 'Vec<u8>',
        author: 'Did'
      },
      EpochNo: 'u32',
      EpochLen: 'u32',
      SlotNo: 'u64',
      Balance: 'u64',
      BlockNumber: 'u32',
      EpochDetail: {
        validator_count: 'u8',
        starting_slot: 'SlotNo',
        expected_ending_slot: 'SlotNo',
        ending_slot: 'Option<SlotNo>',
        emission_for_validators: 'Option<Balance>',
        emission_for_treasury: 'Option<Balance>'
      },
      ValidatorStatsPerEpoch: {
        block_count: 'EpochLen',
        locked_reward: 'Option<Balance>',
        unlocked_reward: 'Option<Balance>'
      },
      Bonus: {
        swap_bonuses: 'Vec<(Balance, BlockNumber)>',
        vesting_bonuses: 'Vec<(Balance, Balance, BlockNumber)>'
      },
      Payload: {
        proposal: 'Vec<u8>',
        round_no: 'u64'
      },
      Membership: {
        members: 'BTreeSet<Did>',
        vote_requirement: 'u64'
      },
      PMAuth: 'BTreeMap<Did, DidSignature>',
      Attestation: {
        priority: 'Compact<u64>',
        iri: 'Option<Vec<u8>>'
      },
      Account: {
        nonce: 'U256',
        balance: 'U256'
      },
      Transaction: {
        nonce: 'U256',
        action: 'String',
        gas_price: 'u64',
        gas_limit: 'u64',
        value: 'U256',
        input: 'Vec<u8>',
        signature: 'Signature'
      },
      Signature: {
        v: 'u64',
        r: 'H256',
        s: 'H256'
      },
      ParamType: {
        _enum: {
          Address: null,
          Int: 'u16',
          Uint: 'u16'
        }
      },
      ContractConfig: {
        address: 'H160',
        query_aggregator_call_encoded: 'Vec<u8>',
        query_price_abi_encoded: 'Vec<u8>',
        return_val_abi: 'Vec<ParamType>'
      },
      StateChange: {
        _enum: {
          KeyUpdate: 'KeyUpdate',
          DidRemoval: 'DidRemoval',
          Revoke: 'Revoke',
          UnRevoke: 'UnRevoke',
          RemoveRegistry: 'RemoveRegistry',
          Blob: 'Blob',
          MasterVote: 'Payload',
          Attestation: '(Did, Attestation)'
        }
      }
    }
  },
  ZERO: {
    ss58Format: 25,
    tokenDecimals: 18,
    tokenSymbol: 'ZERO'
  },
  jDOT: {
    ss58Format: 26,
    tokenDecimals: 10,
    tokenSymbol: 'jDOT'
  },
  KAB: {
    ss58Format: 27,
    tokenDecimals: 12,
    tokenSymbol: 'KAB'
  },
  DHI: {
    ss58Format: 29,
    tokenDecimals: 12,
    tokenSymbol: 'DHI'
  },
  PHA: {
    ss58Format: 30,
    tokenDecimals: 12,
    tokenSymbol: 'PHA'
  },
  LIT: {
    ss58Format: 131,
    tokenDecimals: 12,
    tokenSymbol: 'LIT'
  },
  XRT: {
    ss58Format: 32,
    tokenDecimals: 9,
    tokenSymbol: 'XRT'
  },
  ARES: {
    ss58Format: 34,
    tokenDecimals: 12,
    tokenSymbol: 'ARES'
  },
  USDv: {
    ss58Format: 35,
    tokenDecimals: 15,
    tokenSymbol: 'USDv'
  },
  CFG: {
    ss58Format: 36,
    tokenDecimals: 18,
    tokenSymbol: 'CFG'
  },
  NODL: {
    ss58Format: 37,
    tokenDecimals: 11,
    tokenSymbol: 'NODL'
  },
  KILT: {
    ss58Format: 38,
    tokenDecimals: 15,
    tokenSymbol: 'KILT'
  },
  MATH: {
    ss58Format: 40,
    tokenDecimals: 18,
    tokenSymbol: 'MATH'
  },
  PLMC: {
    ss58Format: 41,
    tokenDecimals: 10,
    tokenSymbol: 'PLMC'
  },
  PCX: {
    ss58Format: 44,
    tokenDecimals: 8,
    tokenSymbol: 'PCX'
  },
  UART: {
    ss58Format: 45,
    tokenDecimals: 12,
    tokenSymbol: 'UART'
  },
  NEAT: {
    ss58Format: 48,
    tokenDecimals: 12,
    tokenSymbol: 'NEAT'
  },
  PICA: {
    ss58Format: 49,
    tokenDecimals: 12,
    tokenSymbol: 'PICA'
  },
  LAYR: {
    ss58Format: 50,
    tokenDecimals: 12,
    tokenSymbol: 'LAYR'
  },
  OAK: {
    ss58Format: 51,
    tokenDecimals: 10,
    tokenSymbol: 'OAK'
  },
  KICO: {
    ss58Format: 52,
    tokenDecimals: 14,
    tokenSymbol: 'KICO'
  },
  DICO: {
    ss58Format: 53,
    tokenDecimals: 14,
    tokenSymbol: 'DICO'
  },
  CERE: {
    ss58Format: 54,
    tokenDecimals: 10,
    tokenSymbol: 'CERE'
  },
  XX: {
    ss58Format: 55,
    tokenDecimals: 9,
    tokenSymbol: 'XX'
  },
  PEN: {
    ss58Format: 56,
    tokenDecimals: 12,
    tokenSymbol: 'PEN'
  },
  AMPE: {
    ss58Format: 57,
    tokenDecimals: 12,
    tokenSymbol: 'AMPE'
  },
  ECC: {
    ss58Format: 58,
    tokenDecimals: 12,
    tokenSymbol: 'ECC'
  },
  HDX: {
    ss58Format: 63,
    tokenDecimals: 12,
    tokenSymbol: 'HDX'
  },
  AVT: {
    ss58Format: 65,
    tokenDecimals: 18,
    tokenSymbol: 'AVT'
  },
  CRU: {
    ss58Format: 66,
    tokenDecimals: 12,
    tokenSymbol: 'CRU',
    types: {
      AccountInfo: 'AccountInfoWithProviders',
      Address: 'AccountId',
      AddressInfo: 'Vec<u8>',
      LookupSource: 'AccountId',
      EraBenefits: {
        total_fee_reduction_quota: 'Compact<Balance>',
        total_market_active_funds: 'Compact<Balance>',
        used_fee_reduction_quota: 'Compact<Balance>',
        active_era: 'Compact<EraIndex>'
      },
      FundsType: {
        _enum: [
          'SWORK',
          'MARKET'
        ]
      },
      FundsUnlockChunk: {
        value: 'Compact<Balance>',
        era: 'Compact<EraIndex>'
      },
      MarketBenefit: {
        total_funds: 'Compact<Balance>',
        active_funds: 'Compact<Balance>',
        used_fee_reduction_quota: 'Compact<Balance>',
        file_reward: 'Compact<Balance>',
        refreshed_at: 'Compact<EraIndex>',
        unlocking_funds: 'Vec<FundsUnlockChunk<Balance>>'
      },
      SworkBenefit: {
        total_funds: 'Compact<Balance>',
        active_funds: 'Compact<Balance>',
        total_fee_reduction_count: 'u32',
        used_fee_reduction_count: 'u32',
        refreshed_at: 'Compact<EraIndex>',
        unlocking_funds: 'Vec<FundsUnlockChunk<Balance>>'
      },
      BridgeChainId: 'u8',
      ChainId: 'u8',
      ResourceId: 'H256',
      DepositNonce: 'u64',
      ProposalStatus: {
        _enum: [
          'Initiated',
          'Approved',
          'Rejected'
        ]
      },
      ProposalVotes: {
        votes_for: 'Vec<AccountId>',
        votes_against: 'Vec<AccountId>',
        status: 'ProposalStatus',
        expiry: 'BlockNumber'
      },
      Erc721Token: {
        id: 'TokenId',
        metadata: 'Vec<u8>'
      },
      TokenId: 'U256',
      ETHAddress: 'Vec<u8>',
      EthereumTxHash: 'H256',
      Lock: {
        total: 'Compact<Balance>',
        last_unlock_at: 'BlockNumber',
        lock_type: 'LockType'
      },
      LockType: {
        delay: 'BlockNumber',
        lock_period: 'u32'
      },
      FileInfo: {
        file_size: 'u64',
        spower: 'u64',
        expired_at: 'BlockNumber',
        calculated_at: 'BlockNumber',
        amount: 'Compact<Balance>',
        prepaid: 'Compact<Balance>',
        reported_replica_count: 'u32',
        replicas: 'Vec<Replica<AccountId>>'
      },
      FileInfoV2: {
        file_size: 'u64',
        spower: 'u64',
        expired_at: 'BlockNumber',
        calculated_at: 'BlockNumber',
        amount: 'Compact<Balance>',
        prepaid: 'Compact<Balance>',
        reported_replica_count: 'u32',
        remaining_paid_count: 'u32',
        replicas: 'BTreeMap<AccountId, Replica<AccountId>>'
      },
      Replica: {
        who: 'AccountId',
        valid_at: 'BlockNumber',
        anchor: 'SworkerAnchor',
        is_reported: 'bool',
        created_at: 'Option<BlockNumber>'
      },
      Guarantee: {
        targets: 'Vec<IndividualExposure<AccountId, Balance>>',
        total: 'Compact<Balance>',
        submitted_in: 'EraIndex',
        suppressed: 'bool'
      },
      ValidatorPrefs: {
        guarantee_fee: 'Compact<Perbill>'
      },
      Group: {
        members: 'BTreeSet<AccountId>',
        allowlist: 'BTreeSet<AccountId>'
      },
      IASSig: 'Vec<u8>',
      Identity: {
        anchor: 'SworkerAnchor',
        punishment_deadline: 'u64',
        group: 'Option<AccountId>'
      },
      ISVBody: 'Vec<u8>',
      MerkleRoot: 'Vec<u8>',
      ReportSlot: 'u64',
      PKInfo: {
        code: 'SworkerCode',
        anchor: 'Option<SworkerAnchor>'
      },
      SworkerAnchor: 'Vec<u8>',
      SworkerCert: 'Vec<u8>',
      SworkerCode: 'Vec<u8>',
      SworkerPubKey: 'Vec<u8>',
      SworkerSignature: 'Vec<u8>',
      WorkReport: {
        report_slot: 'u64',
        spower: 'u64',
        free: 'u64',
        reported_files_size: 'u64',
        reported_srd_root: 'MerkleRoot',
        reported_files_root: 'MerkleRoot'
      }
    }
  },
  GENS: {
    ss58Format: 67,
    tokenDecimals: 9,
    tokenSymbol: 'GENS'
  },
  EQ: {
    ss58Format: 68,
    tokenDecimals: 9,
    tokenSymbol: 'EQ'
  },
  XOR: {
    ss58Format: 420,
    tokenDecimals: 18,
    tokenSymbol: 'XOR'
  },
  P3D: {
    ss58Format: 71,
    tokenDecimals: 12,
    tokenSymbol: 'P3D'
  },
  P3Dt: {
    ss58Format: 72,
    tokenDecimals: 12,
    tokenSymbol: 'P3Dt'
  },
  ZTG: {
    ss58Format: 73,
    tokenDecimals: 10,
    tokenSymbol: 'ZTG'
  },
  MANTA: {
    ss58Format: 77,
    tokenDecimals: 18,
    tokenSymbol: 'MANTA'
  },
  KMA: {
    ss58Format: 78,
    tokenDecimals: 12,
    tokenSymbol: 'KMA'
  },
  PDEX: {
    ss58Format: 89,
    tokenDecimals: 12,
    tokenSymbol: 'PDEX'
  },
  FRQCY: {
    ss58Format: 90,
    tokenDecimals: 8,
    tokenSymbol: 'FRQCY'
  },
  ANML: {
    ss58Format: 92,
    tokenDecimals: 18,
    tokenSymbol: 'ANML'
  },
  NOVA: {
    ss58Format: 93,
    tokenDecimals: 12,
    tokenSymbol: 'NOVA'
  },
  PKS: {
    ss58Format: 98,
    tokenDecimals: 18,
    tokenSymbol: 'PKS'
  },
  PKF: {
    ss58Format: 99,
    tokenDecimals: 18,
    tokenSymbol: 'PKF'
  },
  IANML: {
    ss58Format: 100,
    tokenDecimals: 18,
    tokenSymbol: 'IANML'
  },
  OTP: {
    ss58Format: 101,
    tokenDecimals: 12,
    tokenSymbol: 'OTP'
  },
  PONT: {
    ss58Format: 105,
    tokenDecimals: 10,
    tokenSymbol: 'PONT'
  },
  HKO: {
    ss58Format: 110,
    tokenDecimals: 12,
    tokenSymbol: 'HKO'
  },
  TNKR: {
    ss58Format: 117,
    tokenDecimals: 12,
    tokenSymbol: 'TNKR'
  },
  JOY: {
    ss58Format: 126,
    tokenDecimals: 10,
    tokenSymbol: 'JOY'
  },
  CLV: {
    ss58Format: 128,
    tokenDecimals: 18,
    tokenSymbol: 'CLV'
  },
  DORA: {
    ss58Format: 129,
    tokenDecimals: 12,
    tokenSymbol: 'DORA'
  },
  AIR: {
    ss58Format: 136,
    tokenDecimals: 18,
    tokenSymbol: 'AIR'
  },
  VARA: {
    ss58Format: 137,
    tokenDecimals: 12,
    tokenSymbol: 'VARA'
  },
  PARA: {
    ss58Format: 172,
    tokenDecimals: 12,
    tokenSymbol: 'PARA'
  },
  NET: {
    ss58Format: 252,
    tokenDecimals: 18,
    tokenSymbol: 'NET'
  },
  QTZ: {
    ss58Format: 8883,
    tokenDecimals: 18,
    tokenSymbol: 'QTZ'
  },
  NEER: {
    ss58Format: 268,
    tokenDecimals: 18,
    tokenSymbol: 'NEER'
  },
  AFT: {
    ss58Format: 440,
    tokenDecimals: 12,
    tokenSymbol: 'AFT'
  },
  MQTY: {
    ss58Format: 666,
    tokenDecimals: 18,
    tokenSymbol: 'MQTY'
  },
  CGT: {
    ss58Format: 777,
    tokenDecimals: 18,
    tokenSymbol: 'CGT'
  },
  GEEK: {
    ss58Format: 789,
    tokenDecimals: 18,
    tokenSymbol: 'GEEK'
  },
  CAPS: {
    ss58Format: 995,
    tokenDecimals: 18,
    tokenSymbol: 'CAPS'
  },
  EFI: {
    ss58Format: 1110,
    tokenDecimals: 18,
    tokenSymbol: 'EFI'
  },
  PEAQ: {
    ss58Format: 1221,
    tokenDecimals: 18,
    tokenSymbol: 'PEAQ'
  },
  KREST: {
    ss58Format: 1222,
    tokenDecimals: 18,
    tokenSymbol: 'KREST'
  },
  GLMR: {
    ss58Format: 1284,
    tokenDecimals: 18,
    tokenSymbol: 'GLMR'
  },
  MOVR: {
    ss58Format: 1285,
    tokenDecimals: 18,
    tokenSymbol: 'MOVR'
  },
  AJUN: {
    ss58Format: 1328,
    tokenDecimals: 12,
    tokenSymbol: 'AJUN'
  },
  BAJU: {
    ss58Format: 1337,
    tokenDecimals: 12,
    tokenSymbol: 'BAJU'
  },
  SCTL: {
    ss58Format: 1516,
    tokenDecimals: 12,
    tokenSymbol: 'SCTL'
  },
  SEAL: {
    ss58Format: 1985,
    tokenDecimals: 9,
    tokenSymbol: 'SEAL'
  },
  KAPEX: {
    ss58Format: 2007,
    tokenDecimals: 12,
    tokenSymbol: 'KAPEX'
  },
  CWN: {
    ss58Format: 2009,
    tokenDecimals: 18,
    tokenSymbol: 'CWN'
  },
  LGNT: {
    ss58Format: 2021,
    tokenDecimals: 18,
    tokenSymbol: 'LGNT'
  },
  VOW: {
    ss58Format: 2024,
    tokenDecimals: 18,
    tokenSymbol: 'VOW'
  },
  INTR: {
    ss58Format: 2032,
    tokenDecimals: 10,
    tokenSymbol: 'INTR'
  },
  KINT: {
    ss58Format: 2092,
    tokenDecimals: 12,
    tokenSymbol: 'KINT'
  },
  BBB: {
    ss58Format: 2106,
    tokenDecimals: 18,
    tokenSymbol: 'BBB'
  },
  FLIP: {
    ss58Format: 2112,
    tokenDecimals: 18,
    tokenSymbol: 'FLIP'
  },
  SAMA: {
    ss58Format: 2199,
    tokenDecimals: 18,
    tokenSymbol: 'SAMA'
  },
  ICY: {
    ss58Format: 2206,
    tokenDecimals: 18,
    tokenSymbol: 'ICY'
  },
  ICZ: {
    ss58Format: 2207,
    tokenDecimals: 18,
    tokenSymbol: 'ICZ'
  },
  tSSC: {
    ss58Format: 2254,
    tokenDecimals: 18,
    tokenSymbol: 'tSSC'
  },
  PPY: {
    ss58Format: 3333,
    tokenDecimals: 18,
    tokenSymbol: 'PPY'
  },
  G1: {
    ss58Format: 4450,
    tokenDecimals: 2,
    tokenSymbol: 'G1'
  },
  HMND: {
    ss58Format: 5234,
    tokenDecimals: 18,
    tokenSymbol: 'HMND'
  },
  TNT: {
    ss58Format: 5845,
    tokenDecimals: 18,
    tokenSymbol: 'TNT'
  },
  AI3: {
    ss58Format: 6094,
    tokenDecimals: 18,
    tokenSymbol: 'AI3'
  },
  TDFY: {
    ss58Format: 7007,
    tokenDecimals: 12,
    tokenSymbol: 'TDFY'
  },
  FREN: {
    ss58Format: 7013,
    tokenDecimals: 12,
    tokenSymbol: 'FREN'
  },
  KRGN: {
    ss58Format: 7306,
    tokenDecimals: 9,
    tokenSymbol: 'KRGN'
  },
  UNQ: {
    ss58Format: 7391,
    tokenDecimals: 18,
    tokenSymbol: 'UNQ'
  },
  GGX: {
    ss58Format: 8866,
    tokenDecimals: 18,
    tokenSymbol: 'GGX'
  },
  GGXT: {
    ss58Format: 8886,
    tokenDecimals: 18,
    tokenSymbol: 'GGXT'
  },
  HASH: {
    ss58Format: 9072,
    tokenDecimals: 18,
    tokenSymbol: 'HASH'
  },
  DENTX: {
    ss58Format: 9807,
    tokenDecimals: 18,
    tokenSymbol: 'DENTX'
  },
  TRN: {
    ss58Format: 9935,
    tokenDecimals: 12,
    tokenSymbol: 'TRN'
  },
  BSX: {
    ss58Format: 10041,
    tokenDecimals: 12,
    tokenSymbol: 'BSX'
  },
  TCESS: {
    ss58Format: 11330,
    tokenDecimals: 18,
    tokenSymbol: 'TCESS'
  },
  CESS: {
    ss58Format: 11331,
    tokenDecimals: 18,
    tokenSymbol: 'CESS'
  },
  LUHN: {
    ss58Format: 11486,
    tokenDecimals: 18,
    tokenSymbol: 'LUHN'
  },
  CTX: {
    ss58Format: 11820,
    tokenDecimals: 18,
    tokenSymbol: 'CTX'
  },
  BSTY: {
    ss58Format: 12155,
    tokenDecimals: 18,
    tokenSymbol: 'BSTY'
  },
  NMT: {
    ss58Format: 12191,
    tokenDecimals: 12,
    tokenSymbol: 'NMT'
  },
  ANLOG: {
    ss58Format: 12850,
    tokenDecimals: 12,
    tokenSymbol: 'ANLOG'
  },
  TAO: {
    ss58Format: 13116,
    tokenDecimals: 9,
    tokenSymbol: 'TAO'
  },
  GORO: {
    ss58Format: 14697,
    tokenDecimals: 9,
    tokenSymbol: 'GORO'
  },
  MOS: {
    ss58Format: 14998,
    tokenDecimals: 18,
    tokenSymbol: 'MOS'
  },
  MYTH: {
    ss58Format: 29972,
    tokenDecimals: 18,
    tokenSymbol: 'MYTH'
  },
  XCAV: {
    ss58Format: 8888,
    tokenDecimals: 12,
    tokenSymbol: 'XCAV'
  },
  AZERO: {
    ss58Format: 42,
    tokenDecimals: 12,
    tokenSymbol: 'AZERO'
  },
  DBC: {
    ss58Format: 42,
    tokenDecimals: 15,
    tokenSymbol: 'DBC'
  },
  DEER: {
    ss58Format: 0,
    tokenDecimals: 12,
    tokenSymbol: 'DEER'
  },
  ENJ: {
    ss58Format: 2135,
    tokenDecimals: 18,
    tokenSymbol: 'ENJ'
  },
  SDN: {
    ss58Format: 5,
    tokenDecimals: 18,
    tokenSymbol: 'SDN'
  },
  AVAIL: {
    ss58Format: 42,
    tokenDecimals: 18,
    tokenSymbol: 'AVAIL',
    types: {
      AppId: 'Compact<u32>',
      DataLookupItem: {
        appId: 'AppId',
        start: 'Compact<u32>'
      },
      CompactDataLookup: {
        size: 'Compact<u32>',
        index: 'Vec<DataLookupItem>'
      },
      KateCommitment: {
        rows: 'Compact<u16>',
        cols: 'Compact<u16>',
        commitment: 'Vec<u8>',
        dataRoot: 'H256'
      },
      V3HeaderExtension: {
        appLookup: 'CompactDataLookup',
        commitment: 'KateCommitment'
      },
      HeaderExtension: {
        _enum: {
          V1: null,
          V2: null,
          V3: 'V3HeaderExtension'
        }
      },
      DaHeader: {
        parentHash: 'Hash',
        number: 'Compact<BlockNumber>',
        stateRoot: 'Hash',
        extrinsicsRoot: 'Hash',
        digest: 'Digest',
        extension: 'HeaderExtension'
      },
      Header: 'DaHeader',
      CheckAppIdExtra: {
        appId: 'AppId'
      },
      CheckAppIdTypes: {},
      CheckAppId: {
        extra: 'CheckAppIdExtra',
        types: 'CheckAppIdTypes'
      },
      BlockLengthColumns: 'Compact<u32>',
      BlockLengthRows: 'Compact<u32>',
      BlockLength: {
        max: 'PerDispatchClass',
        cols: 'BlockLengthColumns',
        rows: 'BlockLengthRows',
        chunkSize: 'Compact<u32>'
      },
      PerDispatchClass: {
        normal: 'u32',
        operational: 'u32',
        mandatory: 'u32'
      },
      DataProof: {
        roots: 'TxDataRoots',
        proof: 'Vec<H256>',
        numberOfLeaves: 'Compact<u32>',
        leafIndex: 'Compact<u32>',
        leaf: 'H256'
      },
      TxDataRoots: {
        dataRoot: 'H256',
        blobRoot: 'H256',
        bridgeRoot: 'H256'
      },
      ProofResponse: {
        dataProof: 'DataProof',
        message: 'Option<AddressedMessage>'
      },
      AddressedMessage: {
        message: 'Message',
        from: 'H256',
        to: 'H256',
        originDomain: 'u32',
        destinationDomain: 'u32',
        data: 'Vec<u8>',
        id: 'u64'
      },
      Message: {
        _enum: {
          ArbitraryMessage: 'ArbitraryMessage',
          FungibleToken: 'FungibleToken'
        }
      },
      MessageType: {
        _enum: [
          'ArbitraryMessage',
          'FungibleToken'
        ]
      },
      FungibleToken: {
        assetId: 'H256',
        amount: 'String'
      },
      BoundedData: 'Vec<u8>',
      ArbitraryMessage: 'BoundedData',
      Cell: {
        row: 'u32',
        col: 'u32'
      }
    },
    signedExtensions: [
      'CheckNonZeroSender',
      'CheckSpecVersion',
      'CheckTxVersion',
      'CheckGenesis',
      'CheckMortality',
      'CheckNonce',
      'CheckWeight',
      'ChargeTransactionPayment',
      'CheckAppId'
    ],
    userExtensions: {
      CheckAppId: {
        extrinsic: {
          appId: 'AppId'
        },
        payload: {}
      }
    }
  }
}
