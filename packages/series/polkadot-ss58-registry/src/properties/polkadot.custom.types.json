{"CRU": {"types": {"AccountInfo": "AccountInfoWithProviders", "Address": "AccountId", "AddressInfo": "Vec<u8>", "LookupSource": "AccountId", "EraBenefits": {"total_fee_reduction_quota": "Compact<Balance>", "total_market_active_funds": "Compact<Balance>", "used_fee_reduction_quota": "Compact<Balance>", "active_era": "Compact<EraIndex>"}, "FundsType": {"_enum": ["SWORK", "MARKET"]}, "FundsUnlockChunk": {"value": "Compact<Balance>", "era": "Compact<EraIndex>"}, "MarketBenefit": {"total_funds": "Compact<Balance>", "active_funds": "Compact<Balance>", "used_fee_reduction_quota": "Compact<Balance>", "file_reward": "Compact<Balance>", "refreshed_at": "Compact<EraIndex>", "unlocking_funds": "Vec<FundsUnlockChunk<Balance>>"}, "SworkBenefit": {"total_funds": "Compact<Balance>", "active_funds": "Compact<Balance>", "total_fee_reduction_count": "u32", "used_fee_reduction_count": "u32", "refreshed_at": "Compact<EraIndex>", "unlocking_funds": "Vec<FundsUnlockChunk<Balance>>"}, "BridgeChainId": "u8", "ChainId": "u8", "ResourceId": "H256", "DepositNonce": "u64", "ProposalStatus": {"_enum": ["Initiated", "Approved", "Rejected"]}, "ProposalVotes": {"votes_for": "Vec<AccountId>", "votes_against": "Vec<AccountId>", "status": "ProposalStatus", "expiry": "BlockNumber"}, "Erc721Token": {"id": "TokenId", "metadata": "Vec<u8>"}, "TokenId": "U256", "ETHAddress": "Vec<u8>", "EthereumTxHash": "H256", "Lock": {"total": "Compact<Balance>", "last_unlock_at": "BlockNumber", "lock_type": "LockType"}, "LockType": {"delay": "BlockNumber", "lock_period": "u32"}, "FileInfo": {"file_size": "u64", "spower": "u64", "expired_at": "BlockNumber", "calculated_at": "BlockNumber", "amount": "Compact<Balance>", "prepaid": "Compact<Balance>", "reported_replica_count": "u32", "replicas": "Vec<Replica<AccountId>>"}, "FileInfoV2": {"file_size": "u64", "spower": "u64", "expired_at": "BlockNumber", "calculated_at": "BlockNumber", "amount": "Compact<Balance>", "prepaid": "Compact<Balance>", "reported_replica_count": "u32", "remaining_paid_count": "u32", "replicas": "BTreeMap<AccountId, Replica<AccountId>>"}, "Replica": {"who": "AccountId", "valid_at": "BlockNumber", "anchor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "is_reported": "bool", "created_at": "Option<BlockNumber>"}, "Guarantee": {"targets": "Vec<IndividualExposure<AccountId, Balance>>", "total": "Compact<Balance>", "submitted_in": "EraIndex", "suppressed": "bool"}, "ValidatorPrefs": {"guarantee_fee": "Compact<Perbill>"}, "Group": {"members": "BTreeSet<AccountId>", "allowlist": "BTreeSet<AccountId>"}, "IASSig": "Vec<u8>", "Identity": {"anchor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "punishment_deadline": "u64", "group": "Option<AccountId>"}, "ISVBody": "Vec<u8>", "MerkleRoot": "Vec<u8>", "ReportSlot": "u64", "PKInfo": {"code": "SworkerCode", "anchor": "Option<SworkerAnchor>"}, "SworkerAnchor": "Vec<u8>", "SworkerCert": "Vec<u8>", "SworkerCode": "Vec<u8>", "SworkerPubKey": "Vec<u8>", "SworkerSignature": "Vec<u8>", "WorkReport": {"report_slot": "u64", "spower": "u64", "free": "u64", "reported_files_size": "u64", "reported_srd_root": "MerkleRoot", "reported_files_root": "MerkleRoot"}}}, "DCK": {"types": {"Address": "MultiAddress", "LookupSource": "MultiAddress", "Keys": "SessionKeys2", "PerDispatchClassU32": {"normal": "u32", "operational": "u32", "mandatory": "u32"}, "BlockLength": {"max": "PerDispatchClassU32"}, "Did": "[u8;32]", "Bytes32": {"value": "[u8;32]"}, "Bytes33": {"value": "[u8;33]"}, "Bytes64": {"value": "[u8;64]"}, "Bytes65": {"value": "[u8;65]"}, "PublicKey": {"_enum": {"Sr25519": "Bytes32", "Ed25519": "Bytes32", "Secp256k1": "Bytes33"}}, "DidSignature": {"_enum": {"Sr25519": "Bytes64", "Ed25519": "Bytes64", "Secp256k1": "Bytes65"}}, "KeyDetail": {"controller": "Did", "public_key": "PublicKey"}, "KeyUpdate": {"did": "Did", "public_key": "PublicKey", "controller": "Option<Did>", "last_modified_in_block": "BlockNumber"}, "DidRemoval": {"did": "Did", "last_modified_in_block": "BlockNumber"}, "RegistryId": "[u8;32]", "RevokeId": "[u8;32]", "Registry": {"policy": "Policy", "add_only": "bool"}, "Revoke": {"registry_id": "RegistryId", "revoke_ids": "BTreeSet<RevokeId>", "last_modified": "BlockNumber"}, "UnRevoke": {"registry_id": "RegistryId", "revoke_ids": "BTreeSet<RevokeId>", "last_modified": "BlockNumber"}, "RemoveRegistry": {"registry_id": "RegistryId", "last_modified": "BlockNumber"}, "PAuth": "BTreeMap<Did, DidSignature>", "Policy": {"_enum": {"OneOf": "BTreeSet<Did>"}}, "BlobId": "[u8;32]", "Blob": {"id": "BlobId", "blob": "Vec<u8>", "author": "Did"}, "EpochNo": "u32", "EpochLen": "u32", "SlotNo": "u64", "Balance": "u64", "BlockNumber": "u32", "EpochDetail": {"validator_count": "u8", "starting_slot": "SlotNo", "expected_ending_slot": "SlotNo", "ending_slot": "Option<SlotNo>", "emission_for_validators": "Option<Balance>", "emission_for_treasury": "Option<Balance>"}, "ValidatorStatsPerEpoch": {"block_count": "EpochLen", "locked_reward": "Option<Balance>", "unlocked_reward": "Option<Balance>"}, "Bonus": {"swap_bonuses": "Vec<(<PERSON>lance, BlockNumber)>", "vesting_bonuses": "Vec<(Balance, Balance, BlockNumber)>"}, "Payload": {"proposal": "Vec<u8>", "round_no": "u64"}, "Membership": {"members": "BTreeSet<Did>", "vote_requirement": "u64"}, "PMAuth": "BTreeMap<Did, DidSignature>", "Attestation": {"priority": "Compact<u64>", "iri": "Option<Vec<u8>>"}, "Account": {"nonce": "U256", "balance": "U256"}, "Transaction": {"nonce": "U256", "action": "String", "gas_price": "u64", "gas_limit": "u64", "value": "U256", "input": "Vec<u8>", "signature": "Signature"}, "Signature": {"v": "u64", "r": "H256", "s": "H256"}, "ParamType": {"_enum": {"Address": null, "Int": "u16", "Uint": "u16"}}, "ContractConfig": {"address": "H160", "query_aggregator_call_encoded": "Vec<u8>", "query_price_abi_encoded": "Vec<u8>", "return_val_abi": "Vec<ParamType>"}, "StateChange": {"_enum": {"KeyUpdate": "KeyUpdate", "DidRemoval": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Revoke": "Revoke", "UnRevoke": "UnRevoke", "RemoveRegistry": "RemoveRegistry", "Blob": "Blob", "MasterVote": "Payload", "Attestation": "(Did, Attestation)"}}}}, "FIS": {"types": {"Address": "IndicesLookupSource", "LookupSource": "IndicesLookupSource", "RefCount": "u32", "ChainId": "u8", "ResourceId": "[u8; 32]", "DepositNonce": "u64", "RateType": "u64", "AccountInfo": {"nonce": "u32", "refcount": "RefCount", "data": "AccountData"}, "AccountRData": {"free": "u128"}, "RSymbol": {"_enum": ["RFIS", "RDOT", "RKSM", "RATOM"]}, "AccountXData": {"free": "u128"}, "XSymbol": {"_enum": ["WRA"]}, "ProposalStatus": {"_enum": ["Active", "Passed", "Expired", "Executed"]}, "ProposalVotes": {"voted": "Vec<AccountId>", "status": "ProposalStatus", "expiry": "BlockNumber"}, "BondRecord": {"bonder": "AccountId", "symbol": "RSymbol", "pubkey": "Vec<u8>", "pool": "Vec<u8>", "blockhash": "Vec<u8>", "txhash": "Vec<u8>", "amount": "u128"}, "BondReason": {"_enum": ["Pass", "BlockhashUnmatch", "TxhashUnmatch", "PubkeyUnmatch", "PoolUnmatch", "AmountUnmatch"]}, "BondState": {"_enum": ["Dealing", "Fail", "Success"]}, "SigVerifyResult": {"_enum": ["Invalid<PERSON><PERSON>key", "Fail", "Pass"]}, "PoolBondState": {"_enum": ["EraUpdated", "BondReported", "ActiveReported", "WithdrawSkipped", "WithdrawReported", "TransferReported"]}, "BondSnapshot": {"symbol": "RSymbol", "era": "u32", "pool": "Vec<u8>", "bond": "u128", "unbond": "u128", "active": "u128", "last_voter": "AccountId", "bond_state": "PoolBondState"}, "LinkChunk": {"bond": "u128", "unbond": "u128", "active": "u128"}, "OriginalTxType": {"_enum": ["Transfer", "Bond", "Unbond", "WithdrawUnbond", "ClaimRewards"]}, "Unbonding": {"who": "AccountId", "value": "u128", "recipient": "Vec<u8>"}, "UserUnlockChunk": {"pool": "Vec<u8>", "unlock_era": "u32", "value": "u128", "recipient": "Vec<u8>"}, "RproposalStatus": {"_enum": ["Initiated", "Approved", "Rejected", "Expired"]}, "RproposalVotes": {"votes_for": "Vec<AccountId>", "votes_against": "Vec<AccountId>", "status": "RproposalStatus", "expiry": "BlockNumber"}}}, "AVAIL": {"types": {"AppId": "Compact<u32>", "DataLookupItem": {"appId": "AppId", "start": "Compact<u32>"}, "CompactDataLookup": {"size": "Compact<u32>", "index": "Vec<DataLookupItem>"}, "KateCommitment": {"rows": "Compact<u16>", "cols": "Compact<u16>", "commitment": "Vec<u8>", "dataRoot": "H256"}, "V3HeaderExtension": {"appLookup": "CompactDataLookup", "commitment": "KateCommitment"}, "HeaderExtension": {"_enum": {"V1": null, "V2": null, "V3": "V3HeaderExtension"}}, "DaHeader": {"parentHash": "Hash", "number": "Compact<BlockNumber>", "stateRoot": "Hash", "extrinsicsRoot": "Hash", "digest": "Digest", "extension": "HeaderExtension"}, "Header": "<PERSON><PERSON><PERSON><PERSON>", "CheckAppIdExtra": {"appId": "AppId"}, "CheckAppIdTypes": {}, "CheckAppId": {"extra": "CheckAppIdExtra", "types": "CheckAppIdTypes"}, "BlockLengthColumns": "Compact<u32>", "BlockLengthRows": "Compact<u32>", "BlockLength": {"max": "PerDispatchClass", "cols": "BlockLengthColumns", "rows": "BlockLengthRows", "chunkSize": "Compact<u32>"}, "PerDispatchClass": {"normal": "u32", "operational": "u32", "mandatory": "u32"}, "DataProof": {"roots": "TxDataRoots", "proof": "Vec<H256>", "numberOfLeaves": "Compact<u32>", "leafIndex": "Compact<u32>", "leaf": "H256"}, "TxDataRoots": {"dataRoot": "H256", "blobRoot": "H256", "bridgeRoot": "H256"}, "ProofResponse": {"dataProof": "DataProof", "message": "Option<AddressedMessage>"}, "AddressedMessage": {"message": "Message", "from": "H256", "to": "H256", "originDomain": "u32", "destinationDomain": "u32", "data": "Vec<u8>", "id": "u64"}, "Message": {"_enum": {"ArbitraryMessage": "ArbitraryMessage", "FungibleToken": "FungibleToken"}}, "MessageType": {"_enum": ["ArbitraryMessage", "FungibleToken"]}, "FungibleToken": {"assetId": "H256", "amount": "String"}, "BoundedData": "Vec<u8>", "ArbitraryMessage": "BoundedData", "Cell": {"row": "u32", "col": "u32"}}, "signedExtensions": ["CheckNonZeroSender", "CheckSpecVersion", "CheckTxVersion", "CheckGenesis", "CheckMortality", "CheckNonce", "CheckWeight", "ChargeTransactionPayment", "CheckAppId"], "userExtensions": {"CheckAppId": {"extrinsic": {"appId": "AppId"}, "payload": {}}}}}