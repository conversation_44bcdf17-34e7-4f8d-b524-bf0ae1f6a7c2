import { sha256, stringTo<PERSON><PERSON>, Slip10, Slip10Curve, EnglishMnemonic, Bip39 } from '@cosmjs/crypto'
import { fromBase64, toBase64, toHex, toBech32, fromBech32 } from '@cosmjs/encoding'
import { coins, decodeTxRaw, DirectSecp256k1HdWallet, Registry, DirectSecp256k1Wallet } from '@cosmjs/proto-signing'
import { defaultRegistryTypes, SigningStargateClient } from '@cosmjs/stargate'
import { encodeBech32Pubkey, encodeSecp256k1Pubkey } from '@cosmjs/amino'
// ESLint 默认使用 eslint-plugin-import 插件 使用 substack/resolve 解析器，暂无 Node 原生解析器在 subpath imports 的能力
// eslint-disable-next-line import/extensions
import { TxRaw } from '#v1beta1/tx'

// TODO: 添加 sign message 方法
function getWallet (privateKey, prefix) {
  return DirectSecp256k1Wallet.fromKey(privateKey, prefix)
}

function seedToPrivateKey (seed, hdPath) {
  const { privkey } = Slip10.derivePath(Slip10Curve.Secp256k1, seed, hdPath)
  return privkey
}

async function MnemonicToPrivateKey (mnemonic, hdPath, options = {}) {
  const mnemonicChecked = new EnglishMnemonic(mnemonic)
  const seed = await Bip39.mnemonicToSeed(mnemonicChecked, options.bip39Password)
  return seedToPrivateKey(seed, hdPath)
}

async function generate (hdPaths, prefix, length = 12) {
  const wallet = await DirectSecp256k1HdWallet.generate(length, { hdPaths, prefix })
  const [{ pubkey, address }] = await wallet.getAccounts()
  return {
    publicKey: pubkey,
    address,
    seed: wallet.seed,
    mnemonic: wallet.mnemonic
  }
}

function getRegistry (typeUrl = null, type = null) {
  if (type && typeUrl) {
    const registry = new Registry()
    registry.register(typeUrl, type)
    return registry
  }
  return new Registry(defaultRegistryTypes)
}

function decode (signedRaw) {
  return decodeTxRaw(fromBase64(signedRaw))
}

async function signTx (signer, { from, messages, fee, memo, explicitSignerData }, options = {}) {
  const client = await SigningStargateClient.offline(signer, options)
  const signedTx = await client.sign(from, messages, fee, memo, explicitSignerData)
  const txBytes = TxRaw.encode(signedTx).finish()

  return {
    signedRaw: toBase64(txBytes),
    txHash: toHex(sha256(txBytes)).toUpperCase()
  }
}

function buildResult (decodeTx, messages) {
  return {
    authInfo: {
      fee: {
        amount: decodeTx.authInfo?.fee?.amount,
        gasLimit: decodeTx.authInfo?.fee?.gasLimit?.toString()
      }
    },
    body: {
      memo: decodeTx.body.memo,
      messages
    }
  }
}

export {
  getWallet,
  seedToPrivateKey,
  MnemonicToPrivateKey,
  generate,
  buildResult,
  coins,
  fromBech32,
  toBase64,
  toBech32,
  stringToPath,
  getRegistry,
  decode,
  signTx,
  encodeBech32Pubkey,
  encodeSecp256k1Pubkey
}
