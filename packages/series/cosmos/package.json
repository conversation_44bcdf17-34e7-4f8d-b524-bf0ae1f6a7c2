{"name": "@series/cosmos", "version": "1.2.1", "description": "@common/sdk package description", "author": "blockchain-group", "license": "ISC", "type": "module", "imports": {"#v1beta1/tx": "cosmjs-types/cosmos/tx/v1beta1/tx.js"}, "exports": {"import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}, "publishConfig": {"access": "public"}, "files": ["dist"], "dependencies": {"@cosmjs/crypto": "0.32.4", "@cosmjs/encoding": "0.32.4", "@cosmjs/proto-signing": "0.32.4", "@cosmjs/stargate": "0.32.4", "cosmjs-types": "0.9.0", "@cosmjs/amino": "0.32.4"}}