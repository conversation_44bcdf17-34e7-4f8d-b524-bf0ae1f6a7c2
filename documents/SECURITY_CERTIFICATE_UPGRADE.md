<h1 align="center">🃏 安全证书升级评估</h1>

NodeJs SDK 从 14 升至 18，过程中发现部分类型证书，因为安全性或者过期维护问题，需要强制升级。

## 涉及证书类型

- SSL
- JSON Web Token（JWT）

## SSL

### 问题描述
核心：OpenSSL 3.x 要求证书安全标准从 sha1 升级至 sha256

首先，问题的根源出在 OpenSSL，根据官方文档，其 1.1 版本 2023-09-11 停止更新维护。
其次，根据官方文档，自 NodeJs SDK v16以后，SDK里强制要求 OpenSSL 使用 3.x，并且不再对 OpenSSL 1.x 进行兼容。

文档/资料：
> https://nodejs.org/en/blog/vulnerability/mar-2022-security-releases
> https://www.openssl.org/policies/releasestrat.html
> https://nodejs.org/dist/latest-v18.x/docs/api/https.html#httpscreateserveroptions-requestlistener

### 涉及系统

- 签名机（服务端）
- Validator（服务端）
- Gaia（客户端）

### 解决方案

#### 方案一
仍使用 OpenSSL 1.x 安全标准，证书不变。除 NodeJs 技术栈外，其余涉及系统不做变动。

经调研：
1、为使高版本 NodeJs 可以使用上 OpenSSL 1.x，只能自行根据官方源码编译跨平台 SDK，在编译环节拉取 OpenSSL 1.x 进行集成。
2、NodeJs 并非所有平台的SDK均使用编译集成的 OpenSSL，比如 Macos 和部分发行版 Linux，使用依赖的是系统版本。进而进行运行时环境切换。

结论：
1、自行编译语言SDK，难度大，后期维护随着跨平台的复杂度（macos - [intel/arm]，ubuntu - 测试环境，docker容器 - [node 官方源]等），成本变高。
2、因为不是采用官方源，所以在 SDK 的部署环节选择上，存在安全性攻击风险。
3、因为 OpenSSL 本身 9 月份后也停止对 1.x 版本进行安全维护更新，所以不升级至 3.x，SSL 本身也存在安全性风险

#### 方案二
测试/正式环境，涉及到的系统，全部升级为 OpenSSL 3.x 安全标准的证书。

- 签名机 + Validator 作为服务端
  - 新币
    - 无论签名机是 Js/Java，今后全部采用 OpenSSL 3.x 证书
    - Validator 采用 OpenSSL 3.x 证书
  - 老币
    - 陆续迁移，且要配套升级。
    - 签名机可在停机模式下，直接替换升级。
    - Validator 双跑，老的 Node 14 版本不动，针对升级后的币种，启用 18 版本的实例
- Gaia 作为客户端
  - 代码层面
    - 存在两个版本？首先，作为客户端请求，将向 签名机/Validator 的请求，封装成一份
    - 一份中既兼容 OpenSSL 3.x sha256 标准，也向下兼容 OpenSSL 1.x sha1 标准。根据配置不同，去请求各自的证书，进而完成请求

## JSON Web Token（JWT）

因为非强制性升级，并且涉及系统较多，一期暂时可以不考虑。
