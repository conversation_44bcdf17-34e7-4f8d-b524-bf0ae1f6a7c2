<h2 align="center">🃏 NodeJs 技术栈内部编码规范</h2>

区别 Standard JavaScript, 内部编码在结合 ES2022 背景下，针对部分细节的实际用法及场景，为了统一规范，编写此文档，并期望在此拉齐。

常用文档/资料：
> https://nodejs.org/dist/latest-v18.x/docs/api/
> https://swc.rs/docs/getting-started
> https://pnpm.io/zh/motivation
> https://jestjs.io/zh-Hans/docs/next/getting-started
> https://developer.mozilla.org/zh-CN/docs/Web/JavaScript
> https://node.green/
> https://es6.ruanyifeng.com/
> https://www.copycat.dev/blog/javascript-classes/

### export 与 export.default
1. 什么时候用 export（命名导出）
- 当前文件存在多处需要导出的内容
```javascript
export let name1, name2, …, nameN;
export function FunctionName(){...}
export class ClassName {...}
```
- 虽然只存在一处内容需要导出，但涉及重导出/聚合
> 参考1：https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Statements/export <br>
> 参考2：https://es6.ruanyifeng.com/#docs/module#export-default-命令 <br>
> 参考3：@polkadot/api

```javascript
// pkgA/model1.js
export class ClassName {...}

// pkgA/model2.js
export function FunctionName(){...}

// pkgA/index.js
export * from './model1'
export * from './model2'

// pkgB/index.js
import { ClassName, FunctionName } from 'pkgA'
```

2. 什么时候用 export.default（默认导出）
- 当前文件只存在一处内容需要导出，且不需要>1次以上的重导出/聚合被使用

```javascript
// model1.js
export default class { .. }

// model2.js
import X from 'model1.js' // 无需解构
```

### export 多导出时的规范
不采用底部聚合/批量导出，而是哪里需要导出写哪里

```javascript
// good
export let name1, name2, …, nameN;
export function FunctionName(){...}
export class ClassName {...}

// bad
let name1, name2, …, nameN
function FunctionName(){...}
class ClassName {...}
export { name1, name2, …, nameN, FunctionName, ClassName }
```

### return await
原则上，不进行 `return await` 写法。如果遇到

```javascript
const x = await y;
return x;
```

正确的写法应该是

```javascript
return y;
```
相当于子函数中返回promise，让父函数去承接await

### 函数参数列表
- 何时可以平铺参数列表 `function x(a, b, c) {...}`
  - 列表中参数 <= 2
  ```javascript
  function x(a) {...}
  function y(a, b) {...}
  ```
  - 列表中参数虽然 > 2，但后面的参数均可以通过参数默认值的形式传参
  ```javascript
  function z(a, b, c=0, d='hah') {...}
  ```
- 何时需要对象化参数列表 `function x({ a, b, c }) {...}`
  - 参数 > 2，且后面参数无默认值，或者不清楚默认值时

### 箭头函数与类实例方法写法
- 何时使用箭头函数缩写
  - 所有非类实例方法，建议声明函数时，都用箭头函数缩写
  ```javascript
  const wait = ms => new Promise(resolve => setTimeout(resolve, ms))
  ```
- 何时使用类实例方法缩写
  - 所有类中的实例方法，建议都用类方法缩写
  ```javascript
  wait (x, y, z) {
    return [x, y, z]
  }
  ```

### 普通函数与类实例方法声明空格规范
- 普通函数名称与括号间，不建议加空格
```javascript
function x(a) {...}
function y(a, b) {...}
```
- 类实例方法名称与括号间，建议加入空格
```javascript
constructor (a) {...}
encrypt (a, b) {...}
```

### import 解构规范
如无特殊要求，建议 import 全部采用解构写法
```javascript
import { cube, foo, graph } from 'my-module.js';
```
  
### class constructor return
如无特殊说明，不建议 constructor 中直接 return。因为 return 会破坏该类的实例，return 的不再是默认的 this 原型链。
return 特例举例：`common/utils/model.js` 中的 `MetaModel`

### class 静态、私有属性及方法的界定
- 不被外界直接访问，或者未来可能访问的属性或者方法，原则上，都应该设置为`私有`，避免外部意外引用，导致安全性问题
- 不需要初始化的类，原则上，建议对外使用时，内部定义为`静态`属性或者方法

### constants 与 utils 的划分界定
原则上，constants 与 utils 应该只与当前包的业务逻辑产生关系，而非跨包引用依赖。

### package.json 中需要注意的地方
所有依赖类的属性`dependencies`，`devDependencies`等，建议写在底部，方便直观查看。
