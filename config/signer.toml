[db]
# if coin in wallet.db is different from chain name, you can config it.
# storeName = 'vsys'

# whether migrating db/file store privkey or wif
migrateStorePriv = true
timestamps = false

# sequelize model to use = base or pci
# model =  ''

# sqlite store path
# dbPath =  ''

# sqlite db filename
# dbName = ''

# sqlite table name for keys
# tableName = ''

# address export config
[addrExport]
# export path default is daPath/currency
# exportPath = ''
platform = 'huobipro'
jwt_pub = './crypto/jwt_public_sample.pem'
jwt_priv = './crypto/jwt_private_sample.pem'

# jwt config for address export
[addrExport.jwtOptions]
algorithm = 'ES256'
noTimestamp = true

# jwt config header
[addrExport.jwtOptions.header]
iss = 'HWallet-test'
sub = 'AddressOnChain'

# addr_type field mapping to db record row
# all index area is closed interval
[[addrExport.addrTypes]]
type = 16
indexes = [
    { start = 1, end = 5 },
]

# another address type
[[addrExport.addrTypes]]
type = 4
indexes = [
    { start = 6, end = 10 },
]
