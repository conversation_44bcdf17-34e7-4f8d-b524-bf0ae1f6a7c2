ws_url = 'ws://qukuai-eos-state-1a-1.aws-jp1.huobiidc.com:10000'
rpc_url = 'https://flow-access-mainnet.portto.io'
net_work = 'testnet' #只能是testnet或mainnet

# 注册地址配置
# 注册地址分为两种：
#   1. 使用公钥绑定指定地址(绑定成功即可，不需要导出)
#   2. 注册时返回地址(需要把地址导出，使用签名机工具写回到签名机)
[register]
fromAddress = '4b7471b6aa09c98e'
fromPrivateKey = 'd255e8b9a6ce35d27eba7ef6f189156f8b9d3d5ba1ae933cd8376f07a8af2de1'
fromPublicKey = '6eaf1b119c31d9f7438bd1dcc632df162cb35701b32d9e7acea49ee492d6835c7c6b118ebaa9d5c16e4c35f569feb50fedd6be530fbb5a6a6fa7b30e2c13967b'
publicKeyFile = './pro_export_public_keys.txt'
pubKey2HashFile = './pubkey_to_hashs.txt'
pubKey2AddrFile = './pro_pubkey_to_address.txt'
