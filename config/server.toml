host = '0.0.0.0'
port = 3000
grpcPort = 3001
cron = '0 0 1 * *'
requestCert = true
rejectUnauthorized = true
ssl_ca = './ssl/ca.crt'
ssl_key = './ssl/wallet-test-1.sinnet.huobiidc.com.server.key'
ssl_cert = './ssl/wallet-test-1.sinnet.huobiidc.com.server.crt'

# config for aws
[[awsKeys]]
appid = 'validator'
appkey = '12345678'

# config for secret rsa decrypt
[rsaSecret]
password = 'huobitest'
privKey = './crypto/rsa_private_sample.pem'
pubKey = './crypto/rsa_public_sample.pem'

# config for signed tx rsa encrypt
[rsaSign]
password = 'huobitest'
privKey = './crypto/rsa_private_sample.pem'
pubKey = './crypto/rsa_public_sample.pem'

[ecdh]
pubKeyHash = './crypto/ecdh_public_key_hash'

# config for bunyan logger
[bunyan]
name = 'signer'
file = 'signer.log'
