version: 1.0.0
name: "payprotocol-mainnet-huobi"

client:
  organization: payprotocol

channels:
  payprotocol:
    orderers:
      - payprotocol-orderer
    peers:
      payprotocol-peer:
        endorsingPeer: true
        chaincodeQuery: true
        ledgerQuery: true
        eventSource: true

organizations:
  payprotocol:
    mspid: exchange
    peers:
      - payprotocol-peer:
    certificateAuthorities:
      - huobi-ca
orderers:
  payprotocol-orderer:
    url: grpc://*************:7050
    grpcOptions:
      ssl-target-name-override: payprotocol-orderer


peers:
  payprotocol-peer:
    url: grpc://*************:7051
    grpcOptions:
      ssl-target-name-override: payprotocol-peer
certificateAuthorities:
  huobi-ca:
    caName: exchange-huobi-ca
    url: https://localhost:7054
    tlsCACerts:
      path: tls-cert.pem



customConfig:
  adminUser: huobi
  adminPasswd: *****
  certificateAuthoritiesName: huobi-ca
  peersNname: payprotocol-peer
  orderersName: payprotocol-orderer
  organizationsName: payprotocol
  channelsName: payprotocol
