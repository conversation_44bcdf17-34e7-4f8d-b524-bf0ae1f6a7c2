version: 1.0.0
name: "huobi-sample"

client:
  organization: payprotocol

channels:
  payprotocol:
    orderers:
      - payprotocol-orderer
    peers:
      payprotocol-peer:
        endorsingPeer: true
        chaincodeQuery: true
        ledgerQuery: true
        eventSource: true

organizations:
  payprotocol:
    mspid: exchange
    peers:
      - payprotocol-peer:
    certificateAuthorities:
      - exchange-huobi-ca

orderers:
  payprotocol-orderer:
    url: grpc://orderer.payprotocol.kiesnet.io:7050
    grpcOptions:
      ssl-target-name-override: payprotocol-orderer


peers:
  payprotocol-peer:
    url: grpc://peer.payprotocol.kiesnet.io:7051
    grpcOptions:
      ssl-target-name-override: payprotocol-peer


certificateAuthorities:
  exchange-huobi-ca:
    caName: exchange-huobi-ca
    url: https://wallet-test-3.sinnet.huobiidc.com:7054
    tlsCACerts:
      path: tls-cert.pem

customConfig:
  adminUser: <PERSON><PERSON>i
  adminPasswd: Huobitest
  certificateAuthoritiesName: exchange-huobi-ca
  peersNname: payprotocol-peer
  orderersName: payprotocol-orderer
  organizationsName: payprotocol
  channelsName: payprotocol
